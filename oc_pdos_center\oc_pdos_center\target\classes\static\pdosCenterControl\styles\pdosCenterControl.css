/* 全局禁用滚动和拉伸 - 但允许表格区域滚动 */
* {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

html, body {
  overflow: hidden !important;
  overflow-x: hidden !important;
  overflow-y: hidden !important;
  width: 100%;
  max-width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

/* 禁用拖拽 */
* {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

/* 集团云网服务编排中心页面样式 */
.pdos-center-control {
  background: #f8fbfd;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'HarmonyOS Sans', 'PingFang SC', 'Helvetica Neue', 'Arial Rounded MT Bold', 'Arial', 'sans-serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.pdos-center-control .header {
    background: rgba(116, 190, 223, 0.608);
    padding: 10px 20px;
    font-size: 20px;
    font-weight: bold;
    color: #fff;
}

.query-form {
  background: #fff;
  margin: 20px;
  padding: 20px;
  border-radius: 6px;
  box-shadow: 0 2px 8px #e6f7ff;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-auto-rows: auto;
  gap: 10px 10px;
}

.form-item {
  display: flex;
  align-items: center;
}

.form-item label {
  width: 110px;
  margin-right: 6px;
  color: rgb(144, 144, 144);
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
  text-align: right;
  min-width: 70px;
}

.form-item input,
.form-item select {
  width: 150px;
  min-width: 0;
  max-width: 100%;
  box-sizing: border-box;
  padding: 4px 7px;
  font-size: 12px;
  border-radius: 5px;
  border: 1px solid #c2d3e7;
  color: #333;
  outline: none;
}

.form-item input:focus,
.form-item select:focus {
  border-color: rgb(22, 168, 204);
  box-shadow: 0 0 0 2px rgba(22, 168, 204, 0.2);
}

/* 输入框placeholder颜色 */
.form-item input::placeholder {
  color: #999;
}

/* 开始时间和结束时间输入框样式 */
.form-item input[type="datetime-local"] {
  width: 150px;
  color: #333;
  outline: none;
  padding-right: 30px;
  background: #fff;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* 隐藏浏览器默认的datetime-local图标 */
.form-item input[type="datetime-local"]::-webkit-calendar-picker-indicator {
  display: none;
}

.form-item input[type="datetime-local"]::-webkit-inner-spin-button,
.form-item input[type="datetime-local"]::-webkit-outer-spin-button {
  display: none;
}

/* 为datetime-local输入框的包装器添加自定义图标 */
.form-item .input-wrapper.datetime-wrapper::after {
  content: '';
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%23999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>') no-repeat center;
  background-size: 16px 16px;
  pointer-events: none;
  z-index: 1;
}

.form-item input[type="datetime-local"]:focus {
  border-color: rgb(22, 168, 204);
  box-shadow: 0 0 0 2px rgba(22, 168, 204, 0.2);
}

/* 结束时间字段特殊样式 */
.form-item.end-date-item {
  grid-column: 4 / 6;
  justify-content: flex-start;
}

.form-item.end-date-item .input-wrapper {
  flex: 1;
  max-width: 150px;
}

/* datetime-local输入框包装器样式 - 确保宽度一致 */
.form-item .input-wrapper input[type="datetime-local"] {
  width: 150px;
  box-sizing: border-box;
}

.form-actions {
  margin-top: 18px;
  display: flex;
  align-items: center;
  gap: 18px;
}

.form-actions .tip {
  color: #e14c4c;
  font-size: 13px;
  margin-right: 18px;
}

.form-actions .precise {
  margin-right: 12px;
}

.form-actions-center {
  justify-content: flex-start;
  position: relative;
}

.form-actions-center .tip {
  flex: 0 0 auto;
  margin-right: auto;
}

.form-actions-center .action-btns {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.action-btns {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 表格布局 */
.table-header, .table-body {
  table-layout: fixed;
  width: 100%;
  border-collapse: collapse;
}

/* 表头样式 - 白底黑字 */
th {
  position: relative;
  background: #fff !important;
  font-weight: 600;
  color: #222 !important;
  height: 35px;
  line-height: 35px;
  text-align: center;
  border: 1px solid #e0e0e0;
  font-size: 13px;
  padding: 0 5px;
  box-sizing: border-box;
  vertical-align: middle;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 表格内容样式 */
td {
  border: 1px solid #e0e0e0;
  text-align: center;
  font-size: 13px;
  height: 32px !important;
  line-height: 32px !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 5px;
  box-sizing: border-box;
  vertical-align: middle;
  max-height: 32px !important;
  min-height: 32px !important;
}

/* 强制表格行高度 */
.table-body tr {
  height: 32px !important;
  max-height: 32px !important;
  min-height: 32px !important;
}

/* 强制表格容器高度 */
.table-body {
  height: auto !important;
  max-height: none !important;
}

/* 确保表格内容不被拉伸 */
.table-body-wrapper .table-body {
  flex: none !important;
  height: auto !important;
}

/* 拖拽把手样式 */
.col-resize {
  position: absolute;
  right: 0;
  top: 0;
  width: 8px;
  height: 100%;
  cursor: col-resize;
  z-index: 2;
  background: transparent;
  transition: background 0.2s ease;
}

.col-resize:hover {
  background: #1677c7;
  opacity: 0.3;
}

.col-resize:active {
  background: #1677c7;
  opacity: 0.5;
}

/* 拖拽时的视觉反馈 */
.table-header th:hover .col-resize {
  background: #1677c7;
  opacity: 0.2;
}

/* 内容区样式 */
.table-body-wrapper {
  position: relative;
  flex: 1;
  background: #fff;
  overflow-x: auto;
  overflow-y: auto;
  min-height: 200px;
  display: flex;
  flex-direction: column;
}

/* 表格内容区域 */
.table-body-wrapper table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  flex-shrink: 0;
}

/* 滚动条容器 */
.table-body-wrapper::-webkit-scrollbar {
  height: 8px;
  position: relative;
  bottom: 0;
}

/* 确保滚动条在底部 */
.table-body-wrapper {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
  /* 确保滚动条在底部显示 */
  scrollbar-gutter: stable;
}

.table-body-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.table-body-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-body-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 表头包装器样式 */
.table-header-wrapper {
  overflow-x: auto;
  overflow-y: hidden;
  /* 隐藏表头滚动条 */
  scrollbar-width: none;
  -ms-overflow-style: none;
  flex-shrink: 0;
}

.table-header-wrapper::-webkit-scrollbar {
  display: none;
}

/* 表格容器样式 */
.table-section {
  flex: 1 1 auto;
  margin: 0 20px 0 20px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px #e6f7ff;
  padding: 10px;
  display: flex;
  flex-direction: column;
  min-height: 400px;
  max-height: calc(100vh - 300px);
}

/* 无数据提示样式 */
.no-data-tip {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: #999;
  font-size: 18px;
  z-index: 2;
  pointer-events: none;
  white-space: nowrap;
  display: none;
}

/* 输入框清除按钮 */
.input-wrapper {
  position: relative;
  display: inline-block;
}

.clear-btn {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #999;
  font-size: 16px;
  display: none;
  padding: 0 5px;
  z-index: 1;
}

.input-wrapper:hover .clear-btn {
  display: inline-block;
}

.clear-btn:hover {
  color: #666;
}

.form-item input {
  padding-right: 25px;
}

.form-item select {
  padding-right: 30px;
}

.form-item .input-wrapper select + .clear-btn {
  right: 20px;
}

/* 按钮样式 */
.action-btns button {
  background-color: rgb(45, 195, 232);
  color: white;
  border: 1px solid rgb(45, 195, 232);
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 8px;
}

.action-btns button:hover {
  background-color: rgb(45, 195, 232);
  border-color: rgb(45, 195, 232);
}

.action-btns button:active {
  background-color: rgb(45, 195, 232);
  border-color: rgb(45, 195, 232);
}

/* 必填星号 */
.required-star {
  color: #e14c4c;
  margin-right: 2px;
  font-size: 18px;
  font-family: inherit;
}

/* 页脚 */
.footer {
  margin: 0 20px 20px 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 10px;
  font-size: 13px;
}

/* 查询中悬浮框样式 */
.query-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.query-loading-box {
  background: white;
  padding: 30px 50px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  text-align: center;
  font-size: 16px;
  color: #333;
}

.query-loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #1677c7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 

/* 中央提示框样式 */
.center-tip-modal {
    position: fixed;
    top:50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 15px 25px;
    border-radius: 5px;
    font-size: 14px;
    z-index: 10000;
    max-width: 300px;
    text-align: center;
    word-wrap: break-word;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
} 