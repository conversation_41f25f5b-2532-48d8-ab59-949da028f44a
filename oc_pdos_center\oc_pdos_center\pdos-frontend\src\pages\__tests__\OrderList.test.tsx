import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import OrderList from '../OrderList';
import * as orderService from '../../services/orderService';

// Mock orderService
jest.mock('../../services/orderService');
const mockOrderService = orderService as jest.Mocked<typeof orderService>;

// Mock antd message
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    error: jest.fn(),
    success: jest.fn(),
    warning: jest.fn(),
  },
}));

// 测试数据
const mockOrderData = {
  code: 200,
  message: '成功',
  data: {
    data: [
      {
        id: 1,
        orderNo: 'ORDER001',
        productName: '5G专线产品',
        customerName: '测试客户1',
        orderStatus: 1,
        provinceName: '北京市',
        cityName: '东城区',
        createTime: '2024-01-15 10:00:00',
        updateTime: '2024-01-15 10:00:00',
      },
      {
        id: 2,
        orderNo: 'ORDER002',
        productName: '云专线产品',
        customerName: '测试客户2',
        orderStatus: 2,
        provinceName: '上海市',
        cityName: '黄浦区',
        createTime: '2024-01-16 11:00:00',
        updateTime: '2024-01-16 11:00:00',
      },
    ],
    pageNum: 1,
    pageSize: 10,
    total: 2,
    totalPages: 1,
    hasNext: false,
    hasPrevious: false,
    isFirst: true,
    isLast: true,
  },
  timestamp: '2024-01-20 10:00:00',
};

const mockProductData = {
  code: 200,
  message: '成功',
  data: [
    {
      id: 1,
      productCode: 'PROD001',
      productName: '5G专线产品',
      productType: 'NETWORK',
      status: 1,
      createTime: '2024-01-01 00:00:00',
      updateTime: '2024-01-01 00:00:00',
    },
    {
      id: 2,
      productCode: 'PROD002',
      productName: '云专线产品',
      productType: 'CLOUD',
      status: 1,
      createTime: '2024-01-01 00:00:00',
      updateTime: '2024-01-01 00:00:00',
    },
  ],
  timestamp: '2024-01-20 10:00:00',
};

// 包装组件以提供Router上下文
const renderWithRouter = (component: React.ReactElement) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe('OrderList Component', () => {
  beforeEach(() => {
    // 重置所有mock
    jest.clearAllMocks();
    
    // 设置默认的mock返回值
    mockOrderService.getProducts.mockResolvedValue(mockProductData);
    mockOrderService.queryOrderList.mockResolvedValue(mockOrderData);
  });

  /**
   * 测试组件正常渲染
   */
  test('renders OrderList component correctly', async () => {
    renderWithRouter(<OrderList />);
    
    // 检查页面标题
    expect(screen.getByText('订单列表')).toBeInTheDocument();
    
    // 检查搜索表单
    expect(screen.getByText('产品名称')).toBeInTheDocument();
    expect(screen.getByText('订单流水号')).toBeInTheDocument();
    expect(screen.getByText('客户订单号')).toBeInTheDocument();
    expect(screen.getByText('客户名称')).toBeInTheDocument();
    expect(screen.getByText('订单状态')).toBeInTheDocument();
    expect(screen.getByText('省份')).toBeInTheDocument();
    expect(screen.getByText('业务类型')).toBeInTheDocument();
    expect(screen.getByText('时间范围')).toBeInTheDocument();
    
    // 检查操作按钮
    expect(screen.getByText('查询')).toBeInTheDocument();
    expect(screen.getByText('重置')).toBeInTheDocument();
  });

  /**
   * 测试产品数据加载
   */
  test('loads product data on component mount', async () => {
    renderWithRouter(<OrderList />);
    
    // 等待产品数据加载
    await waitFor(() => {
      expect(mockOrderService.getProducts).toHaveBeenCalledTimes(1);
    });
  });

  /**
   * 测试搜索功能
   */
  test('performs search when query button is clicked', async () => {
    renderWithRouter(<OrderList />);
    
    // 等待组件加载完成
    await waitFor(() => {
      expect(mockOrderService.getProducts).toHaveBeenCalled();
    });
    
    // 填写搜索条件
    const orderNoInput = screen.getByPlaceholderText('请输入订单流水号');
    fireEvent.change(orderNoInput, { target: { value: 'ORDER001' } });
    
    // 点击查询按钮
    const queryButton = screen.getByText('查询');
    fireEvent.click(queryButton);
    
    // 验证API调用
    await waitFor(() => {
      expect(mockOrderService.queryOrderList).toHaveBeenCalledWith(
        expect.objectContaining({
          orderNo: 'ORDER001',
        })
      );
    });
  });

  /**
   * 测试重置功能
   */
  test('resets form when reset button is clicked', async () => {
    renderWithRouter(<OrderList />);
    
    // 等待组件加载完成
    await waitFor(() => {
      expect(mockOrderService.getProducts).toHaveBeenCalled();
    });
    
    // 填写搜索条件
    const orderNoInput = screen.getByPlaceholderText('请输入订单流水号');
    fireEvent.change(orderNoInput, { target: { value: 'ORDER001' } });
    
    // 点击重置按钮
    const resetButton = screen.getByText('重置');
    fireEvent.click(resetButton);
    
    // 验证表单已重置
    expect(orderNoInput).toHaveValue('');
  });

  /**
   * 测试订单数据显示
   */
  test('displays order data in table', async () => {
    renderWithRouter(<OrderList />);
    
    // 等待组件加载完成
    await waitFor(() => {
      expect(mockOrderService.getProducts).toHaveBeenCalled();
    });
    
    // 模拟查询操作
    const queryButton = screen.getByText('查询');
    fireEvent.click(queryButton);
    
    // 等待数据加载并显示
    await waitFor(() => {
      expect(screen.getByText('ORDER001')).toBeInTheDocument();
      expect(screen.getByText('ORDER002')).toBeInTheDocument();
      expect(screen.getByText('5G专线产品')).toBeInTheDocument();
      expect(screen.getByText('云专线产品')).toBeInTheDocument();
      expect(screen.getByText('测试客户1')).toBeInTheDocument();
      expect(screen.getByText('测试客户2')).toBeInTheDocument();
    });
  });

  /**
   * 测试分页功能
   */
  test('handles pagination correctly', async () => {
    renderWithRouter(<OrderList />);
    
    // 等待组件加载完成
    await waitFor(() => {
      expect(mockOrderService.getProducts).toHaveBeenCalled();
    });
    
    // 模拟查询操作
    const queryButton = screen.getByText('查询');
    fireEvent.click(queryButton);
    
    // 等待数据加载
    await waitFor(() => {
      expect(mockOrderService.queryOrderList).toHaveBeenCalled();
    });
    
    // 检查分页组件是否存在
    const pagination = screen.getByRole('navigation');
    expect(pagination).toBeInTheDocument();
  });

  /**
   * 测试订单状态显示
   */
  test('displays order status correctly', async () => {
    renderWithRouter(<OrderList />);
    
    // 等待组件加载完成
    await waitFor(() => {
      expect(mockOrderService.getProducts).toHaveBeenCalled();
    });
    
    // 模拟查询操作
    const queryButton = screen.getByText('查询');
    fireEvent.click(queryButton);
    
    // 等待数据加载并检查状态显示
    await waitFor(() => {
      expect(screen.getByText('处理中')).toBeInTheDocument(); // 状态1
      expect(screen.getByText('已完成')).toBeInTheDocument(); // 状态2
    });
  });

  /**
   * 测试查看详情功能
   */
  test('navigates to detail page when detail button is clicked', async () => {
    renderWithRouter(<OrderList />);
    
    // 等待组件加载完成
    await waitFor(() => {
      expect(mockOrderService.getProducts).toHaveBeenCalled();
    });
    
    // 模拟查询操作
    const queryButton = screen.getByText('查询');
    fireEvent.click(queryButton);
    
    // 等待数据加载
    await waitFor(() => {
      expect(mockOrderService.queryOrderList).toHaveBeenCalled();
    });
    
    // 点击查看详情按钮
    const detailButtons = screen.getAllByText('查看详情');
    expect(detailButtons.length).toBeGreaterThan(0);
    
    fireEvent.click(detailButtons[0]);
    
    // 验证页面跳转（这里需要根据实际的路由实现来验证）
    // 由于使用了BrowserRouter，可以检查URL变化或其他导航相关的行为
  });

  /**
   * 测试错误处理
   */
  test('handles API errors gracefully', async () => {
    // Mock API错误
    mockOrderService.queryOrderList.mockRejectedValue(new Error('API Error'));
    
    renderWithRouter(<OrderList />);
    
    // 等待组件加载完成
    await waitFor(() => {
      expect(mockOrderService.getProducts).toHaveBeenCalled();
    });
    
    // 模拟查询操作
    const queryButton = screen.getByText('查询');
    fireEvent.click(queryButton);
    
    // 等待错误处理
    await waitFor(() => {
      expect(mockOrderService.queryOrderList).toHaveBeenCalled();
    });
    
    // 验证错误消息显示（根据实际的错误处理实现）
    // 这里可能需要检查错误提示或其他错误状态的显示
  });

  /**
   * 测试加载状态
   */
  test('shows loading state during data fetch', async () => {
    // Mock延迟的API响应
    mockOrderService.queryOrderList.mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve(mockOrderData), 1000))
    );
    
    renderWithRouter(<OrderList />);
    
    // 等待组件加载完成
    await waitFor(() => {
      expect(mockOrderService.getProducts).toHaveBeenCalled();
    });
    
    // 模拟查询操作
    const queryButton = screen.getByText('查询');
    fireEvent.click(queryButton);
    
    // 检查加载状态（根据实际的加载状态实现）
    // 这里可能需要检查loading spinner或其他加载指示器
  });

  /**
   * 测试表单验证
   */
  test('validates form inputs correctly', async () => {
    renderWithRouter(<OrderList />);
    
    // 等待组件加载完成
    await waitFor(() => {
      expect(mockOrderService.getProducts).toHaveBeenCalled();
    });
    
    // 不填写任何条件直接查询
    const queryButton = screen.getByText('查询');
    fireEvent.click(queryButton);
    
    // 验证是否有适当的验证提示
    // 这里需要根据实际的表单验证实现来检查
  });
});