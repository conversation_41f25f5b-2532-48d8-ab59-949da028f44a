-------------------------------------------------------------------------------
Test set: com.iwhalecloud.pdos.integration.PdosIntegrationTest
-------------------------------------------------------------------------------
Tests run: 8, Failures: 4, Errors: 0, Skipped: 0, Time elapsed: 20.50 s <<< FAILURE! -- in com.iwhalecloud.pdos.integration.PdosIntegrationTest
com.iwhalecloud.pdos.integration.PdosIntegrationTest.testCorsConfiguration -- Time elapsed: 0.077 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:59)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:122)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:637)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.iwhalecloud.pdos.integration.PdosIntegrationTest.testCorsConfiguration(PdosIntegrationTest.java:157)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

com.iwhalecloud.pdos.integration.PdosIntegrationTest.testCompleteOrderQueryFlow -- Time elapsed: 0.266 s <<< FAILURE!
java.lang.AssertionError: JSON path "$.code" expected:<0> but was:<-1>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:59)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:122)
	at org.springframework.test.util.JsonPathExpectationsHelper.assertValue(JsonPathExpectationsHelper.java:123)
	at org.springframework.test.web.servlet.result.JsonPathResultMatchers.lambda$value$2(JsonPathResultMatchers.java:111)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.iwhalecloud.pdos.integration.PdosIntegrationTest.testCompleteOrderQueryFlow(PdosIntegrationTest.java:61)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

com.iwhalecloud.pdos.integration.PdosIntegrationTest.testPagination -- Time elapsed: 0.042 s <<< FAILURE!
java.lang.AssertionError: JSON path "$.code" expected:<0> but was:<-1>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:59)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:122)
	at org.springframework.test.util.JsonPathExpectationsHelper.assertValue(JsonPathExpectationsHelper.java:123)
	at org.springframework.test.web.servlet.result.JsonPathResultMatchers.lambda$value$2(JsonPathResultMatchers.java:111)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.iwhalecloud.pdos.integration.PdosIntegrationTest.testPagination(PdosIntegrationTest.java:134)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

com.iwhalecloud.pdos.integration.PdosIntegrationTest.testPerformanceWithLargeDataset -- Time elapsed: 0.052 s <<< FAILURE!
java.lang.AssertionError: JSON path "$.code" expected:<0> but was:<-1>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:59)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:122)
	at org.springframework.test.util.JsonPathExpectationsHelper.assertValue(JsonPathExpectationsHelper.java:123)
	at org.springframework.test.web.servlet.result.JsonPathResultMatchers.lambda$value$2(JsonPathResultMatchers.java:111)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.iwhalecloud.pdos.integration.PdosIntegrationTest.testPerformanceWithLargeDataset(PdosIntegrationTest.java:191)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

