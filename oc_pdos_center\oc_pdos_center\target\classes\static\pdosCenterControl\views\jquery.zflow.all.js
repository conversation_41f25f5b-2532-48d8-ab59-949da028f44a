//=========zflow===========
(function ($) {
    function IwhalecloudFlow() {
        var flow = {};
        //流程中节点集合，map结构
        flow.activities = {};
        //流程中线条集合，map结构
        flow.transitions = {};
        //控制线的临时变量，用于避免画控制线时线条重合
        flow.controlLines = [];
        flow.constant = {
            MODE: {
                INST: 'inst',
                DEF: 'def'
            },
            DIRECTION: {
                H: 'horizontal',
                V: 'vertical'
            },
            LAYOUT:{
                T: 'top',
                L: 'left',
                B: 'button',
                R: 'right'
            }
        };
        flow.color = {
            blue:"#7185B7", // 蓝色
            orange:"#e67e22", // 橙色
            white:"#ffffff", // 白色
            green:"#2ecc71", // 绿色
            purple:"#9b59b6", // 紫色
            yellow:"#F1CD55", // 黄色
            red:"#ff0000", // 红色
            black:"#34495e", // 黑色
            gray:"#95a5a6",	//灰色
            cyan:"#9FE0F6",//青色
            greenCyan:"#98FF72",//青绿色
            brown:"#250D03",//棕色
            moreBlue:"#253074",//深蓝色
            litPink:"#EDD5BD",//淡粉色
            moreGray:"#bdc3c7",//深灰色
            litGray:"#95a5a6",//浅灰色
        };
        flow.options = {
            autsize: true, //自动计算节点和分支间隔
            offsetX: 100,//整个流程图的水平偏移
            offsetY: 100,//整个流程图的垂直偏移
            branchSize:80,//分支间隔
            nodeSpace:100,//节点间隔
            imageSize: 24,//图片大小
            textSpace:5,//图片与文本间的间隔
            textLineWords:5,//单行文本字数
            textLineSpace:5,//行间隔
            textLayout:"",//文本布局方向
            linePos: 2/4,//线条偏移量百分比
            lineArrorEnd: 'classic-wide-long', //线条结束端箭头
            fontSize:12//文字大小
        };
        flow.options.color = {
            init: flow.color.gray,
            finish:flow.color.blue
        };
        //状态编码：状态描述，图标名,水平/垂直布局旋转角度,是否图例
        flow.options.state = {
            "": {name:"未开始", url:"tache_init.png", angle:{vertical:"R0",horizontal:"R0"}},
            "10D": {name:"已派发", url:"tache_doing.png", angle:{vertical:"R0",horizontal:"R0"}, legend:false},
            "10I": {name:"正常处理", url:"tache_doing.png", angle:{vertical:"R0",horizontal:"R0"}, legend:true},
            "1TD": {name:"转派处理中", url:"tache_doing.png", angle:{vertical:"R0",horizontal:"R0"}, legend:false},
            "10F": {name:"处理完成", url:"tache_finish.png", angle:{vertical:"R0",horizontal:"R0"}, legend:true},
            "10A": {name:"已归档", url:"tache_finish.png", angle:{vertical:"R0",horizontal:"R0"}, legend:false},
            "10E": {name:"执行异常", url:"tache_error.png", angle:{vertical:"R0",horizontal:"R0"}, legend:true},
            "10X": {name:"作废", url:"tache_cancel.png", angle:{vertical:"R0",horizontal:"R0"}, legend:true},
            "10C": {name:"已撤单", url:"tache_cancel.png", angle:{vertical:"R0",horizontal:"R0"}, legend:false}
        };
        //节点图标：描述，图标名,水平/垂直布局旋转角度
        flow.options.icon = {
            "Tache": {name:"环节节点", url:"node_tache.png", angle:{vertical:"R0",horizontal:"R0"}},
            "Start": {name:"开始节点", url:"node_start.png", angle:{vertical:"R0",horizontal:"R0"}},
            "Parallel": {name:"开节点", url:"node_parallel.png", angle:{vertical:"R90",horizontal:"R0"}},
            "Relation": {name:"合节点", url:"node_relation.png", angle:{vertical:"R0",horizontal:"R0"}},
            "Control": {name:"控制节点", url:"node_control.png", angle:{vertical:"R0",horizontal:"R0"}},
            "Finish": {name:"结束节点", url:"node_finish.png", angle:{vertical:"R0",horizontal:"R0"}}
        };
        flow.options.iconPath='../imgs/';
        //----------activity-------start--------
        flow.Activity = function (activityXML, parent) {
            //===========节点属性========start================
            this.ele = $(activityXML);
            this.parent = parent;
            this.id = this.ele.attr("id");
            this.defId = this.ele.attr("defId");
            this.name = this.ele.attr("name");
            this.tacheId = this.ele.attr("tacheId");
            this.tacheCode = this.ele.attr("tacheCode");
            this.areaId = this.ele.attr("areaId");
            this.isRunning = this.ele.attr("isRunning");
            this.type = this.ele.attr("type");
            this.subType = this.ele.attr("subType");
            this.isParallel = this.ele.attr("isParallel");
            this.isJoin = this.ele.attr("isJoin");
            this.flowTips = this.ele.attr("flowTips");
            this.state = this.ele.attr("state");
            this.isTimeOut = this.ele.attr("isTimeOut");
            this.workOrderId = this.ele.attr("workOrderId");
            this.direction = this.ele.attr("direction");
            this.workItemId = this.ele.attr("workItemId");
            this.inLines = flow.ele.find('Transition[to="' + this.id + '"]');
            this.outLines = flow.ele.find('Transition[from="' + this.id + '"]');
            this.node = new flow.node(this);
            this.x = 0;//x坐标
            this.y = 0;//y坐标
            this.color = flow.options.color.init;
            this.realActivity = this;
            flow.activities[this.id] = this;
            //===========节点属性========end================
            this.size = function(){
                return { 
                    width:this.hide && flow.options.direction == flow.constant.DIRECTION.H ? 0 : this.node.width,
                    height:this.hide && flow.options.direction == flow.constant.DIRECTION.V ? 0 : this.node.height 
                };
            }
            //更新坐标
            this.updateData = function (x, y) {
                this.x = x;
                this.y = y;
                switch(this.type){
                    case 'Start':
                    case 'Finish':{
                        this.color = flow.options.color.finish;
                        break;
                    }
                    case 'Tache':{
                        this.color = this.state == '10F' ? flow.options.color.finish : this.color;
                        break;
                    }
                    case 'Parallel':
                    case 'Control':{
                        this.color = this.preNode ? this.preNode.color : this.parent.preNode.color;
                        break;
                    }
                    case 'Relation':{
                        var allOK = true;
                        for(var i = 0; i < this.inLines.length; i++) {
                            var line = flow.transitions[$(this.inLines[i]).attr('id')];
                            if(line.lineType == 'Control'){ continue; }
                            if(line.startActivity.color == this.color){
                                allOK = false;
                                break;
                            }
                        }
                        if(allOK){ this.color = flow.options.color.finish; }
                        break;
                    }
                }
                if(flow.options.reduce){
                    switch(this.type){
                        case "Control":
                        case "Parallel":
                        case "Relation": {
                            this.node.hide = true;
                            var inActivity = this.getSingleInActivity();
                            if(inActivity){
                                if(flow.options.direction == flow.constant.DIRECTION.H){
                                    if(this.y == inActivity.y){
                                        this.x = inActivity.x;
                                        this.hide = true;
                                        this.realActivity = inActivity.realActivity;
                                    }
                                }
                                if(flow.options.direction == flow.constant.DIRECTION.V){
                                    if(this.x == inActivity.x){
                                        this.y = inActivity.y;
                                        this.hide = true;
                                        this.realActivity = inActivity.realActivity;
                                    }
                                }
                            }
                            break;
                        }
                        case "Finish":
                        case "Tache":{
                            var inActivity = this.getSingleInActivity();
                            if(inActivity && inActivity.realActivity){ inActivity = inActivity.realActivity; }
                            if(inActivity && this.preNode){
                                if(flow.options.direction == flow.constant.DIRECTION.H){
                                    if(this.y == inActivity.y && inActivity.node.hide && this.preNode.outLines.length == 1){
                                        this.x = inActivity.x;
                                        inActivity.hide = true;
                                        inActivity.realActivity = this;
                                    }
                                }
                                if(flow.options.direction == flow.constant.DIRECTION.V){
                                    if(this.x == inActivity.x && inActivity.node.hide && this.preNode.outLines.length == 1){
                                        this.y = inActivity.y;
                                        inActivity.hide = true;
                                        inActivity.realActivity = this;
                                    }
                                }
                            }
                            break;
                        }
                    }
                }
            }
            this.getSingleInActivity = function(){
                var activities = [];
                //根据流程图找上一层Activity
                var lastActivity;
                for(var i = this.index - 1; i >= 0; i--) {
                    //todo 后续这里可以考虑严谨一下，如果上个是Parallel节点，应取其结构中最后一个Activity
                    lastActivity = flow.root.nodes[i];
                    if (lastActivity.id) {
                        break;
                    }
                }
                if(this.inLines.length == 1){
                    //根据线条找上一个from节点
                    var traActivity = flow.activities[$(this.inLines[0]).attr('from')];
                    //如果流程图的紧邻上个Activity水平坐标大于from节点的，说明当前节点与其from节点中间流程有跳过节点，应取最大水平坐标作为上个节点的水平坐标
                    //否则将造成下个Tache节点计算坐标与此处紧邻上个Tache坐标重合
                    if (lastActivity && lastActivity.x > traActivity.x) {
                        activities.push(lastActivity);
                    } else {
                        activities.push(traActivity);
                    }
                }else {
                    for(var i = 0; i < this.inLines.length; i++) {
                        var activity = flow.activities[$(this.inLines[i]).attr('from')];
                        //if(activity.type != 'Control'){ activities.push(activity); }
                        if((activity.type == 'Control' && activity.x != 0) || activity.type != 'Control'){ activities.push(activity); }
                    }
                }
                return activities.length == 1 ? activities[0] : null;
            }
            //获取下一个节点的水平坐标
            this.getNextX = function () {
                return this.x + this.node.width;
            }
            //获取下一个节点的垂直坐标
            this.getNextY = function () {
                return this.y + this.node.height;
            }
            //绘制方法
            this.paint = function (paper) {
                this.node.reset(paper, this.x, this.y);
                if(this.type == 'Tache' && this.node.image && flow.options.callback){
                    flow.options.callback(this);
                }
                if(this.type == 'Tache' && this.node.image && flow.options.click){
                    this.node.image.click(function(){
                        flow.options.click(this);
                    }.bind(this));
                }
                if(this.type == 'Tache' && this.node.image && flow.options.mouseover){
                    this.node.image.mouseover(function(){
                        flow.options.mouseover(this);
                    }.bind(this));
                }
                if(this.type == 'Tache' && this.node.image && flow.options.mouseout){
                    this.node.image.mouseout(function(){
                        flow.options.mouseout(this);
                    }.bind(this));
                }
            }
        };
        //----------activity--------end-------

        //----------Parallel--------start-------
        flow.Parallel = function (parallelXML, parent) {
            this.ele = $(parallelXML);
            this.parent = parent;
            this.x = 0;//x坐标
            this.y = 0;//y坐标
            this.blankBranch = null;//空分支
            this.branches = new Array();//分支数组
            //处理各分支上的节点
            var childrens = this.ele.children();
            for(var i = 0; i < childrens.length; i++) {
                var branch = new flow.Branch(childrens[i], this);
                this.branches.push(branch);
                if(branch.nodes.length == 0){
                    this.blankBranch = branch;
                }
            }
            this.size = function(){
                var size = {width:0, height:0};
                for(var i = 0; i < this.branches.length; i++) {
                    var branch = this.branches[i].size();
                    if(flow.options.direction == flow.constant.DIRECTION.H) {
                        //横向布局：高=子节点最大的高，宽=所有子节点宽之和
                        size.width = size.width < branch.width ? branch.width : size.width;
                        size.height = size.height + branch.height;
                    }else if (flow.options.direction == flow.constant.DIRECTION.V) {
                        //纵向布局：高=所有子节点高之和，宽=子节点最大的宽
                        size.width = size.width + branch.width;
                        size.height = size.height < branch.height ? branch.height : size.height;
                    }
                }
                return size;
            }
            //更新坐标
            this.updateData = function (x, y) {
                this.x = x;
                this.y = y;
                var sum = 0;
                var size = this.size();
                for(var i = 0; i < this.branches.length; i++) {
                    var branch = this.branches[i];
                    var branchSize = branch.size();
                    if(flow.options.direction == flow.constant.DIRECTION.H) {
                        sum = i == 0 ? this.y : sum;
                        branch.updateData(this.x, sum);
                        sum += branchSize.height;
                    }else if (flow.options.direction == flow.constant.DIRECTION.V) {
                        sum = i == 0 ? this.x : sum;
                        branch.updateData(sum, this.y);
                        sum += branchSize.width;
                    }
                }
            }
            //获取下一个节点的水平坐标
            this.getNextX = function () {
                return this.x + this.size().width;
            }
            //获取下一个节点的垂直坐标
            this.getNextY = function () {
                return this.y + this.size().height;
            }
            //绘制方法
            this.paint = function (paper) {
                for(var i = 0; i < this.branches.length; i++) {
                    this.branches[i].paint(paper);
                }
            }
        };
        //----------Parallel--------end-------

        //----------Branch--------end-------
        flow.Branch = function(branchXML, parent){
            this.ele = $(branchXML);
            this.parent = parent;
            this.x = 0;//x坐标
            this.y = 0;//y坐标
            this.nodes = new Array();
            var preNode;
            var childrens = this.ele.children();
            for(var i = 0; i < childrens.length; i++) {
                var childNode = childrens[i];
                var node;
                switch(childNode.nodeName) {
                    case "Activity": {
                        node = new flow.Activity(childNode, this.parent);
                        break;
                    }
                    case "Parallel": {
                        node = new flow.Parallel(childNode, this.parent);
                        break;
                    }
                }
                node.index = i;
                if(preNode){
                    preNode.nextNode = node;
                }
                node.preNode = preNode;
                preNode = node;
                this.nodes.push(node);
            }
            this.size = function(){
                var size = {width:0, height:0};
                for(var i = 0; i < this.nodes.length; i++) {
                    var node = this.nodes[i].size();
                    //计算分支的宽和高
                    if(flow.options.direction == flow.constant.DIRECTION.H) {
                        //横向布局：高=子节点最大的高，宽=所有子节点宽之和
                        size.width = size.width + node.width;
                        size.height = size.height < node.height ? node.height : size.height;
                    }else if (flow.options.direction == flow.constant.DIRECTION.V) {
                        //纵向布局：高=所有子节点高之和，宽=子节点最大的宽
                        size.width = size.width < node.width ? node.width : size.width;
                        size.height = size.height + node.height;
                    }
                }
                if(this.nodes.length == 0) {
                    if(flow.options.direction == flow.constant.DIRECTION.H) {
                        size.height = flow.options.branchSize;
                    }else if (flow.options.direction == flow.constant.DIRECTION.V) {
                        size.width = flow.options.branchSize;
                    }
                }
                return size;
            }
            //更新坐标
            this.updateData = function (x, y) {
                this.x = x;
                this.y = y;
                var size = this.size();
                var controlLines={};//控制线，主要用于更新控制线偏移量
                for(var i = 0; i < this.nodes.length; i++) {
                    if (flow.options.direction == flow.constant.DIRECTION.H) {
                        this.nodes[i].updateData(i > 0 ? this.nodes[i - 1].getNextX() : this.x, this.y + size.height/2 - this.nodes[i].size().height/2);
                    } else if (flow.options.direction == flow.constant.DIRECTION.V) {
                        this.nodes[i].updateData(this.x + size.width/2 - this.nodes[i].size().width/2, i > 0 ? this.nodes[i - 1].getNextY() : this.y);
                    }
                    if(this.nodes[i].type == 'Control') {
                        for(var j = 0; j < this.nodes[i].outLines.length; j++) {
                            var line = flow.transitions[$(this.nodes[i].outLines[j]).attr('id')];
                            if(line.lineType == 'Control') {
                                var count = Math.abs(line.startActivity.index - line.endActivity.index);
                                if(!controlLines[count]){ controlLines[count]=[] }
                                controlLines[count].push(line);
                            }
                        }
                    }
                }
                for(var key in controlLines) {
                    for(var i = 0; i < controlLines[key].length; i++) {
                        controlLines[key][i].updateLinePos();
                    }
                }
            }
            //绘制方法
            this.paint = function (paper) {
                for(var i = 0; i < this.nodes.length; i++) {
                    this.nodes[i].paint(paper);
                }
            }
        }
        //----------Branch--------end-------

        //----------Transition--------start-------
        flow.Transition = function (transitionXML) {
            //===========线条属性======start==========
            this.ele = $(transitionXML);
            this.id = this.ele.attr("id");
            this.name = this.ele.attr("name");
            this.from = this.ele.attr("from");
            this.to = this.ele.attr("to");
            this.isRunning = this.ele.attr("isRunning");
            this.direction = this.ele.attr("direction");
            this.lineType = this.ele.attr("lineType");
            this.startActivity = flow.activities[this.from];
            this.endActivity = flow.activities[this.to];
            this.lineSpace = 5;//线条和节点间隔
            this.lineSize = flow.options.nodeSpace - this.lineSpace * 2;//线条直线长度
            this.linePos = this.lineSize * flow.options.linePos;//线条偏移量
            if(this.startActivity == null) {
                throw new Error(this.id + "的startActivity不能为空");
            }
            if(this.endActivity == null) {
                throw new Error(this.id + "的endActivity不能为空");
            }
            if(this.ele.attr("lineType") == "控制线条") {
                this.lineType = "Control";
            }
            if(this.startActivity.outLines.length < 2 && this.lineType == 'Control'){
                this.lineType = 'Normal'; //如果控制线条只有一条，则按照普通线条画线
            }
            flow.transitions[this.id] = this;
            //===========线条属性======end==========
            //更新控制线的偏移量
            this.updateLinePos = function(){
                if(this.lineType == 'Control'){
                    var offset = 0;
                    var space = 5;
                    var startNode = this.startActivity;
                    var endNode = this.endActivity;
                    if(startNode.index > endNode.index){
                        startNode = this.endActivity;
                        endNode = this.startActivity;
                    }
                    //获取最小偏移量
                    do {
                        var size = startNode.size();
                        if (flow.options.direction == flow.constant.DIRECTION.H) {
                            offset = offset < size.height/2 ? size.height/2 : offset;
                        } else if (flow.options.direction == flow.constant.DIRECTION.V) {
                            offset = offset < size.width/2 ? size.width/2 : offset;
                        }
                        startNode = startNode.nextNode;
                    }
                    while (startNode.id != endNode.id);
                    //计算额外偏移量，避免重合
                    while (this.isOverlay(offset)) {
                        offset += space;
                    }
                    this.linePos = offset;
                }
            }
            //判断线条是否与其他有重合
            this.isOverlay = function(offset){
                if (flow.options.direction == flow.constant.DIRECTION.H) {
                    var y = this.startActivity.y - offset;
                    var x1 = Math.min(this.startActivity.x,this.endActivity.x);
                    var x2 = Math.max(this.startActivity.x,this.endActivity.x);
                    for(var i = 0; i < flow.controlLines.length; i++) {
                        if(y == flow.controlLines[i].y 
                            &&((x1 > flow.controlLines[i].x1 && x1 < flow.controlLines[i].x2)
                            || (x2 > flow.controlLines[i].x1 && x2 < flow.controlLines[i].x2)
                            || (flow.controlLines[i].x1 > x1 && flow.controlLines[i].x1 < x2)
                            || (flow.controlLines[i].x2 > x1 && flow.controlLines[i].x2 < x2))
                        ) { return true; }
                    }
                    flow.controlLines.push({y:y,x1:x1,x2:x2,id:this.id});
                } else if (flow.options.direction == flow.constant.DIRECTION.V) {
                    var x = this.startActivity.x - offset;
                    var y1 = Math.min(this.startActivity.y,this.endActivity.y);
                    var y2 = Math.max(this.startActivity.y,this.endActivity.y);
                    for(var i = 0; i < flow.controlLines.length; i++) {
                        if(x == flow.controlLines[i].x 
                            &&((y1 > flow.controlLines[i].y1 && y1 < flow.controlLines[i].y2)
                            || (y2 > flow.controlLines[i].y1 && y2 < flow.controlLines[i].y2)
                            || (flow.controlLines[i].y1 > y1 && flow.controlLines[i].y1 < y2)
                            || (flow.controlLines[i].y2 > y1 && flow.controlLines[i].y2 < y2))
                        ) { return true; }
                    }
                    flow.controlLines.push({x:x,y1:y1,y2:y2,id:this.id});
                }
                return false;
            }
            //绘制方法
            this.paint = function (paper, tTimes) {
                if (this.startActivity.node.x == this.endActivity.node.x && this.startActivity.node.y == this.endActivity.node.y) {
                    //如果开始节点和结束节点坐标相同，则不画线
                    return;
                }
                var col = this.startActivity.realActivity.color;
                //var col = this.endActivity.type == 'Relation' && this.startActivity.type != 'Parallel' ? this.endActivity.color : this.startActivity.color;
                if(flow.options.direction == flow.constant.DIRECTION.H) {
                    this.startX = this.startActivity.node.x + this.startActivity.node.imageSize + this.lineSpace;
                    this.startY = this.startActivity.node.y + this.startActivity.node.imageSize/2;
                    this.endX = this.endActivity.node.x - this.lineSpace;
                    this.endY = this.endActivity.node.y + this.endActivity.node.imageSize/2;
                    /**每条线，除了开始点和结束点，中间均有4个点
                    *1.普通直线：
                    *  ——1/2——3/4——
                    *2.普通折线：
                    *  ——1/2
                    *      |
                    *      3/4——
                    *3.空分支线：
                    *  ——1  4——
                    *    |  |
                    *    2——3
                    *4.控制线：
                    *    1/2——3/4
                    *    |      |
                    **/
                    //普通线条
                    this.midX1 = this.endX - (this.startY == this.endY ? 0 : this.linePos);
                    this.midY1 = this.startY;
                    this.midX2 = this.midX1;
                    this.midY2 = this.midY1;
                    this.midX3 = this.endX - (this.startY == this.endY ? 0 : this.linePos);
                    this.midY3 = this.endY;
                    this.midX4 = this.midX3;
                    this.midY4 = this.midY3;
                    if(this.startActivity.type == 'Parallel' && this.endActivity.type == 'Relation'){
                        //空分支线条
                        var branch = this.startActivity.nextNode.blankBranch;
                        if(branch){
                            this.midX1 = this.startX + this.lineSize - this.linePos;
                            this.midY1 = this.startY;
                            this.midX2 = this.midX1;
                            this.midY2 = branch.y + flow.options.imageSize/2;
                            this.midX3 = this.endX - this.linePos;
                            this.midY3 = this.midY2;
                            this.midX4 = this.midX3;
                            this.midY4 = this.endY;
                        }
                    }
                    if(this.endActivity.node.hide && !this.endActivity.hide){
                        this.endX = this.endX + this.endActivity.node.imageSize + this.lineSpace * 2;
                    }
                    if(flow.options.textLayout === flow.constant.LAYOUT.R && !this.startActivity.realActivity.node.hide){
                        this.startX = this.startX + this.startActivity.realActivity.node.text.width + this.lineSpace;
                    }
                    if(flow.options.textLayout === flow.constant.LAYOUT.L && !this.endActivity.realActivity.node.hide){
                        this.endX = this.endX - this.endActivity.realActivity.node.text.width - this.lineSpace;
                    }
                    if(this.lineType == 'Control') {
                        this.startX = this.startActivity.node.x + this.startActivity.node.imageSize/2;
                        this.startY = this.startActivity.node.y - this.lineSpace;
                        this.endX = this.endActivity.node.x + this.endActivity.node.imageSize/2;
                        this.endY = this.endActivity.node.y - this.lineSpace;

                        this.midX1 = this.startX;
                        this.midY1 = this.startY - this.linePos;
                        this.midX2 = this.midX1;
                        this.midY2 = this.midY1;
                        this.midX3 = this.endX;
                        this.midY3 = this.endY - this.linePos;
                        this.midX4 = this.midX3;
                        this.midY4 = this.midY3;
                        if(this.startActivity.node.hide && (!this.startActivity.realActivity || (this.startActivity.realActivity.type != 'Tache' && this.startActivity.realActivity.type != 'Start'))){
                            this.startY = this.startY + this.startActivity.node.imageSize/2;
                        }
                        if(this.endActivity.node.hide && (!this.endActivity.realActivity || (this.endActivity.realActivity.type != 'Tache' && this.endActivity.realActivity.type != 'Start'))){
                            this.endY = this.endY + this.endActivity.node.imageSize/2;
                        }
                        if(flow.options.textLayout === flow.constant.LAYOUT.T && !this.startActivity.realActivity.node.hide){
                            this.startY = this.startY - this.startActivity.realActivity.node.text.height - this.lineSpace;
                        }
                        if(flow.options.textLayout === flow.constant.LAYOUT.T && !this.endActivity.realActivity.node.hide){
                            this.endY = this.endY - this.endActivity.realActivity.node.text.height - this.lineSpace;
                        }
                    }
                }else if (flow.options.direction == flow.constant.DIRECTION.V) {
                    this.startX = this.startActivity.node.x + this.startActivity.node.imageSize/2;
                    this.startY = this.startActivity.node.y + this.startActivity.node.imageSize + this.lineSpace;
                    this.endX = this.endActivity.node.x + this.endActivity.node.imageSize/2;
                    this.endY = this.endActivity.node.y - this.lineSpace;
                    //普通线条
                    this.midX1 = this.startX;
                    this.midY1 = this.endY - (this.startX == this.endX ? 0 : this.linePos);
                    this.midX2 = this.midX1;
                    this.midY2 = this.midY1;
                    this.midX3 = this.endX;
                    this.midY3 = this.endY - (this.startX == this.endX ? 0 : this.linePos);
                    this.midX4 = this.midX3;
                    this.midY4 = this.midY3;
                    if(this.startActivity.type == 'Parallel' && this.endActivity.type == 'Relation'){
                        //空分支线条
                        var branch = this.startActivity.nextNode.blankBranch;
                        if(branch){
                            this.midX1 = this.startX;
                            this.midY1 = this.startY + this.lineSize - this.linePos;
                            this.midX2 = branch.x + flow.options.imageSize/2;
                            this.midY2 = this.midY1;
                            this.midX3 = this.midX2;
                            this.midY3 = this.endY - this.linePos;
                            this.midX4 = this.endX;
                            this.midY4 = this.midY3;
                        }
                    }
                    if(this.endActivity.node.hide && !this.endActivity.hide){
                        this.endY = this.endY + this.endActivity.node.imageSize + this.lineSpace * 2;
                    }
                    if(flow.options.textLayout === flow.constant.LAYOUT.B){
                        this.startY = this.startY + this.startActivity.realActivity.node.text.height + this.lineSpace;
                    }
                    if(flow.options.textLayout === flow.constant.LAYOUT.T){
                        this.endY = this.endY - this.endActivity.realActivity.node.text.height - this.lineSpace;
                    }
                    if(this.lineType == 'Control') {
                        this.startX = this.startActivity.node.x - this.lineSpace;
                        this.startY = this.startActivity.node.y + this.startActivity.node.imageSize/2;
                        this.endX = this.endActivity.node.x - this.lineSpace;
                        this.endY = this.endActivity.node.y + this.endActivity.node.imageSize/2;

                        this.midX1 = this.startX - this.linePos;
                        this.midY1 = this.startY;
                        this.midX2 = this.midX1;
                        this.midY2 = this.midY1;
                        this.midX3 = this.endX - this.linePos;
                        this.midY3 = this.endY;
                        this.midX4 = this.midX3;
                        this.midY4 = this.midY3;
                        if(this.startActivity.node.hide && (!this.startActivity.realActivity || (this.startActivity.realActivity.type != 'Tache' && this.startActivity.realActivity.type != 'Start'))){
                            this.startX = this.startX + this.endActivity.node.imageSize/2;
                        }
                        if(this.endActivity.node.hide && (!this.endActivity.realActivity || (this.endActivity.realActivity.type != 'Tache' && this.endActivity.realActivity.type != 'Start'))){
                            this.endX = this.endX + this.endActivity.node.imageSize/2;
                        }
                    }
                }
                this.line = paper.path("M" + this.startX + " " + this.startY
                    + "L" + this.midX1 + " " + this.midY1
                    + "L" + this.midX2 + " " + this.midY2
                    + "L" + this.midX3 + " " + this.midY3
                    + "L" + this.midX4 + " " + this.midY4
                    + "L" + this.endX + " " + this.endY
                ).attr({
                    'arrow-end': !this.endActivity.realActivity.node.hide || this.startActivity.type == 'Control' ? flow.options.lineArrorEnd : 'none',
                    "stroke-width": '2',
                    stroke: col,
                    start: this.startActivity.id,
                    end: this.endActivity.id,
                    id: this.id,
                    title:this.name
                });
            }
        };
        //----------Transition--------end-------

        flow.node = function(activity){
            this.activity = activity;
            this.imageSize = flow.options.imageSize;
            this.fontSize = flow.options.fontSize;
            this.textSpace = flow.options.textSpace;
            this.textLineWords = flow.options.textLineWords;
            this.textLineSpace = flow.options.textLineSpace;
            //获取图标
            this.imageIcon = flow.options.icon[this.activity.type];
            if(this.activity.type === 'Tache' && flow.options.state[this.activity.state]){
                this.imageIcon = flow.options.state[this.activity.state];
            }
            //获取文本
            this.text = flow.text(this.activity.name);
            //计算节点的宽度和高度
            if(flow.options.direction == flow.constant.DIRECTION.H) {
                this.width = this.imageSize + (this.activity.type == 'Finish' ? 0 : flow.options.nodeSpace);
                this.height = flow.options.branchSize;
            }else if (flow.options.direction == flow.constant.DIRECTION.V) {
                this.width = flow.options.branchSize;
                this.height = this.imageSize + (this.activity.type == 'Finish' ? 0 : flow.options.nodeSpace);
            }
            this.reset = function (paper, x, y) {
                this.x = x;
                this.y = y;
                if(this.activity.node.hide){ return; }
                //图标
                this.image = paper.image(flow.options.iconPath + this.imageIcon.url, this.x, this.y, this.imageSize, this.imageSize).attr({
                    title: this.activity.name,
                    transform: this.imageIcon.angle ? this.imageIcon.angle[flow.options.direction] : null
                });
                
                //文字
                var textX = this.x;
                var textY = this.y;
                for (var i = 0; i < this.text.lines.length; i++) {
                    if(flow.options.textLayout === flow.constant.LAYOUT.T){//文字放在上边
                        //文本中心点x坐标=相对x坐标 + 图标大小的一半
                        //文本中心点y坐标=相对y坐标 -文本高度 - 图标与文本间隔 + 文本前i行的行高之和 + 当前行字体高度的一半
                        textX = this.x + this.imageSize/2;
                        textY = this.y - this.text.height - this.textSpace + (this.fontSize + this.textLineSpace) * i + this.fontSize * 0.5;
                    }else if(flow.options.textLayout === flow.constant.LAYOUT.B){//文字放在下边
                        //文本中心点x坐标=相对x坐标 + 图标大小的一半
                        //文本中心点y坐标=相对y坐标 + 图标大小 + 图标与文本间隔 + 文本前i行的行高之和 + 当前行字体高度的一半
                        textX = this.x + this.imageSize/2;
                        textY = this.y + this.imageSize + this.textSpace + (this.fontSize + this.textLineSpace) * i + this.fontSize * 0.5;
                    }else if(flow.options.textLayout === flow.constant.LAYOUT.L){//文字放在左边
                        //文本中心点x坐标=相对x坐标 - 图标与文本间隔 - 文本宽度的一半
                        //文本中心点y坐标=相对y坐标 + 图标大小的一半 - 文本高度的一半 + 文本前i行的行高之和 + 当前行字体高度的一半
                        textX = this.x - this.textSpace - this.text.width/2;
                        textY = this.y + this.imageSize/2 - this.text.height/2 + (this.fontSize + this.textLineSpace) * i + this.fontSize * 0.5;
                    }else if(flow.options.textLayout === flow.constant.LAYOUT.R){//文字放在右边
                        //文本中心点x坐标=相对x坐标 + 图标大小 + 图标与文本间隔 + 文本宽度的一半
                        //文本中心点y坐标=相对y坐标 + 图标大小的一半 - 文本高度的一半 + 文本前i行的行高之和 + 当前行字体高度的一半
                        textX = this.x + this.imageSize + this.textSpace + this.text.width/2;
                        textY = this.y + this.imageSize/2 - this.text.height/2 + (this.fontSize + this.textLineSpace) * i + this.fontSize * 0.5;
                    }
                    paper.text(textX, textY, this.text.lines[i]).attr({
                        title: this.activity.name,
                        "font-size": this.fontSize
                    });
                }

                //节点属性
                $(this.image.node).attr({ id: this.activity.id });
                $(this.image.node).attr({ tacheId: this.activity.tacheId });
                $(this.image.node).attr({ tacheName: this.activity.name });
                $(this.image.node).attr({ workOrderId: this.activity.workOrderId });
                $(this.image.node).attr({ activityId: this.activity.id });
                $(this.image.node).attr({ type: this.activity.type });
                $(this.image.node).attr({ subType: this.activity.subType });
                $(this.image.node).attr({ state: this.activity.state });
            }
        }
        flow.text = function(name) {
            var textLineWords = Math.ceil(name.length/(Math.ceil(name.length/flow.options.textLineWords)));
            var fontSize = flow.options.fontSize;
            var textLineSpace = flow.options.textLineSpace;
            //获取文本行数
            var lines =[];
            for(var i = 0; i < name.length; i += textLineWords) {
                lines.push(name.slice(i, i + textLineWords));
            }
            //文本宽度=单行最大字符数 * 字体大小
            var width = (lines.length > 1 ? textLineWords : name.length) * fontSize;
            //文本高度=文本行数 * 字体大小 + 文本行之间的间隔数 * 行间隔
            var height = lines.length * fontSize + (lines.length > 0 ? lines.length -1 : 0) * textLineSpace;
            return {lines:lines, width:width, height:height}
        }
        flow.init = function (ele, flowXML) {
            flow.flowXML = $.parseXML(flowXML);
            flow.ele = $(flow.flowXML);
            //1.初始化展示实例画板
            flow.paper = Raphael($(ele)[0].id);
            //2.动态计算节点间隔和分支间隔
            var maxName = "";
            flow.ele.find('Activity').each(function () {
                var name = $(this).attr("name");
                if(maxName.length < name.length) {
                    maxName = name;
                }
            });
            flow.options.textLineWords = flow.options.textLineWords > maxName.length ? maxName.length : flow.options.textLineWords;
            //设置默认值
            var maxText = $.extend(flow.text(maxName),{width:flow.options.textLineWords * flow.options.fontSize});
            if(flow.options.direction == flow.constant.DIRECTION.H) {
                var nodeSpace = maxText.width;
                var branchSize = flow.options.imageSize + flow.options.textSpace + maxText.height;
                flow.options.nodeSpace = flow.options.autsize && flow.options.nodeSpace < nodeSpace ? nodeSpace : flow.options.nodeSpace;
                flow.options.branchSize = flow.options.autsize && flow.options.branchSize < branchSize ? branchSize : flow.options.branchSize;
                flow.options.textLayout = flow.options.textLayout ? flow.options.textLayout : flow.constant.LAYOUT.B;
            }else if (flow.options.direction == flow.constant.DIRECTION.V) {
                var nodeSpace = maxText.height;
                var branchSize = flow.options.imageSize + flow.options.textSpace + maxText.width;
                flow.options.nodeSpace = flow.options.autsize && flow.options.nodeSpace < nodeSpace ? nodeSpace : flow.options.nodeSpace;
                flow.options.branchSize = flow.options.autsize && flow.options.branchSize < branchSize ? branchSize : flow.options.branchSize;
                flow.options.textLayout = flow.options.textLayout ? flow.options.textLayout : flow.constant.LAYOUT.R;
            }
            //3.解析节点,并计算节点的宽和高
            flow.root = new flow.Branch(flow.ele.find("Activities"));
            //4.解析线条
            flow.ele.find('Transition').each(function () {
                new flow.Transition(this)
            });
            
            //5.更新每个节点坐标
            flow.root.updateData(flow.options.offsetX, flow.options.offsetY);
            //6.根据流程图的大小自动适配，重置画板的大小
            var size = flow.root.size();
            flow.paper.setSize(size.width + flow.options.offsetX * 2, size.height + flow.options.offsetY * 2);
            //7.画节点
            flow.root.paint(flow.paper);
            //8.画线条
            for(var key in flow.transitions){
                flow.transitions[key].paint(flow.paper);
            }
        };
        flow.initState = function (ele, stateConfig) {
            var paperState = Raphael($(ele)[0].id);
            this.x = 0;
            this.y = 0;
            this.fontSize = flow.options.fontSize + 3;
            this.imageSize = flow.options.imageSize;
            for (var key in flow.options.state) {
                var icon = flow.options.state[key];
                if(icon.legend == false){ continue; }
                paperState.image(flow.options.iconPath + icon.url, this.x, this.y, this.imageSize, this.imageSize);
                this.width = this.width + this.imageSize;

                this.x = this.x + this.imageSize + icon.name.length * this.fontSize / 2 + 5;
                paperState.text(this.x, this.y + this.imageSize / 2, icon.name).attr({
                    "font-size": this.fontSize
                });
                this.x = this.x + icon.name.length * this.fontSize / 2 + 20;
            }
            paperState.setSize(this.x, paperState.height);
        };
        return flow;
    }

    $.fn.zflow = function (flowXML, state, config) {
        var flow = IwhalecloudFlow();
        var reduce = window.location.search.substr(1).match(new RegExp("(^|&)reduce=([^&]*)(&|$)"));
        $.extend(flow.options, config, {direction:flow.constant.DIRECTION.H,reduce:reduce ? reduce[2] == 'true' : true});
        $.extend(flow.options.state, state);
        flow.ele = this;
        flow.init(this, flowXML);
        return flow;
    };
    $.IwhalecloudFlow = IwhalecloudFlow;
})(jQuery);
//=========zflow===========
