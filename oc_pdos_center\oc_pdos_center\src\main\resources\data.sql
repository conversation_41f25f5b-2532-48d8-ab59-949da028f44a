-- SQLite数据库兼容的测试数据初始化SQL
-- 清理现有数据
DELETE FROM t_process_step WHERE 1=1;
DELETE FROM t_order WHERE 1=1;
DELETE FROM t_product WHERE 1=1;

-- 插入产品数据
INSERT INTO t_product (id, product_code, product_name, product_type, description, status, create_time, update_time) VALUES (1, 'PROD001', '5G专网', 'CLOUD_SERVICE', '5G专网业务产品', 1, datetime('now'), datetime('now'));
INSERT INTO t_product (id, product_code, product_name, product_type, description, status, create_time, update_time) VALUES (2, 'CLOUD_PC_PUBLIC', '云电脑公众版', 'CLOUD_SERVICE', '云电脑公众版业务产品', 1, datetime('now'), datetime('now'));
INSERT INTO t_product (id, product_code, product_name, product_type, description, status, create_time, update_time) VALUES (3, 'SMART_HOME', '智慧家庭', 'IOT_SERVICE', '智慧家庭业务产品', 1, datetime('now'), datetime('now'));
INSERT INTO t_product (id, product_code, product_name, product_type, description, status, create_time, update_time) VALUES (4, 'ENTERPRISE_CLOUD', '企业云', 'CLOUD_SERVICE', '企业云业务产品', 1, datetime('now'), datetime('now'));
INSERT INTO t_product (id, product_code, product_name, product_type, description, status, create_time, update_time) VALUES (5, '5G_PACKAGE', '5G套餐', 'TELECOM_SERVICE', '5G套餐业务产品', 1, datetime('now'), datetime('now'));

-- 插入订单数据
INSERT INTO t_order (id, order_no, cust_order_no, product_id, product_name, customer_id, customer_name, order_status, order_amount, province_code, province_name, city_code, city_name, business_type, process_instance_id, current_step, description, create_time, update_time, create_by, update_by) VALUES (1, 'ORD000000050', 'CUST20240101001', 'PROD001', '5G专网', '***********', '张三', 1, 1000.00, '110000', '北京市', '110100', '东城区', '开通', 'PROC_INST_001', '省编排拆单', '5G专网业务开通订单', datetime('now', '-1 day'), datetime('now'), 'system', 'system');
INSERT INTO t_order (id, order_no, cust_order_no, product_id, product_name, customer_id, customer_name, order_status, order_amount, province_code, province_name, city_code, city_name, business_type, process_instance_id, current_step, description, create_time, update_time, create_by, update_by) VALUES (2, 'ORD000000051', 'CUST20240101002', 'CLOUD_PC_PUBLIC', '云电脑公众版', '***********', '李四', 2, 2000.00, '110000', '北京市', '110200', '西城区', '开通', 'PROC_INST_002', '全程报竣', '云电脑公众版业务开通订单', datetime('now', '-2 day'), datetime('now'), 'system', 'system');
INSERT INTO t_order (id, order_no, cust_order_no, product_id, product_name, customer_id, customer_name, order_status, order_amount, province_code, province_name, city_code, city_name, business_type, process_instance_id, current_step, description, create_time, update_time, create_by, update_by) VALUES (3, 'ORD000000052', 'CUST20240101003', 'SMART_HOME', '智慧家庭', '***********', '王五', 0, 1500.00, '110000', '北京市', '110300', '朝阳区', '开通', 'PROC_INST_003', '订单受理', '智慧家庭业务开通订单', datetime('now', '-3 day'), datetime('now'), 'system', 'system');
INSERT INTO t_order (id, order_no, cust_order_no, product_id, product_name, customer_id, customer_name, order_status, order_amount, province_code, province_name, city_code, city_name, business_type, process_instance_id, current_step, description, create_time, update_time, create_by, update_by) VALUES (4, 'ORD000000053', 'CUST20240101004', 'ENTERPRISE_CLOUD', '企业云', '***********', '赵六', 4, 5000.00, '110000', '北京市', '110400', '丰台区', '开通', 'PROC_INST_004', '省内装维', '企业云业务开通订单', datetime('now', '-4 day'), datetime('now'), 'system', 'system');
INSERT INTO t_order (id, order_no, cust_order_no, product_id, product_name, customer_id, customer_name, order_status, order_amount, province_code, province_name, city_code, city_name, business_type, process_instance_id, current_step, description, create_time, update_time, create_by, update_by) VALUES (5, 'ORD000000054', 'CUST20240101005', '5G_PACKAGE', '5G套餐', '***********', '钱七', 1, 300.00, '110000', '北京市', '110500', '石景山区', '开通', 'PROC_INST_005', '省派发集团', '5G套餐业务开通订单', datetime('now', '-5 day'), datetime('now'), 'system', 'system');

-- 插入流程步骤数据 - 订单1的流程步骤
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (1, 1, 'ORD000000050', 'ORDER_ACCEPT', '订单受理', 2, 1, datetime('now', '-1 day'), datetime('now', '-1 day', '+1 hour'), '张三', 'CRM部门', '订单受理完成', 'PROVINCE_CRM', datetime('now', '-1 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (2, 1, 'ORD000000050', 'PROVINCE_RECEIVE', '省编排收单', 2, 2, datetime('now', '-1 day', '+1 hour'), datetime('now', '-1 day', '+2 hour'), '李四', '省编排中心', '省编排收单完成', 'PROVINCE_ORCHESTRATION', datetime('now', '-1 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (3, 1, 'ORD000000050', 'PROVINCE_SPLIT', '省编排拆单', 1, 3, datetime('now', '-1 day', '+2 hour'), NULL, '王五', '省编排中心', '正在处理中', 'PROVINCE_ORCHESTRATION', datetime('now', '-1 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (4, 1, 'ORD000000050', 'SEND_TO_GROUP', '省派发集团', 0, 4, NULL, NULL, NULL, NULL, '等待处理', 'PROVINCE_ORCHESTRATION', datetime('now', '-1 day'), datetime('now'));

-- 订单2的流程步骤
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (5, 2, 'ORD000000051', 'ORDER_ACCEPT', '订单受理', 2, 1, datetime('now', '-2 day'), datetime('now', '-2 day', '+1 hour'), '张三', 'CRM部门', '订单受理完成', 'PROVINCE_CRM', datetime('now', '-2 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (6, 2, 'ORD000000051', 'PROVINCE_RECEIVE', '省编排收单', 2, 2, datetime('now', '-2 day', '+1 hour'), datetime('now', '-2 day', '+2 hour'), '李四', '省编排中心', '省编排收单完成', 'PROVINCE_ORCHESTRATION', datetime('now', '-2 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (7, 2, 'ORD000000051', 'PROVINCE_SPLIT', '省编排拆单', 2, 3, datetime('now', '-2 day', '+2 hour'), datetime('now', '-2 day', '+3 hour'), '王五', '省编排中心', '省编排拆单完成', 'PROVINCE_ORCHESTRATION', datetime('now', '-2 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (8, 2, 'ORD000000051', 'SEND_TO_GROUP', '省派发集团', 2, 4, datetime('now', '-2 day', '+3 hour'), datetime('now', '-2 day', '+4 hour'), '赵六', '省编排中心', '省派发集团完成', 'PROVINCE_ORCHESTRATION', datetime('now', '-2 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (9, 2, 'ORD000000051', 'GROUP_ORCHESTRATION', '集团编排开通', 2, 5, datetime('now', '-2 day', '+4 hour'), datetime('now', '-2 day', '+5 hour'), '钱七', '集团编排中心', '集团编排开通完成', 'GROUP_ORCHESTRATION', datetime('now', '-2 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (10, 2, 'ORD000000051', 'FINAL_REPORT', '全程报竣', 2, 6, datetime('now', '-2 day', '+5 hour'), datetime('now', '-2 day', '+6 hour'), '孙八', 'CRM部门', '全程报竣完成', 'PROVINCE_CRM', datetime('now', '-2 day'), datetime('now'));

-- 订单3的流程步骤（智慧家庭）
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (11, 3, 'ORD000000052', 'ORDER_ACCEPT', '订单受理', 2, 1, datetime('now', '-3 day'), datetime('now', '-3 day', '+1 hour'), '李明', 'CRM部门', '订单受理完成', 'PROVINCE_CRM', datetime('now', '-3 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (12, 3, 'ORD000000052', 'CRM_VALIDATE', 'CRM订单校验', 2, 2, datetime('now', '-3 day', '+1 hour'), datetime('now', '-3 day', '+90 minutes'), '王芳', 'CRM部门', 'CRM订单校验完成', 'PROVINCE_CRM', datetime('now', '-3 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (13, 3, 'ORD000000052', 'PROVINCE_RECEIVE', '省编排收单', 2, 3, datetime('now', '-3 day', '+2 hour'), datetime('now', '-3 day', '+3 hour'), '张伟', '省编排中心', '省编排收单完成', 'PROVINCE_ORCHESTRATION', datetime('now', '-3 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (14, 3, 'ORD000000052', 'PROVINCE_SPLIT', '省编排拆单', 2, 4, datetime('now', '-3 day', '+3 hour'), datetime('now', '-3 day', '+4 hour'), '刘强', '省编排中心', '省编排拆单完成', 'PROVINCE_ORCHESTRATION', datetime('now', '-3 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (15, 3, 'ORD000000052', 'GROUP_RESOURCE_CHECK', '集团资源检查', 2, 5, datetime('now', '-3 day', '+4 hour'), datetime('now', '-3 day', '+5 hour'), '陈华', '集团编排中心', '集团资源检查完成', 'GROUP_ORCHESTRATION', datetime('now', '-3 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (16, 3, 'ORD000000052', 'GROUP_ORCHESTRATION', '集团编排开通', 2, 6, datetime('now', '-3 day', '+5 hour'), datetime('now', '-3 day', '+6 hour'), '赵磊', '集团编排中心', '集团编排开通完成', 'GROUP_ORCHESTRATION', datetime('now', '-3 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (17, 3, 'ORD000000052', 'PROVINCE_DISPATCH', '省调度派单', 1, 7, datetime('now', '-3 day', '+6 hour'), NULL, '孙丽', '省调度中心', '正在派单处理', 'PROVINCE_DISPATCH', datetime('now', '-3 day'), datetime('now'));

-- 订单4的流程步骤（企业云）
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (18, 4, 'ORD000000053', 'ORDER_ACCEPT', '订单受理', 2, 1, datetime('now', '-4 day'), datetime('now', '-4 day', '+1 hour'), '周杰', 'CRM部门', '订单受理完成', 'PROVINCE_CRM', datetime('now', '-4 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (19, 4, 'ORD000000053', 'CRM_VALIDATE', 'CRM订单校验', 2, 2, datetime('now', '-4 day', '+1 hour'), datetime('now', '-4 day', '+2 hour'), '吴敏', 'CRM部门', 'CRM订单校验完成', 'PROVINCE_CRM', datetime('now', '-4 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (20, 4, 'ORD000000053', 'CRM_APPROVAL', 'CRM审批', 2, 3, datetime('now', '-4 day', '+2 hour'), datetime('now', '-4 day', '+3 hour'), '郑涛', 'CRM部门', 'CRM审批完成', 'PROVINCE_CRM', datetime('now', '-4 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (21, 4, 'ORD000000053', 'PROVINCE_RECEIVE', '省编排收单', 2, 4, datetime('now', '-4 day', '+3 hour'), datetime('now', '-4 day', '+4 hour'), '何琳', '省编排中心', '省编排收单完成', 'PROVINCE_ORCHESTRATION', datetime('now', '-4 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (22, 4, 'ORD000000053', 'PROVINCE_SPLIT', '省编排拆单', 2, 5, datetime('now', '-4 day', '+4 hour'), datetime('now', '-4 day', '+5 hour'), '马超', '省编排中心', '省编排拆单完成', 'PROVINCE_ORCHESTRATION', datetime('now', '-4 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (23, 4, 'ORD000000053', 'GROUP_ORCHESTRATION', '集团编排开通', 2, 6, datetime('now', '-4 day', '+5 hour'), datetime('now', '-4 day', '+7 hour'), '朱雯', '集团编排中心', '集团编排开通完成', 'GROUP_ORCHESTRATION', datetime('now', '-4 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (24, 4, 'ORD000000053', 'PROVINCE_DISPATCH', '省调度派单', 2, 7, datetime('now', '-4 day', '+7 hour'), datetime('now', '-4 day', '+8 hour'), '徐峰', '省调度中心', '省调度派单完成', 'PROVINCE_DISPATCH', datetime('now', '-4 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (25, 4, 'ORD000000053', 'PROVINCE_INSTALL', '省内装维', 1, 8, datetime('now', '-4 day', '+8 hour'), NULL, '黄斌', '省装维中心', '正在装维处理', 'PROVINCE_DISPATCH', datetime('now', '-4 day'), datetime('now'));

-- 订单5的流程步骤（5G套餐）
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (26, 5, 'ORD000000054', 'ORDER_ACCEPT', '订单受理', 2, 1, datetime('now', '-5 day'), datetime('now', '-5 day', '+30 minutes'), '田静', 'CRM部门', '订单受理完成', 'PROVINCE_CRM', datetime('now', '-5 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (27, 5, 'ORD000000054', 'PROVINCE_RECEIVE', '省编排收单', 2, 2, datetime('now', '-5 day', '+1 hour'), datetime('now', '-5 day', '+2 hour'), '邓伟', '省编排中心', '省编排收单完成', 'PROVINCE_ORCHESTRATION', datetime('now', '-5 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (28, 5, 'ORD000000054', 'PROVINCE_SPLIT', '省编排拆单', 2, 3, datetime('now', '-5 day', '+2 hour'), datetime('now', '-5 day', '+3 hour'), '罗军', '省编排中心', '省编排拆单完成', 'PROVINCE_ORCHESTRATION', datetime('now', '-5 day'), datetime('now'));
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_order, start_time, end_time, handler, handler_dept, remark, step_type, create_time, update_time) VALUES (29, 5, 'ORD000000054', 'SEND_TO_GROUP', '省派发集团', 1, 4, datetime('now', '-5 day', '+3 hour'), NULL, '高强', '省编排中心', '正在派发集团', 'PROVINCE_ORCHESTRATION', datetime('now', '-5 day'), datetime('now'));