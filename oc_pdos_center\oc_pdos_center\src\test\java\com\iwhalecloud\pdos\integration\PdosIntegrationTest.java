package com.iwhalecloud.pdos.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iwhalecloud.PdosCenterApplication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.HashMap;
import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * PDOS系统集成测试
 * 测试完整的API调用流程
 */
@SpringBootTest(classes = PdosCenterApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebMvc
@ActiveProfiles("test")
class PdosIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;
    
    private MockMvc mockMvc;
    private ObjectMapper objectMapper;
    
    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        objectMapper = new ObjectMapper();
    }
    
    /**
     * 测试完整的订单查询流程
     */
    @Test
    void testCompleteOrderQueryFlow() throws Exception {
        // 1. 首先获取产品列表
        mockMvc.perform(get("/pdos/products"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data").isArray());
        
        // 2. 查询订单列表
        Map<String, Object> queryParams = createValidOrderQueryParams();
        
        mockMvc.perform(post("/pdos/queryCenterOrderList")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(queryParams)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.data").isArray())
                .andExpect(jsonPath("$.data.pageNum").exists())
                .andExpect(jsonPath("$.data.pageSize").exists())
                .andExpect(jsonPath("$.data.total").exists());
        
        // 3. 查询订单详情
        Map<String, Object> detailParams = createValidOrderDetailParams();
        
        mockMvc.perform(post("/pdos/queryCenterOrderDetails")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(detailParams)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.provinceSteps").isArray())
                .andExpect(jsonPath("$.data.groupVisualization").exists());
    }
    
    /**
     * 测试API参数校验
     */
    @Test
    void testApiValidation() throws Exception {
        // 测试缺少必填参数
        Map<String, Object> invalidParams = new HashMap<>();
        invalidParams.put("pageNum", 1);
        invalidParams.put("pageSize", 10);
        // 缺少productCode
        
        mockMvc.perform(post("/pdos/queryCenterOrderList")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidParams)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(-1));
    }
    
    /**
     * 测试时间范围校验
     */
    @Test
    void testTimeRangeValidation() throws Exception {
        // 测试时间范围超过限制
        Map<String, Object> params = createValidOrderQueryParams();
        params.put("startTime", "2024-01-01 00:00:00");
        params.put("endTime", "2024-03-31 23:59:59"); // 超过40天的时间范围
        // 确保包含必填参数
        params.put("productCode", "PROD001");
        params.put("provinceId", "110000");
        params.put("businessAccount", "***********"); // 添加三选一必填字段
        
        mockMvc.perform(post("/pdos/queryCenterOrderList")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(params)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(-1))
                .andExpect(jsonPath("$.message").value("查询时间区间不能超过40天"));
    }
    
    /**
     * 测试分页功能
     */
    @Test
    void testPagination() throws Exception {
        Map<String, Object> params = createValidOrderQueryParams();
        
        // 测试第一页
        params.put("pageNum", 1);
        params.put("pageSize", 5);
        
        mockMvc.perform(post("/pdos/queryCenterOrderList")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(params)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.pageNum").value(1))
                .andExpect(jsonPath("$.data.pageSize").value(5));
        
        // 测试第二页
        params.put("pageNum", 2);
        
        mockMvc.perform(post("/pdos/queryCenterOrderList")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(params)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.pageNum").value(2));
    }
    
    /**
     * 测试跨域配置
     */
    @Test
    void testCorsConfiguration() throws Exception {
        mockMvc.perform(options("/pdos/products")
                .header("Origin", "http://localhost:3002")
                .header("Access-Control-Request-Method", "GET"))
                .andExpect(status().isOk())
                .andExpect(header().string("Access-Control-Allow-Origin", "http://localhost:3002"));
    }
    
    /**
     * 测试错误处理
     */
    @Test
    void testErrorHandling() throws Exception {
        // 测试不存在的接口
        mockMvc.perform(get("/pdos/nonexistent"))
                .andExpect(status().isNotFound());
        
        // 测试无效的JSON格式
        mockMvc.perform(post("/pdos/queryCenterOrderList")
                .contentType(MediaType.APPLICATION_JSON)
                .content("invalid json"))
                .andExpect(status().isBadRequest());
    }
    
    /**
     * 测试性能 - 大量数据查询
     */
    @Test
    void testPerformanceWithLargeDataset() throws Exception {
        Map<String, Object> params = createValidOrderQueryParams();
        params.put("pageSize", 100); // 查询较大数据集
        
        long startTime = System.currentTimeMillis();
        
        mockMvc.perform(post("/pdos/queryCenterOrderList")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(params)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 验证响应时间在合理范围内（例如小于5秒）
        assert duration < 5000 : "查询响应时间过长: " + duration + "ms";
    }
    
    /**
     * 测试并发访问
     */
    @Test
    void testConcurrentAccess() throws Exception {
        Map<String, Object> params = createValidOrderQueryParams();
        String jsonParams = objectMapper.writeValueAsString(params);
        
        // 模拟并发请求
        Thread[] threads = new Thread[5];
        for (int i = 0; i < threads.length; i++) {
            threads[i] = new Thread(() -> {
                try {
                    mockMvc.perform(post("/pdos/queryCenterOrderList")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(jsonParams))
                            .andExpect(status().isOk())
                            .andExpect(jsonPath("$.code").value(0));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }
    }
    
    // 辅助方法：创建有效的订单查询参数
    private Map<String, Object> createValidOrderQueryParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("productCode", "PROD001");
        params.put("provinceId", "110000");
        params.put("startTime", "2024-01-01 00:00:00");
        params.put("endTime", "2024-01-31 23:59:59"); // 修改为30天内的时间范围
        params.put("businessAccount", "***********"); // 三选一必填字段之一
        params.put("pageNum", 1);
        params.put("pageSize", 10);
        return params;
    }
    
    // 辅助方法：创建有效的订单详情查询参数
    private Map<String, Object> createValidOrderDetailParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("provinceSerialId", "SERIAL001"); // 必填参数
        params.put("productCode", "PROD001");
        params.put("orderNumber", "ORDER001");
        params.put("startTime", "2024-01-01 00:00:00");
        params.put("endTime", "2024-01-31 23:59:59"); // 修改为30天内的时间范围
        return params;
    }
}