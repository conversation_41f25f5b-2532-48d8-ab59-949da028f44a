﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-0px;
  width:1868px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1px;
  width:1868px;
  height:963px;
  display:flex;
  transition:none;
}
#u0 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u0_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1868px;
  height:963px;
}
#u0_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1 {
  border-width:0px;
  position:absolute;
  left:1200px;
  top:402px;
  width:116px;
  height:96px;
  display:flex;
  transition:none;
}
#u1 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:96px;
}
#u1_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u2 {
  border-width:0px;
  position:absolute;
  left:1199px;
  top:536px;
  width:117px;
  height:15px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u2 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
