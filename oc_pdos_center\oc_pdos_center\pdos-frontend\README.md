# PDOS战新可视化系统 - 前端

这是PDOS战新可视化系统的前端项目，基于React + TypeScript + Ant Design构建。

## 技术栈

- **React 18** - 前端框架
- **TypeScript** - 类型安全的JavaScript
- **Ant Design** - 企业级UI组件库
- **Create React App** - 项目脚手架

## 项目结构

```
src/
├── pages/                 # 页面组件
│   ├── Dashboard.tsx      # 仪表板页面
│   ├── OrderList.tsx      # 订单列表页面
│   └── OrderDetail.tsx    # 订单详情页面
├── services/              # API服务
│   └── api.ts            # API接口定义
├── types/                 # TypeScript类型定义
│   └── index.ts          # 通用类型定义
├── App.tsx               # 主应用组件
└── index.tsx             # 应用入口
```

## 开发命令

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm start
```
应用将在 [http://localhost:3002](http://localhost:3002) 启动

### 构建生产版本
```bash
npm run build
```

### 运行测试
```bash
npm test
```

## 功能特性

### 订单管理
- 订单列表查询和分页
- 多条件筛选（订单号、客户名称、订单状态等）
- 订单详情查看

### 流程可视化
- 订单流程步骤展示
- 流程进度跟踪
- 步骤状态可视化

### UI特性
- 响应式设计
- 现代化界面
- 状态标签和进度条
- 详细信息弹窗

## API配置

前端通过以下API与后端通信：

- **基础URL**: `http://localhost:8888/api`
- **订单列表**: `POST /pdos/queryCenterOrderList`
- **订单详情**: `POST /pdos/queryCenterOrderDetails`

## 环境配置

项目使用 `.env` 文件进行环境配置：

```
REACT_APP_API_BASE_URL=http://localhost:8888/api
PORT=3002
```

## 开发说明

### 代码规范
- 使用TypeScript进行类型安全开发
- 遵循React Hooks最佳实践
- 使用Ant Design组件库保证UI一致性

### 状态管理
- 使用React内置的useState和useEffect
- API调用统一封装在services层

### 类型定义
- 所有API响应和组件Props都有完整的TypeScript类型定义
- 类型文件位于 `src/types/` 目录

## 故障排除

### 常见问题

1. **端口冲突**
   - 默认端口3002，如有冲突请修改.env文件中的PORT配置

2. **API连接问题**
   - 确保后端服务已启动（端口8888）
   - 检查.env文件中的API_BASE_URL配置

3. **依赖安装问题**
   - 删除node_modules文件夹和package-lock.json
   - 重新运行npm install

### 调试
- 使用浏览器开发者工具查看网络请求
- 控制台日志输出API调用信息
- React Developer Tools扩展用于组件调试
