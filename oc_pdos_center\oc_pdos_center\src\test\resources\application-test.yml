# 测试环境配置文件
spring:
  # 数据源配置 - 使用SQLite数据库进行测试
  datasource:
    url: *******************************
    driver-class-name: org.sqlite.JDBC
    username: 
    password: 
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop  # 测试时自动创建和删除表
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.community.dialect.SQLiteDialect
        
  # SQL初始化配置
  sql:
    init:
      mode: always
      data-locations: classpath:data.sql
      
# 日志配置
logging:
  level:
    com.iwhalecloud.pdos: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    
# 服务器配置
server:
  port: 0  # 随机端口，避免测试时端口冲突
  
# 测试数据初始化
test:
  data:
    init: true
    cleanup: true