// 通用响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp: string;
}

// 分页结果类型
export interface PageResult<T> {
  pageNum: number;
  pageSize: number;
  total: number;
  totalPages: number;
  data: T[];
  hasNext: boolean;
  hasPrevious: boolean;
  isFirst: boolean;
  isLast: boolean;
}

// 产品类型
export interface Product {
  id: number;
  productCode: string;
  productName: string;
  productType: string;
  status: number;
  description?: string;
  createTime: string;
  updateTime: string;
  createBy: string;
  updateBy: string;
}

// 订单类型
export interface Order {
  id: number;
  orderNo: string;
  custOrderNo?: string;
  productId: number;
  productName: string;
  customerId?: string;
  customerName?: string;
  orderStatus: number; // 0-待处理，1-处理中，2-已完成，3-已取消，4-异常
  orderAmount?: number;
  provinceCode?: string;
  provinceName?: string;
  cityCode?: string;
  cityName?: string;
  businessType?: string;
  processInstanceId?: string;
  currentStep?: string;
  description?: string;
  createTime: string;
  updateTime: string;
  completeTime?: string;
  createBy?: string;
  updateBy?: string;
}

// 流程步骤类型
export interface ProcessStep {
  id: number;
  orderId: number;
  orderNo: string;
  stepCode: string;
  stepName: string;
  stepStatus: string | number;
  stepType?: 'PROVINCE_CRM' | 'PROVINCE_ORCHESTRATION' | 'GROUP_ORCHESTRATION' | 'PROVINCE_DISPATCH';
  stepOrder?: number;
  handler?: string;
  handlerDept?: string;
  processor?: string;
  processorDept?: string;
  startTime?: string;
  endTime?: string;
  durationMinutes?: number;
  stepResult?: string;
  remark?: string;
  errorMessage?: string;
  description?: string;
  timestamp?: string;
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
}

// 订单查询参数类型
export interface OrderQueryParams {
  orderNo?: string; // 订单流水号
  custOrderNo?: string; // 客户订单号
  productName?: string; // 产品名称
  customerName?: string; // 客户名称
  orderStatus?: number; // 订单状态
  provinceCode?: string; // 省份编码
  businessType?: string; // 业务类型
  startTime?: string; // 开始时间（必填）
  endTime?: string; // 结束时间（必填）
  timeRange?: any[]; // 时间范围选择器的值
  current?: number;
  size?: number;
  // 新增字段以支持新业务接口
  productCode?: string; // 产品编码
  businessNumber?: string; // 业务号码
  accessNumber?: string; // 接入号
  productInstanceId?: string; // 产品实例ID
  businessAccount?: string; // 业务账号
  areaCode?: string; // 地区编码
  cityCode?: string; // 地市编码
}

// 新业务省内段订单查询参数类型（4.121接口）
export interface ProvinceOrderQueryParams {
  productCode: string;        // 产品编码（必填）
  businessNumber?: string;    // 业务号码
  orderNumber?: string;       // 订单流水号
  accessNumber?: string;      // 接入号
  productInstanceId?: string; // 产品实例ID
  provinceCode?: string;      // 省份编码
  cityCode?: string;          // 地市编码
  startTime?: string;         // 开始时间
  endTime?: string;           // 结束时间
  pageNum: number;
  pageSize: number;
}

// 新业务省内段环节信息查询参数类型（4.122接口）
export interface ProvinceOrderDetailParams {
  productCode: string;        // 产品编码（必填）
  businessNumber?: string;    // 业务号码
  orderNumber?: string;       // 订单流水号
  accessNumber?: string;      // 接入号
  productInstanceId?: string; // 产品实例ID
  provinceCode?: string;      // 省份编码
  cityCode?: string;          // 地市编码
  startTime?: string;         // 开始时间
  endTime?: string;           // 结束时间
}

// 统计数据类型
export interface Statistics {
  totalOrders: number;
  todayOrders: number;
  monthOrders: number;
  orderStatusStats: { [key: string]: number };
  provinceStats: { [key: string]: number };
  productStats: { [key: string]: number };
}

// 客户规划流程步骤类型
export interface CustomerPlanStep {
  stepName: string;
  stepStatus: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'EXCEPTION';
  relatedEndToEndSteps: string[]; // 关联的端到端流程环节
}

// 端到端流程环节类型
export interface EndToEndStep {
  stepCode: string;
  stepName: string;
  stepStatus: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'EXCEPTION';
  stepType: 'PROVINCE_CRM' | 'PROVINCE_ORCHESTRATION' | 'GROUP_ORCHESTRATION' | 'PROVINCE_DISPATCH';
  startTime?: string;
  endTime?: string;
  errorMessage?: string;
}

// 流程列表项类型
export interface ProcessListItem {
  stepCode: string;
  stepName: string;
  stepStatus: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'EXCEPTION';
  stepType: 'PROVINCE_CRM' | 'PROVINCE_ORCHESTRATION' | 'GROUP_ORCHESTRATION' | 'PROVINCE_DISPATCH';
  handler?: string;
  handlerDept?: string;
  startTime?: string;
  endTime?: string;
  durationMinutes?: number;
  remark?: string;
  errorMessage?: string;
}

// 订单详情类型
export interface OrderDetail {
  order: Order;
  steps: ProcessStep[];
  product: Product;
  processSteps?: ProcessStep[]; // 流程步骤列表
  groupedProcessSteps?: {
    PROVINCE_CRM: ProcessStep[];
    PROVINCE_ORCHESTRATION: ProcessStep[];
    GROUP_ORCHESTRATION: ProcessStep[];
    PROVINCE_DISPATCH: ProcessStep[];
  }; // 按系统类型分组的流程步骤
  completedSteps?: number; // 已完成步骤数
  totalSteps?: number; // 总步骤数
  progress?: number; // 进度百分比
  customerPlanSteps?: CustomerPlanStep[]; // 客户规划流程
  endToEndSteps?: EndToEndStep[]; // 端到端流程图
  processListItems?: ProcessListItem[]; // 流程列表
  // 新增字段：省内环节信息
  provinceSteps?: ProvinceStep[];
  // 新增字段：集团战新可视化数据
  groupVisualizationData?: GroupVisualizationData;
  statistics?: VisualizationStatistics;
}

// 省内环节步骤类型
export interface ProvinceStep {
  stepName: string;
  status: string;
  statusText: string;
  time?: string;
}

// 集团战新可视化数据类型
export interface GroupVisualizationData {
  endToEndProcess: ProcessStepInfo[];
  customerPlanProcess: ProcessStepInfo[];
  processList: ProcessListInfo[];
  statistics: VisualizationStatistics;
  businessDetail: BusinessDetail;
}

// 流程步骤信息类型
export interface ProcessStepInfo {
  stepName: string;
  status: string;
  statusText: string;
  time?: string;
}

// 流程列表信息类型
export interface ProcessListInfo {
  processName: string;
  status: string;
  statusText: string;
}

// 可视化统计信息类型
export interface VisualizationStatistics {
  totalSteps: number;
  completedSteps: number;
  processingSteps: number;
  pendingSteps: number;
  completionRate: string;
}

// 业务详情类型
export interface BusinessDetail {
  productCode: string;
  productName: string;
  businessNumber?: string;
  orderNumber?: string;
  accessNumber?: string;
  productInstanceId?: string;
  provinceName?: string;
  cityName?: string;
}

// 产品查询说明配置
export interface ProductQueryConfig {
  productCode: string;
  productName: string;
  queryDescription: string;
  requiredFields: string[]; // 必填字段（三选一）
}