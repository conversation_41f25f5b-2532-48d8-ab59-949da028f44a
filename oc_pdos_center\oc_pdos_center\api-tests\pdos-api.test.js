/**
 * PDOS系统API接口测试
 * 使用Node.js和axios进行API测试
 * 运行方式：node api-tests/pdos-api.test.js
 */

const axios = require('axios');
const assert = require('assert');

// 配置
const BASE_URL = 'http://localhost:8888/api';
const API_TIMEOUT = 10000;

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 测试数据
const testData = {
  validOrderQuery: {
    productCode: 'PROD001',
    provinceId: '110000',
    startTime: '2024-01-01 00:00:00',
    endTime: '2024-01-31 23:59:59', // 修改为30天内的时间范围
    businessAccount: '***********', // 添加三选一必填字段
    pageNum: 1,
    pageSize: 10,
  },
  validOrderDetail: {
    provinceSerialId: 'SERIAL001', // 必填参数
    productCode: 'PROD001',
    orderNumber: 'ORDER001',
    startTime: '2024-01-01 00:00:00',
    endTime: '2024-01-31 23:59:59', // 修改为30天内的时间范围
  },
  invalidOrderQuery: {
    // 缺少必填参数
    startTime: '2024-01-01 00:00:00',
    endTime: '2024-01-31 23:59:59',
    pageNum: 1,
    pageSize: 10,
  },
};

// 测试工具函数
class ApiTester {
  constructor() {
    this.testResults = [];
    this.totalTests = 0;
    this.passedTests = 0;
    this.failedTests = 0;
  }

  async runTest(testName, testFunction) {
    this.totalTests++;
    console.log(`\n🧪 运行测试: ${testName}`);
    
    try {
      await testFunction();
      this.passedTests++;
      console.log(`✅ 测试通过: ${testName}`);
      this.testResults.push({ name: testName, status: 'PASSED' });
    } catch (error) {
      this.failedTests++;
      console.log(`❌ 测试失败: ${testName}`);
      console.log(`   错误信息: ${error.message}`);
      this.testResults.push({ 
        name: testName, 
        status: 'FAILED', 
        error: error.message 
      });
    }
  }

  printSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 测试结果汇总');
    console.log('='.repeat(60));
    console.log(`总测试数: ${this.totalTests}`);
    console.log(`通过: ${this.passedTests}`);
    console.log(`失败: ${this.failedTests}`);
    console.log(`成功率: ${((this.passedTests / this.totalTests) * 100).toFixed(2)}%`);
    
    if (this.failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults
        .filter(result => result.status === 'FAILED')
        .forEach(result => {
          console.log(`   - ${result.name}: ${result.error}`);
        });
    }
    
    console.log('\n' + '='.repeat(60));
  }

  // 断言工具
  assertEqual(actual, expected, message = '') {
    assert.strictEqual(actual, expected, message);
  }

  assertTrue(condition, message = '') {
    assert.ok(condition, message);
  }

  assertNotNull(value, message = '') {
    assert.ok(value !== null && value !== undefined, message);
  }

  assertArrayNotEmpty(array, message = '') {
    assert.ok(Array.isArray(array) && array.length > 0, message);
  }
}

// 创建测试实例
const tester = new ApiTester();

// 测试用例
async function runAllTests() {
  console.log('🚀 开始PDOS系统API测试');
  console.log(`📍 测试目标: ${BASE_URL}`);
  
  // 1. 测试服务器连通性
  await tester.runTest('服务器连通性测试', async () => {
    const response = await api.get('/pdos/products');
    tester.assertEqual(response.status, 200, '服务器应该返回200状态码');
  });

  // 2. 测试获取产品列表
  await tester.runTest('获取产品列表', async () => {
    const response = await api.get('/pdos/products');
    const data = response.data;
    
    tester.assertEqual(data.code, 0, '响应码应该为0');
    tester.assertNotNull(data.data, '产品数据不应为空');
    tester.assertTrue(Array.isArray(data.data), '产品数据应该是数组');
    
    if (data.data.length > 0) {
      const product = data.data[0];
      tester.assertNotNull(product.productCode, '产品编码不应为空');
      tester.assertNotNull(product.productName, '产品名称不应为空');
    }
  });

  // 3. 测试查询订单列表 - 有效参数
  await tester.runTest('查询订单列表 - 有效参数', async () => {
    const response = await api.post('/pdos/queryCenterOrderList', testData.validOrderQuery);
    const data = response.data;
    
    tester.assertEqual(data.code, 0, '响应码应该为0');
    tester.assertNotNull(data.data, '订单数据不应为空');
    tester.assertNotNull(data.data.data, '订单列表不应为空');
    tester.assertTrue(Array.isArray(data.data.data), '订单列表应该是数组');
    tester.assertNotNull(data.data.pageNum, '页码不应为空');
    tester.assertNotNull(data.data.pageSize, '页大小不应为空');
    tester.assertNotNull(data.data.total, '总数不应为空');
  });

  // 4. 测试查询订单列表 - 无效参数
  await tester.runTest('查询订单列表 - 无效参数', async () => {
    const response = await api.post('/pdos/queryCenterOrderList', testData.invalidOrderQuery);
    const data = response.data;
    
    tester.assertEqual(data.code, -1, '无效参数应该返回-1错误码');
  });

  // 5. 测试查询订单详情
  await tester.runTest('查询订单详情', async () => {
    const response = await api.post('/pdos/queryCenterOrderDetails', testData.validOrderDetail);
    const data = response.data;
    
    tester.assertEqual(data.code, 0, '响应码应该为0');
    tester.assertNotNull(data.data, '详情数据不应为空');
    
    if (data.data.provinceSteps) {
      tester.assertTrue(Array.isArray(data.data.provinceSteps), '省内步骤应该是数组');
    }
    
    if (data.data.groupVisualization) {
      tester.assertNotNull(data.data.groupVisualization, '集团可视化数据不应为空');
    }
  });

  // 6. 测试分页功能
  await tester.runTest('分页功能测试', async () => {
    // 测试第一页
    const page1Response = await api.post('/pdos/queryCenterOrderList', {
      ...testData.validOrderQuery,
      pageNum: 1,
      pageSize: 5,
    });
    
    const page1Data = page1Response.data;
    tester.assertEqual(page1Data.code, 0, '第一页响应码应该为0');
    tester.assertEqual(page1Data.data.pageNum, 1, '页码应该为1');
    tester.assertEqual(page1Data.data.pageSize, 5, '页大小应该为5');
    
    // 如果有数据，测试第二页
    if (page1Data.data.total > 5) {
      const page2Response = await api.post('/pdos/queryCenterOrderList', {
        ...testData.validOrderQuery,
        pageNum: 2,
        pageSize: 5,
      });
      
      const page2Data = page2Response.data;
      tester.assertEqual(page2Data.code, 0, '第二页响应码应该为0');
      tester.assertEqual(page2Data.data.pageNum, 2, '页码应该为2');
    }
  });

  // 7. 测试时间范围验证
  await tester.runTest('时间范围验证', async () => {
    // 测试时间范围过大的情况
    const invalidTimeRange = {
      ...testData.validOrderQuery,
      startTime: '2024-01-01 00:00:00',
      endTime: '2024-03-31 23:59:59', // 超过40天
    };
    
    const response = await api.post('/pdos/queryCenterOrderList', invalidTimeRange);
    const data = response.data;
    
    tester.assertEqual(data.code, -1, '时间范围过大应该返回-1错误码');
  });

  // 8. 测试并发请求
  await tester.runTest('并发请求测试', async () => {
    const concurrentRequests = [];
    
    // 创建5个并发请求
    for (let i = 0; i < 5; i++) {
      concurrentRequests.push(
        api.post('/pdos/queryCenterOrderList', testData.validOrderQuery)
      );
    }
    
    const responses = await Promise.all(concurrentRequests);
    
    // 验证所有请求都成功
    responses.forEach((response, index) => {
      tester.assertEqual(response.status, 200, `并发请求${index + 1}应该成功`);
      tester.assertEqual(response.data.code, 0, `并发请求${index + 1}响应码应该为0`);
    });
  });

  // 9. 测试错误处理
  await tester.runTest('错误处理测试', async () => {
    try {
      // 测试不存在的接口
      await api.get('/pdos/nonexistent');
      throw new Error('应该抛出404错误');
    } catch (error) {
      if (error.response) {
        tester.assertEqual(error.response.status, 404, '不存在的接口应该返回404');
      } else {
        throw error;
      }
    }
  });

  // 10. 测试响应时间
  await tester.runTest('响应时间测试', async () => {
    const startTime = Date.now();
    await api.post('/pdos/queryCenterOrderList', testData.validOrderQuery);
    const endTime = Date.now();
    
    const responseTime = endTime - startTime;
    tester.assertTrue(responseTime < 5000, `响应时间应该小于5秒，实际: ${responseTime}ms`);
  });

  // 打印测试结果汇总
  tester.printSummary();
  
  // 如果有失败的测试，退出码为1
  if (tester.failedTests > 0) {
    process.exit(1);
  }
}

// 主函数
async function main() {
  try {
    await runAllTests();
    console.log('\n🎉 所有测试完成！');
  } catch (error) {
    console.error('\n💥 测试执行出错:', error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = { ApiTester, testData };