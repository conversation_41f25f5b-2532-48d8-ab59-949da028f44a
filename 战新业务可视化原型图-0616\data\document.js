﻿$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,d,p,f),q,_(r,[_(s,t,u,v,w,x,y,z),_(s,A,u,B,w,x,y,C),_(s,D,u,E,w,x,y,F)]),G,[H,I,J,K],L,[M,N,O],P,_(Q,R),S,_(T,_(s,U,V,W,X,Y,Z,ba,bb,bc,bd,_(be,bf,bg,bh),bi,bj,bk,f,bl,bm,bn,ba,bo,ba,bp,bq,br,f,bs,_(bt,bu,bv,bu),bw,_(bx,bu,by,bu),bz,bA,bB,d,bC,f,bD,U,bE,_(be,bf,bg,bF),bG,_(be,bf,bg,bH),bI,bJ,bK,bf,bL,[bM],bN,bJ,bO,bP,bQ,bR,bS,bR,bT,bU,bV,bW,bX,bW,bY,bW,bZ,bW,ca,_(),cb,null,cc,null,cd,bP,ce,_(cf,f,cg,ch,ci,ch,cj,ch,ck,bu,bg,_(cl,bM,cm,bM,cn,bM,co,cp)),cq,_(cf,f,cg,bu,ci,ch,cj,ch,ck,bu,bg,_(cl,bM,cm,bM,cn,bM,co,cp)),cr,_(cf,f,cg,cs,ci,cs,cj,ch,ck,bu,bg,_(cl,bM,cm,bM,cn,bM,co,ct)),cu,cv,cw,_(cf,f,cx,cy),cz,_(cf,f,cx,cy),cA,_(cB,bM,cC,bu,cD,bq),cE,_(cF,cs,cG,cs,cH,bu,cI,bu,cJ,bu),cK,_(bt,cL,bv,cL)),cM,_(cN,_(s,cO),cP,_(s,cQ),cR,_(s,cS,bI,bP),cT,_(s,cU,bd,_(be,bf,bg,bF),bI,bP,bO,bc,bE,_(be,bf,bg,cV)),cW,_(s,cX,bi,cY,X,cZ,bI,bP,bE,_(be,bf,bg,da,bN,bu),bl,db,bT,dc,bV,bP,bX,bP,bY,bP,bZ,bP),dd,_(s,de,bi,df,X,cZ,bI,bP,bE,_(be,bf,bg,da,bN,bu),bl,db,bT,dc,bV,bP,bX,bP,bY,bP,bZ,bP),dg,_(s,dh,bi,di,X,cZ,bI,bP,bE,_(be,bf,bg,da,bN,bu),bl,db,bT,dc,bV,bP,bX,bP,bY,bP,bZ,bP),dj,_(s,dk,bi,dl,X,cZ,bI,bP,bE,_(be,bf,bg,da,bN,bu),bl,db,bT,dc,bV,bP,bX,bP,bY,bP,bZ,bP),dm,_(s,dn,X,cZ,bI,bP,bE,_(be,bf,bg,da,bN,bu),bl,db,bT,dc,bV,bP,bX,bP,bY,bP,bZ,bP),dp,_(s,dq,bi,dr,X,cZ,bI,bP,bE,_(be,bf,bg,da,bN,bu),bl,db,bT,dc,bV,bP,bX,bP,bY,bP,bZ,bP),ds,_(s,dt,bi,bj,bI,bP,bE,_(be,bf,bg,da,bN,bu),bl,db,bT,dc,bV,bP,bX,bP,bY,bP,bZ,bP),du,_(s,dv,bI,bP,bE,_(be,bf,bg,da,bN,bu),bl,db,bT,dc,bV,bP,bX,bP,bY,bP,bZ,bP),dw,_(s,dx,bd,_(be,bf,bg,dy),bl,db,bT,bU),dz,_(s,dA,bd,_(be,bf,bg,dy),bl,db,bT,dc),dB,_(s,dC),dD,_(s,dE,bd,_(be,bf,bg,dF)),dG,_(s,dH,bE,_(be,bf,bg,dI)),dJ,_(s,dK,bd,_(be,bf,bg,dF)),dL,_(s,dM,bE,_(be,bf,bg,dI)),dN,_(s,dO,bE,_(be,dP,dQ,_(bt,dR,bv,bu),dS,_(bt,dR,bv,cs),dT,[_(bg,bF,dU,bu,bN,cs),_(bg,dV,dU,bu,bN,cs),_(bg,dW,dU,cs,bN,cs),_(bg,bF,dU,cs,bN,cs)]))),dX,_()));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="useLabels",o="useViews",p="loadFeedbackPlugin",q="sitemap",r="rootNodes",s="id",t="02g8sg",u="pageName",v="战新可视化-首页",w="type",x="Wireframe",y="url",z="战新可视化-首页.html",A="gstmlu",B="可视化详情页面",C="可视化详情页面.html",D="kvr2rj",E="可视化列表页面",F="可视化列表页面.html",G="additionalJs",H="plugins/debug/debug.js",I="plugins/sitemap/sitemap.js",J="plugins/page_notes/page_notes.js",K="resources/scripts/hintmanager.js",L="additionalCss",M="plugins/debug/styles/debug.css",N="plugins/sitemap/styles/sitemap.css",O="plugins/page_notes/styles/page_notes.css",P="globalVariables",Q="onloadvariable",R="",S="stylesheet",T="defaultStyle",U="627587b6038d43cca051c114ac41ad32",V="fontName",W="\"Arial Normal\", \"Arial\", sans-serif",X="fontWeight",Y="400",Z="fontStyle",ba="normal",bb="fontStretch",bc="5",bd="foreGroundFill",be="fillType",bf="solid",bg="color",bh=0xFF333333,bi="fontSize",bj="13px",bk="underline",bl="horizontalAlignment",bm="center",bn="lineSpacing",bo="characterSpacing",bp="letterCase",bq="none",br="strikethrough",bs="location",bt="x",bu=0,bv="y",bw="size",bx="width",by="height",bz="buttonSize",bA="12",bB="visible",bC="limbo",bD="baseStyle",bE="fill",bF=0xFFFFFFFF,bG="borderFill",bH=0xFF797979,bI="borderWidth",bJ="1",bK="linePattern",bL="linePatternArray",bM=0,bN="opacity",bO="cornerRadius",bP="0",bQ="borderVisibility",bR="top right bottom left",bS="cornerVisibility",bT="verticalAlignment",bU="middle",bV="paddingLeft",bW="2",bX="paddingTop",bY="paddingRight",bZ="paddingBottom",ca="stateStyles",cb="image",cc="imageFilter",cd="rotation",ce="outerShadow",cf="on",cg="offsetX",ch=5,ci="offsetY",cj="blurRadius",ck="spread",cl="r",cm="g",cn="b",co="a",cp=0.34901960784313724,cq="innerShadow",cr="textShadow",cs=1,ct=0.6470588235294118,cu="viewOverride",cv="19e82109f102476f933582835c373474",cw="widgetBlur",cx="radius",cy=4,cz="backdropBlur",cA="transition",cB="easing",cC="duration",cD="css",cE="transform",cF="scaleX",cG="scaleY",cH="translateX",cI="translateY",cJ="rotate",cK="transformOrigin",cL=50,cM="customStyles",cN="box_1",cO="********************************",cP="_形状",cQ="40519e9ec4264601bfb12c514e4f4867",cR="_图片",cS="75a91ee5b9d042cfa01b8d565fe289c0",cT="primary_button",cU="cd64754845384de3872fb4a066432c1f",cV=0xFF1E98D7,cW="_一级标题",cX="1111111151944dfba49f67fd55eb1f88",cY="32px",cZ="bold",da=0xFFFFFF,db="left",dc="top",dd="_二级标题",de="b3a15c9ddde04520be40f94c8168891e",df="24px",dg="_三级标题",dh="8c7a4c5ad69a4369a5f7788171ac0b32",di="18px",dj="_四级标题",dk="e995c891077945c89c0b5fe110d15a0b",dl="14px",dm="_五级标题",dn="386b19ef4be143bd9b6c392ded969f89",dp="_六级标题",dq="fc3b9a13b5574fa098ef0a1db9aac861",dr="10px",ds="label",dt="2285372321d148ec80932747449c36c9",du="_段落",dv="4988d43d80b44008a4a415096f1632af",dw="text_field",dx="44157808f2934100b68f2394a66b2bba",dy=0xFF000000,dz="droplist",dA="85f724022aae41c594175ddac9c289eb",dB="table_cell",dC="33ea2511485c479dbf973af3302f2352",dD="form_hint",dE="3c35f7f584574732b5edbd0cff195f77",dF=0xFF999999,dG="form_disabled",dH="2829faada5f8449da03773b96e566862",dI=0xFFF0F0F0,dJ="_表单提示文本",dK="4889d666e8ad4c5e81e59863039a5cc0",dL="_表单禁用",dM="9bd0236217a94d89b0314c8c7fc75f16",dN="_流程图形状",dO="df01900e3c4e43f284bafec04b0864c4",dP="linearGradient",dQ="startPoint",dR=0.5,dS="endPoint",dT="stops",dU="offset",dV=0xFFF2F2F2,dW=0xFFE4E4E4,dX="duplicateStyles";
return _creator();
})());