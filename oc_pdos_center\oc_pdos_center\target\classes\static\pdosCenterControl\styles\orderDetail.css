.order-detail-page {
  font-family: 'HarmonyOS Sans', 'PingFang SC', 'Helvetica Neue', 'Arial Rounded MT Bold', 'Arial', 'sans-serif';
  padding: 0 0 40px 0;
}
.section {
  background: #fff;
  margin: 20px 0;
  border-radius: 8px;
  box-shadow: 0 2px 8px #e6f7ff;
  padding: 18px 24px 24px 24px;
  width: 100%;
  box-sizing: border-box;
}
.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #000;
  margin-bottom: 16px;
}
.flow-graph-placeholder {
  background: #e3f0fa;
  color: #1677c7;
  border-radius: 6px;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-bottom: 8px;
}
.info-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 8px;
  background: #fff;
  min-width: 1200px;
}
.info-table th, .info-table td {
  border: 1px solid #e0e0e0;
  padding: 7px 6px;
  text-align: center;
  font-size: 14px;
  color: #000;
}
.info-table th {
  background: #e3f0fa;
  color: #000;
  font-weight: 600;
}
.header {
  background: rgba(116, 190, 223, 0.608);
  padding: 10px 20px;
  font-size: 20px;
  font-weight: bold;
  color: #fff;
  border-radius: 0 0 6px 6px;
  margin-bottom: 0;
}
.flow-graph {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 10px;
  gap: 0 8px;
}
.flow-node {
  display: flex;
  align-items: center;
  background: #e3f0fa;
  border-radius: 8px;
  min-width: 60px;
  min-height: 44px;
  padding: 0 16px;
  font-size: 15px;
  color: #000;
  font-weight: 600;
  box-shadow: 0 2px 8px #e6f7ff;
  position: relative;
  justify-content: center;
}
.flow-node.blue {
  background: #b7d8f6;
  color: #000;
}
.flow-node.start {
  background: #e3f0fa;
  color: #000;
}
.flow-node.end {
  background: #e3f0fa;
  color: #000;
}
.flow-node.orange {
  background: #ffa940;
  color: #fff;
}
.flow-arrow {
  font-size: 22px;
  color: #b0b0b0;
  margin: 0 2px;
  user-select: none;
}
.icon-check {
  display: inline-block;
  width: 28px;
  height: 28px;
  background: linear-gradient(180deg, #4a90e2 0%, #1253a2 100%) !important;
  border-radius: 6px;
  margin-right: 6px;
  position: relative;
}
.icon-check::after {
  content: '';
  display: block;
  position: absolute;
  left: 8px;
  top: 6px;
  width: 12px;
  height: 12px;
  border: solid #fff;
  border-width: 0 3px 3px 0;
  transform: rotate(45deg);
}
.icon-play {
  display: inline-block;
  width: 28px;
  height: 28px;
  background: linear-gradient(180deg, #6eb6f7 0%, #3a8dde 100%);
  border-radius: 50%;
  margin-right: 6px;
  position: relative;
}
.icon-play::after {
  content: '';
  display: block;
  position: absolute;
  left: 10px;
  top: 7px;
  width: 0;
  height: 0;
  border-top: 7px solid transparent;
  border-bottom: 7px solid transparent;
  border-left: 10px solid #fff;
}
.icon-stop {
  display: inline-block;
  width: 28px;
  height: 28px;
  background: linear-gradient(180deg, #6eb6f7 0%, #3a8dde 100%);
  border-radius: 6px;
  margin-right: 6px;
  position: relative;
}
.icon-stop::after {
  content: '';
  display: block;
  position: absolute;
  left: 8px;
  top: 8px;
  width: 12px;
  height: 12px;
  background: #fff;
  border-radius: 2px;
}
.flow-status-legend {
  margin-top: 8px;
  display: flex;
  gap: 18px;
  font-size: 14px;
  align-items: center;
}
.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
}
.legend-box {
  display: inline-block;
  width: 18px;
  height: 18px;
  border-radius: 4px;
  margin-right: 2px;
  vertical-align: middle;
}
.legend-box.gray { background: #d1d1d1; }
.legend-box.green { background: #b8e6b8; }
.legend-box.blue { background: #a8d8ff; }
.legend-box.red { background: #ffb3b3; }

/* 图例图标样式 */
.legend-icon {
  width: 20px;
  height: 20px;
  margin-right: 6px;
  vertical-align: middle;
  display: inline-block;
  border-radius: 3px;
}

.customer-plan-section .flow-graph,
.group-flow-section .flow-graph {
  justify-content: center;
}
/* 端到端流程图样式优化 */
.end2end-flow-section .flow-graph {
  justify-content: center;
  padding: 20px;
  overflow: visible;
}
/* 移除端到端流程图flow-graph的滚动条样式，因为滚动条现在只在#end2endFlow容器上 */
.section-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 10px;
  width: 100%;
  box-sizing: border-box;
  min-height: 0 !important;
  height: 31px !important;
  padding: 0 !important;
  margin-bottom: 0 !important;
  display: flex;
  align-items: center;
}

/* 端到端流程图section-header特殊样式 */
.end2end-flow-section .section-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 10px;
  width: 100%;
  box-sizing: border-box;
  max-width: 1368px;
  margin-left: auto;
  margin-right: auto;
}
.end2end-flow-section .flow-node {
  min-width: 50px;
  min-height: 36px;
  padding: 0 12px;
  font-size: 13px;
}
.end2end-flow-section .icon-check,
.end2end-flow-section .icon-play,
.end2end-flow-section .icon-stop {
  width: 24px;
  height: 24px;
  min-width: 24px;
  min-height: 24px;
  margin-right: 5px;
}
.end2end-flow-section .icon-check::after {
  left: 6px;
  top: 5px;
  width: 10px;
  height: 10px;
  border-width: 0 2px 2px 0;
}
.end2end-flow-section .icon-play::after {
  left: 8px;
  top: 6px;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-left: 8px solid #fff;
}
.end2end-flow-section .icon-stop::after {
  left: 6px;
  top: 6px;
  width: 10px;
  height: 10px;
  border-radius: 2px;
}

.customer-plan-section .flow-node,
.group-flow-section .flow-node {
  min-width: 44px;
  min-height: 32px;
  padding: 0 8px;
  font-size: 13px;
}
.customer-plan-section .icon-check,
.customer-plan-section .icon-play,
.customer-plan-section .icon-stop,
.group-flow-section .icon-check,
.group-flow-section .icon-play,
.group-flow-section .icon-stop {
  width: 20px;
  height: 20px;
  min-width: 20px;
  min-height: 20px;
  margin-right: 4px;
}
.customer-plan-section .icon-check::after,
.group-flow-section .icon-check::after {
  left: 5px;
  top: 4px;
  width: 8px;
  height: 8px;
  border-width: 0 2px 2px 0;
}
.customer-plan-section .icon-play::after,
.group-flow-section .icon-play::after {
  left: 7px;
  top: 4px;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 7px solid #fff;
}
.customer-plan-section .icon-stop::after,
.group-flow-section .icon-stop::after {
  left: 5px;
  top: 5px;
  width: 8px;
  height: 8px;
  border-radius: 2px;
}
.table-scroll-x {
  overflow-x: auto;
  width: 100%;
}
.btn {
  padding: 8px 16px;
  border-radius: 4px;
  border: 1px solid #1677c7;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #1677c7;
  color: #fff;
  border: 1px solid #1677c7;
}

.btn-primary:hover {
  background-color: #0e5aa3;
}

/* 集团编排流程列表 派单报文、报文响应列固定宽度，超出隐藏，悬浮显示全部 */
.info-table td.dispatch-msg, .info-table td.response-msg {
    max-width: 180px;
    min-width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    position: relative;
}
.info-table td.dispatch-msg:hover, .info-table td.response-msg:hover {
    background: #f5faff;
}
.info-table td.dispatch-msg[title], .info-table td.response-msg[title] {
    /* 兼容IE8+，title属性自动显示原文 */
}

/* 集团编排流程列表异常行样式 */
.info-table tr.error td {
    color: #b80000 !important;
}

/* 流程图节点闪烁动画 */
@keyframes nodeFlash {
    0% { background: #52c41a; }
    50% { background: #95de64; }
    100% { background: #52c41a; }
}

/* 兼容IE9及以下 */
@-webkit-keyframes nodeFlash {
    0% { background: #52c41a; }
    50% { background: #95de64; }
    100% { background: #52c41a; }
}

.flow-node.processing {
    animation: nodeFlash 1.5s infinite;
    -webkit-animation: nodeFlash 1.5s infinite;
    color: #fff;
}

/* 流程节点不同状态下的样式 */
.flow-node.gray { color: #000 !important; background: #d3d3d3 !important; }
.flow-node.blue { color: #000 !important; background: #a8d8ff !important; }
.flow-node.processing { color: #000 !important; background: #b8e6b8 !important; }
.flow-node.error { color: #000 !important; background: #ffb3b3 !important; }

/* 异常状态：红色叉叉 */
.icon-error {
  display: inline-block;
  width: 28px;
  height: 28px;
  background: linear-gradient(180deg, #ff2d2d 0%, #b80000 100%) !important;
  border-radius: 6px;
  margin-right: 6px;
  position: relative;
}
.icon-error::after, .icon-error::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 13px;
  width: 12px;
  height: 3px;
  background: #fff;
  border-radius: 2px;
}
.icon-error::after {
  transform: rotate(45deg);
}
.icon-error::before {
  transform: rotate(-45deg);
}

/* 正常处理中：绿色方框，无对勾 */
.icon-processing {
  display: inline-block;
  width: 28px;
  height: 28px;
  background: linear-gradient(180deg, #52c41a 0%, #237804 100%) !important;
  border-radius: 6px;
  margin-right: 6px;
  position: relative;
}

.node-icon {
  width: 28px;
  height: 28px;
  vertical-align: middle;
  margin-right: 6px;
} 

/* 区块标题蓝色标志样式 */
.section-title {
  position: relative;
  padding-left: 15px;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: bold;
  color: #000;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 15px;
  background-color: #2196F3;
  border-radius: 1px;
}

/* 为特定区块添加蓝色标志 */
.customer-info-section .section-title::before,
.network-topology-section .section-title::before,
.customer-plan-section .section-title::before,
.end2end-flow-section .section-title::before,
.group-flow-section .section-title::before {
  background-color: #2196F3;
}

/* 确保标题有足够的左边距来容纳蓝色条 */
.customer-info-section h3,
.network-topology-section h3,
.customer-plan-section h3,
.end2end-flow-section h3,
.group-flow-section h3 {
  position: relative;
  padding-left: 15px;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: bold;
  color: #000;
}

.customer-info-section h3::before,
.network-topology-section h3::before,
.customer-plan-section h3::before,
.end2end-flow-section h3::before,
.group-flow-section h3::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 15px;
  background-color: #2196F3;
  border-radius: 1px;
} 

/* 工单信息区块样式优化 */
.order-info-section {
    background: #fff;
    border-radius: 8px;
    padding: 18px 12px 24px 12px;
    margin: 20px 0 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: none;
    margin-left: 0 !important;
    margin-right: 0 !important;
    box-sizing: border-box;
}

.order-info-grid {
    display: grid;
    grid-template-columns: repeat(4, minmax(0, 1fr));
    gap: 12px 16px;
    margin-top: 16px;
}

.info-item {
    display: flex;
    align-items: center;
    min-height: 32px;
    position: relative;
    gap: 4px; /* 字段紧挨着输入框 */
}

.info-item label {
    flex-shrink: 0;
    width: 120px; /* 增加宽度以适应字段名 */
    text-align: right; /* 右对齐，让冒号对齐 */
    color: rgb(144, 144, 144);
    font-size: 14px;
    font-weight: 400; /* 不加粗 */
    line-height: 32px;
    position: relative;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: flex-end; /* 右对齐 */
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 120px;
    max-width: 120px;
}

.info-item label::after {
    content: ':';
    color: #000;
    font-weight: 400; /* 冒号也不加粗 */
    margin-left: 4px; /* 减少间距 */
    flex-shrink: 0;
    min-width: 4px;
}

.info-item input {
    flex: 1;
    text-align: left;
    color: #333;
    font-size: 14px;
    line-height: 24px; /* 调整为与height一致 */
    word-break: break-all;
    overflow-x: auto; /* 允许水平滚动 */
    overflow-y: hidden; /* 隐藏垂直滚动 */
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 400;
    min-width: 0; /* 允许flex项目收缩 */
    border: 1px solid #d9d9d9; /* 输入框边框 */
    border-radius: 4px; /* 圆角 */
    padding: 4px 8px; /* 内边距 */
    height: 24px; /* 固定高度 */
    display: flex;
    align-items: center;
    justify-content: flex-start; /* 确保左对齐 */
    box-sizing: border-box;
    cursor: text; /* 显示文本光标 */
    user-select: text; /* 允许文本选择 */
    -webkit-user-select: text; /* Safari支持 */
    -moz-user-select: text; /* Firefox支持 */
    -ms-user-select: text; /* IE支持 */
    transition: border-color 0.2s ease; /* 边框颜色过渡效果 */
    background: transparent; /* 透明背景 */
    outline: none; /* 移除默认焦点轮廓 */
}



/* 输入框点击时的样式 */
.info-item input:focus {
    outline: none; /* 移除默认焦点轮廓 */
    border-color: #1890ff; /* 点击时边框变蓝 */
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2); /* 添加焦点阴影 */
}

/* 输入框悬停时的样式 */
.info-item input:hover {
    border-color: #40a9ff; /* 悬停时边框颜色 */
}

/* 自定义滚动条样式 */
.info-item input::-webkit-scrollbar {
    height: 4px; /* 水平滚动条高度 */
}

.info-item input::-webkit-scrollbar-track {
    background: #f1f1f1; /* 滚动条轨道背景 */
    border-radius: 2px;
}

.info-item input::-webkit-scrollbar-thumb {
    background: #c1c1c1; /* 滚动条滑块颜色 */
    border-radius: 2px;
}

.info-item input::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8; /* 悬停时滑块颜色 */
}

.order-info-grid .info-item input {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
  cursor: text;
}

/* 响应式布局 */
@media (max-width: 1200px) {
    .order-info-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 900px) {
    .order-info-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 600px) {
    .order-info-grid {
        grid-template-columns: 1fr;
    }
    
    .info-item label {
        width: 120px;
    }
} 

/* 异常行高亮样式 */
.error-row {
    background-color: #fff5f5 !important;
    border-left: 4px solid #ffb3b3 !important;
    box-shadow: 0 1px 3px rgba(255, 179, 179, 0.3) !important;
}

.error-row td {
    color: #d32f2f !important;
    font-weight: 500 !important;
}

.error-row:hover {
    background-color: #ffe6e6 !important;
    box-shadow: 0 2px 6px rgba(255, 179, 179, 0.4) !important;
}

.error {
    background-color: #fff5f5 !important;
    border-left: 4px solid #ffb3b3 !important;
}

.error td {
    color: #d32f2f !important;
    font-weight: bold !important;
}

.error:hover {
    background-color: #ffe6e6 !important;
}

/* 查询中悬浮框样式 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    background: white;
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    text-align: center;
    min-width: 200px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #333;
    font-size: 16px;
    font-weight: 500;
    margin: 0;
} 

/* 表格布局 */
.table-header, .table-body {
  table-layout: fixed;
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
}

/* 表头样式 - 使用原来的背景颜色 */
th {
  position: relative;
  background: #e3f0fa !important;
  font-weight: 600;
  color: #222 !important;
  height: 35px;
  line-height: 35px;
  text-align: center;
  border: 1px solid #e0e0e0;
  font-size: 13px;
  padding: 0 5px;
  box-sizing: border-box;
  vertical-align: middle;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 确保表头表格高度与包装器一致 */
.table-header {
  height: 35px;
  line-height: 35px;
  margin: 0;
  padding: 0;
  border-spacing: 0;
  border-collapse: collapse;
}

/* 表格内容样式 */
td {
  border: 1px solid #e0e0e0;
  text-align: center;
  font-size: 13px;
  height: 32px !important;
  line-height: 32px !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 5px;
  box-sizing: border-box;
  vertical-align: middle;
  max-height: 32px !important;
  min-height: 32px !important;
  color: rgb(76, 76, 76);
}

/* 确保表体表格高度与包装器一致 */
.table-body {
  height: auto;
  line-height: 32px;
  margin: 0;
  padding: 0;
  border-spacing: 0;
  border-collapse: collapse;
}

/* 确保表格单元格边框完全连接，消除任何间隙 */
.table-header th {
  border-bottom: 1px solid #e0e0e0;
}

.table-body td {
  border-top: none;
}

/* 确保表格行之间没有间隙 */
.table-header tr,
.table-body tr {
  margin: 0;
  padding: 0;
  border-spacing: 0;
}

/* 确保表格容器没有间隙 */
.table-header,
.table-body {
  margin: 0;
  padding: 0;
  border-spacing: 0;
  border-collapse: collapse;
}

/* 强制表格行高度 */
.table-body tr {
  height: 32px !important;
  max-height: 32px !important;
  min-height: 32px !important;
}

/* 强制表格容器高度 */
.table-body {
  height: auto !important;
  max-height: none !important;
}

/* 表格包装器样式 */
.table-body-wrapper .table-body {
  margin-top: 0;
}

/* 列宽调整器样式 */
.col-resize {
  position: absolute;
  right: 0;
  top: 0;
  width: 4px;
  height: 100%;
  cursor: col-resize;
  background: transparent;
  transition: background 0.2s;
}

.col-resize:hover {
  background: #1677c7;
}

.col-resize:active {
  background: #0e5aa3;
}

.table-header th:hover .col-resize {
  background: #e6f7ff;
}



.table-body-wrapper table {
  margin: 0;
  border-collapse: collapse;
  min-width: 100%;
  border-top: none;
}

.table-body-wrapper::-webkit-scrollbar {
  height: 8px;
  width: 8px;
  background: #f0f6fa;
}



.table-body-wrapper::-webkit-scrollbar-track {
  background: #f0f6fa;
}

.table-body-wrapper::-webkit-scrollbar-thumb {
  background: #b7d8f6;
  border-radius: 4px;
}

.table-body-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8d8ff;
}



/* 表格区域样式 */
.table-section {
  margin-top: 16px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px #e6f7ff;
  overflow: hidden;
  border: none;
}

/* 确保表头和表体紧密连接 */
.table-header {
  margin-bottom: 0;
  border-bottom: none;
}

.table-body {
  margin-top: 0;
  border-top: none;
}

/* 移除表格之间的默认间距 */
.table-header-wrapper,
.table-body-wrapper {
  margin: 0;
  padding: 0;
}

/* 确保表格行之间没有间隙 */
.table-header tr,
.table-body tr {
  margin: 0;
  padding: 0;
}

/* 彻底消除表头和表体之间的过渡区域 */
.table-header-wrapper {
  border-bottom: none;
}

.table-body-wrapper {
  border-top: none;
}

/* 确保表格单元格边框连接 */
.table-header th {
  border-bottom: 1px solid #e0e0e0;
}

.table-body td {
  border-top: none;
}

/* 移除表格容器的任何额外间距 */
.table-header,
.table-body {
  margin: 0;
  padding: 0;
  border-spacing: 0;
  border-collapse: collapse;
}

/* 确保表格区域没有额外的边框 */
.table-section {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow: hidden;
}

/* 表头包装器边框设置 */
.table-header-wrapper {
  border: 1px solid #e0e0e0;
  border-bottom: none;
  border-radius: 6px 6px 0 0;
  margin-bottom: 0;
  line-height: 0;
  font-size: 0;
  height: 35px;
  overflow-x: hidden;
  overflow-y: hidden;
}

/* 表体包装器边框设置 */
.table-body-wrapper {
  border: 1px solid #e0e0e0;
  border-top: none;
  border-radius: 0 0 6px 6px;
  margin-top: 0;
  line-height: 0;
  font-size: 0;
  overflow-x: auto;
  overflow-y: auto;
  max-height: 400px;
  scrollbar-width: thin;
  scrollbar-color: #b7d8f6 #f0f6fa;
}

/* 确保表格单元格边框完全连接 */
.table-header th:last-child {
  border-right: 1px solid #e0e0e0;
}

.table-header th:first-child {
  border-left: 1px solid #e0e0e0;
}

.table-body td:last-child {
  border-right: 1px solid #e0e0e0;
}

.table-body td:first-child {
  border-left: 1px solid #e0e0e0;
}

/* 确保表格行边框连接 */
.table-header tr:first-child th {
  border-top: 1px solid #e0e0e0;
}

.table-body tr:last-child td {
  border-bottom: 1px solid #e0e0e0;
}

/* 无数据提示 */
.no-data-tip {
  text-align: center;
  padding: 40px 20px;
  color: #999;
  font-size: 14px;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-top: none;
}

.flow-graph {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 !important;
    min-height: 0 !important;
    height: 90px !important;
}

#customerPlanFlow,
#end2endFlow,
#groupFlow {
    height: 92px !important;
    min-height: 92px !important;
    max-height: 92px !important;
    display: flex;
    align-items: center;
    justify-content: center;
}

#customerPlanFlow svg {
    width: 100% !important;
    height: 90px !important;
    min-height: 90px !important;
    max-height: 90px !important;
    display: block;
    margin: 0 auto;
    align-self: center;
}

/* 确保端到端流程图容器有足够的空间 */
.end2end-flow-section {
    padding: 0;
    min-height: 175px;
    width: 100%;
    box-sizing: border-box;
    max-width: 1368px;
    margin-left: auto;
    margin-right: auto;
    overflow: visible;
}

.end2end-flow-section .flow-graph {
    min-width: 100%;
    justify-content: center;
    padding: 20px;
}

/* 确保zflow绘制的流程图能够正确显示 */
#end2endFlow svg {
    min-width: 100%;
    width: auto;
    height: 100%;
}

/* 移除页面滚动条 */
.order-detail-page {
    overflow-x: hidden;
}

/* zflow流程图滚动条样式 */
#customerPlanFlow::-webkit-scrollbar,
#end2endFlow::-webkit-scrollbar,
#groupFlow::-webkit-scrollbar {
    height: 8px;
}

#customerPlanFlow::-webkit-scrollbar-track,
#end2endFlow::-webkit-scrollbar-track,
#groupFlow::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

#customerPlanFlow::-webkit-scrollbar-thumb,
#end2endFlow::-webkit-scrollbar-thumb,
#groupFlow::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

#customerPlanFlow::-webkit-scrollbar-thumb:hover,
#end2endFlow::-webkit-scrollbar-thumb:hover,
#groupFlow::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}