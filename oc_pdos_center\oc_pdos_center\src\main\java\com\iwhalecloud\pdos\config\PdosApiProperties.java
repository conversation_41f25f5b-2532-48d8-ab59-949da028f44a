package com.iwhalecloud.pdos.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import java.util.Map;

@Configuration
@ConfigurationProperties(prefix = "pdos.center")
public class PdosApiProperties {
    private Map<String, ApiConfig> apis;

    public Map<String, ApiConfig> getApis() {
        return apis;
    }
    public void setApis(Map<String, ApiConfig> apis) {
        this.apis = apis;
    }

    public static class ApiConfig {
        private String url;
        private Map<String, String> headers;
        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
        public Map<String, String> getHeaders() { return headers; }
        public void setHeaders(Map<String, String> headers) { this.headers = headers; }
    }
} 