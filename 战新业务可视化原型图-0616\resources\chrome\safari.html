﻿<html>
    <head>
        <title>Axure RP - Safari Local File Restrictions</title>
        <style type="text/css">
            * {
                font-family: "Source Sans Pro", sans-serif;
            }

            body {
                text-align: center;
                background-color: #f7fafc;
                color: #37465e;
            }

            p {
                font-size: 14px;
                line-height: 18px;
                color: #333333;
            }

            div.container {
                width: 980px;
                margin-left: auto;
                margin-right: auto;
                text-align: left;
            }

            a {
                text-decoration: none;
                color: #ffffff;
            }

            .button {
                display: block;
                width: 240px;
                height: 30px;
                padding: 2px 8px 2px 8px;
                border-radius: 4px;
                background-color: #3eacef;
                box-sizing: border-box;
                font-family: "Source Sans Pro SemiBold", "Source Sans Pro", sans-serif;
                font-weight: 600;
                color: #ffffff;
                text-align: center;
                font-size: 14px;
                line-height: 28px;
            }

            a:hover.button {
                background-color: #1482c5;
            }

            div.buttonContainer {
                text-align: center;
            }

            .header {
                font-size: 22px;
                color: #333333;
                line-height: 50px;
                font-weight: bold;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <br />
            <br />
            <img src="axure_logo.png" alt="axure" style="width: 100px" />
            <br />
            <br />
            <p class="header">
                Safari Local File Restrictions
            </p>
            <p>
                To view locally stored projects in Safari, you will need to <b>Disable Local File Restrictions</b>. Alternatively,
                you can upload your RP file to <a href="https://www.axure.cloud" style="color: #53ADE9">Axure Cloud</a> or use a different browser. You can also Preview from Axure RP.
            </p>
            <img src="preview-rp.png" alt="preview" />
            <p class="header">
                View local projects in Safari
            </p>
            <div>
                <p>
                    1. Open <b>Safari → Preferences → Advanced</b> from the top menu, and check the option to <b>Show develop menu in menu bar</b>
                </p>
                <img src="safari_advanced.png" alt="advanced" />
            </div>
            <div style="clear: both; height: 20px;">&nbsp;</div>
            <div class="">
                <p>
                    2. In the Develop menu, click <b>Develop → Disable Local File Restrictions</b> to un-select the menu option.
                </p>
                <img src="safari_restrictions.png" alt="extensions" />
            </div>
            <div style="clear: both; height: 20px;">&nbsp;</div>
            <p>
                3. Click the button below
            </p>
            <div class="buttonContainer">
                <a class="button" href="../../start.html">View in Safari</a>
            </div>
            <p class="header">
                Need private or on-premises hosting?
            </p>
            <p>
                Axure Cloud for Business gives you all of the features of Axure Cloud plus control of accounts and permissions. For hosting behind your firewall, check out Axure Cloud On-Premises.
            </p>
            <div class="buttonContainer">
                <a class="button" href="https://www.axure.com/axure-cloud#hosted">Learn more</a>
            </div>
            <p class="header">
                We're here to help
            </p>
            <p>
                If you have any questions, please email us at <a href="mailto:<EMAIL>" style="color: #53ADE9 "><EMAIL></a>.
            </p>
            <div style="clear: both; height: 20px;"></div>
        </div>
    </body>
</html>
