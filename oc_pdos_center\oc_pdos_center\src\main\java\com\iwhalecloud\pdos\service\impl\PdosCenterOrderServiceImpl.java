package com.iwhalecloud.pdos.service.impl;

import com.iwhalecloud.pdos.dto.PageResult;
import com.iwhalecloud.pdos.entity.Order;
import com.iwhalecloud.pdos.entity.ProcessStep;
import com.iwhalecloud.pdos.repository.OrderRepository;
import com.iwhalecloud.pdos.repository.ProcessStepRepository;
import com.iwhalecloud.pdos.service.PdosCenterOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@Slf4j
public class PdosCenterOrderServiceImpl implements PdosCenterOrderService {

    @Autowired
    private OrderRepository orderRepository;
    
    @Autowired
    private ProcessStepRepository processStepRepository;

    @Override
    public PageResult<Order> queryCenterOrderList(
            String orderNo, String custOrderNo, String productName, String customerName,
            Integer orderStatus, String provinceCode, String businessType,
            LocalDateTime startTime, LocalDateTime endTime, int pageNum, int pageSize) {
        
        try {
            // 创建分页对象（Spring Data页码从0开始）
            Pageable pageable = PageRequest.of(pageNum - 1, pageSize, 
                Sort.by(Sort.Direction.DESC, "createTime"));
            
            // 调用Repository的多条件查询方法
            Page<Order> page = orderRepository.findByConditions(
                orderNo, custOrderNo, productName, customerName, orderStatus,
                provinceCode, businessType, startTime, endTime, pageable);
            
            return PageResult.of(page);
        } catch (Exception e) {
            log.error("查询订单列表失败", e);
            return PageResult.empty(pageNum, pageSize);
        }
    }

    @Override
    public Map<String, Object> queryCenterOrderDetails(String orderNo, Long orderId) {
        try {
            log.info("查询订单详情 - orderNo: {}, orderId: {}", orderNo, orderId);
            Order order = null;
            
            // 根据订单号或订单ID查询订单
            if (StringUtils.hasText(orderNo)) {
                log.info("根据订单号查询: {}", orderNo);
                order = orderRepository.findByOrderNo(orderNo).orElse(null);
                log.info("查询结果: {}", order != null ? "找到订单" : "订单不存在");
            } else if (orderId != null) {
                log.info("根据订单ID查询: {}", orderId);
                order = orderRepository.findById(orderId).orElse(null);
                log.info("查询结果: {}", order != null ? "找到订单" : "订单不存在");
            }
            
            if (order == null) {
                log.error("订单不存在 - orderNo: {}, orderId: {}", orderNo, orderId);
                throw new RuntimeException("订单不存在");
            }
            
            log.info("找到订单: {}", order.getOrderNo());
            
            // 查询订单的流程步骤
            List<ProcessStep> processSteps = processStepRepository.findByOrderIdOrderByStepOrder(order.getId());
            
            // 按stepType分组流程步骤
            Map<String, List<ProcessStep>> groupedSteps = new HashMap<>();
            groupedSteps.put("PROVINCE_CRM", new ArrayList<>());
            groupedSteps.put("PROVINCE_ORCHESTRATION", new ArrayList<>());
            groupedSteps.put("GROUP_ORCHESTRATION", new ArrayList<>());
            groupedSteps.put("PROVINCE_DISPATCH", new ArrayList<>());
            
            for (ProcessStep step : processSteps) {
                String stepType = step.getStepType();
                if (stepType != null && groupedSteps.containsKey(stepType)) {
                    groupedSteps.get(stepType).add(step);
                }
            }
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("order", order);
            result.put("processSteps", processSteps);
            result.put("groupedProcessSteps", groupedSteps);
            
            // 统计步骤信息
            long completedSteps = processSteps.stream().filter(step -> step.getStepStatus() == 2).count();
            long totalSteps = processSteps.size();
            result.put("completedSteps", completedSteps);
            result.put("totalSteps", totalSteps);
            result.put("progress", totalSteps > 0 ? (double) completedSteps / totalSteps * 100 : 0);
            
            return result;
        } catch (Exception e) {
            log.error("查询订单详情失败", e);
            throw new RuntimeException("查询订单详情失败: " + e.getMessage());
        }
    }
    
    @Override
    public Map<String, Object> getStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 订单状态统计
            List<Object[]> statusStats = orderRepository.countByOrderStatus();
            Map<String, Long> statusMap = new HashMap<>();
            for (Object[] stat : statusStats) {
                Integer status = (Integer) stat[0];
                Long count = (Long) stat[1];
                String statusName = getOrderStatusName(status);
                statusMap.put(statusName, count);
            }
            statistics.put("orderStatusStats", statusMap);
            
            // 省份统计
            List<Object[]> provinceStats = orderRepository.countByProvince();
            Map<String, Long> provinceMap = new HashMap<>();
            for (Object[] stat : provinceStats) {
                String province = (String) stat[0];
                Long count = (Long) stat[1];
                provinceMap.put(province, count);
            }
            statistics.put("provinceStats", provinceMap);
            
            // 产品统计
            List<Object[]> productStats = orderRepository.countByProduct();
            Map<String, Long> productMap = new HashMap<>();
            for (Object[] stat : productStats) {
                String product = (String) stat[0];
                Long count = (Long) stat[1];
                productMap.put(product, count);
            }
            statistics.put("productStats", productMap);
            
            // 今日和本月新增订单数
            Long todayCount = orderRepository.countTodayOrders();
            Long monthCount = orderRepository.countThisMonthOrders();
            statistics.put("todayOrders", todayCount != null ? todayCount : 0);
            statistics.put("monthOrders", monthCount != null ? monthCount : 0);
            
            // 总订单数
            long totalOrders = orderRepository.count();
            statistics.put("totalOrders", totalOrders);
            
            return statistics;
        } catch (Exception e) {
            log.error("获取统计数据失败", e);
            throw new RuntimeException("获取统计数据失败: " + e.getMessage());
        }
    }
    
    @Override
    public Order createOrder(Order order) {
        try {
            // 设置创建时间
            order.setCreateTime(LocalDateTime.now());
            order.setUpdateTime(LocalDateTime.now());
            
            // 生成订单号（如果没有）
            if (!StringUtils.hasText(order.getOrderNo())) {
                order.setOrderNo(generateOrderNo());
            }
            
            return orderRepository.save(order);
        } catch (Exception e) {
            log.error("创建订单失败", e);
            throw new RuntimeException("创建订单失败: " + e.getMessage());
        }
    }
    
    @Override
    public void updateOrderStatus(Long orderId, Integer status) {
        try {
            Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new RuntimeException("订单不存在"));
            
            order.setOrderStatus(status);
            order.setUpdateTime(LocalDateTime.now());
            
            if (status == 2) { // 已完成
                order.setCompleteTime(LocalDateTime.now());
            }
            
            orderRepository.save(order);
        } catch (Exception e) {
            log.error("更新订单状态失败", e);
            throw new RuntimeException("更新订单状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return "ORD" + System.currentTimeMillis();
    }
    
    /**
     * 获取订单状态名称
     */
    private String getOrderStatusName(Integer status) {
        switch (status) {
            case 0: return "待处理";
            case 1: return "处理中";
            case 2: return "已完成";
            case 3: return "已取消";
            case 4: return "异常";
            default: return "未知";
        }
    }

    @Override
    public Page<Order> queryProvinceOrderList(String productCode, String businessNumber, 
                                            String orderNumber, String accessNumber, 
                                            String productInstanceId, String provinceCode, 
                                            String cityCode, String startTime, String endTime, 
                                            int pageNum, int pageSize) {
        log.info("查询新业务省内段订单列表信息 - 产品编码: {}, 业务号码: {}, 订单流水号: {}, 接入号: {}, 产品实例ID: {}, 省份: {}, 地市: {}, 时间范围: {} - {}", 
                productCode, businessNumber, orderNumber, accessNumber, productInstanceId, provinceCode, cityCode, startTime, endTime);
        
        try {
            // 构建查询条件
            Specification<Order> spec = (root, query, criteriaBuilder) -> {
                List<Predicate> predicates = new ArrayList<>();
                
                // 产品编码过滤
                if (StringUtils.hasText(productCode)) {
                    predicates.add(criteriaBuilder.equal(root.get("productId"), productCode));
                }
                
                // 业务号码过滤
                if (StringUtils.hasText(businessNumber)) {
                    predicates.add(criteriaBuilder.like(root.get("customerOrderNo"), "%" + businessNumber + "%"));
                }
                
                // 订单流水号过滤
                if (StringUtils.hasText(orderNumber)) {
                    predicates.add(criteriaBuilder.like(root.get("orderNo"), "%" + orderNumber + "%"));
                }
                
                // 接入号过滤
                if (StringUtils.hasText(accessNumber)) {
                    predicates.add(criteriaBuilder.like(root.get("customerOrderNo"), "%" + accessNumber + "%"));
                }
                
                // 产品实例ID过滤
                if (StringUtils.hasText(productInstanceId)) {
                    predicates.add(criteriaBuilder.like(root.get("description"), "%" + productInstanceId + "%"));
                }
                
                // 省份编码过滤
                if (StringUtils.hasText(provinceCode)) {
                    predicates.add(criteriaBuilder.equal(root.get("provinceCode"), provinceCode));
                }
                
                // 地市编码过滤
                if (StringUtils.hasText(cityCode)) {
                    predicates.add(criteriaBuilder.equal(root.get("cityCode"), cityCode));
                }
                
                // 时间范围过滤
                if (StringUtils.hasText(startTime)) {
                    try {
                        LocalDateTime start = LocalDateTime.parse(startTime + " 00:00:00", 
                                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime"), start));
                    } catch (Exception e) {
                        log.warn("开始时间格式错误: {}", startTime);
                    }
                }
                
                if (StringUtils.hasText(endTime)) {
                    try {
                        LocalDateTime end = LocalDateTime.parse(endTime + " 23:59:59", 
                                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createTime"), end));
                    } catch (Exception e) {
                        log.warn("结束时间格式错误: {}", endTime);
                    }
                }
                
                return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
            };
            
            // 分页查询
            Pageable pageable = PageRequest.of(pageNum - 1, pageSize, Sort.by(Sort.Direction.DESC, "createTime"));
            Page<Order> result = orderRepository.findAll(spec, pageable);
            
            log.info("查询完成，共找到 {} 条记录", result.getTotalElements());
            return result;
            
        } catch (Exception e) {
            log.error("查询新业务省内段订单列表信息失败", e);
            throw new RuntimeException("查询订单列表失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> queryProvinceOrderDetails(String productCode, String businessNumber, 
                                                       String orderNumber, String accessNumber, 
                                                       String productInstanceId, String provinceCode, 
                                                       String cityCode, String startTime, String endTime) {
        log.info("查询新业务省内段环节信息 - 产品编码: {}, 业务号码: {}, 订单流水号: {}, 接入号: {}, 产品实例ID: {}, 省份: {}, 地市: {}, 时间范围: {} - {}", 
                productCode, businessNumber, orderNumber, accessNumber, productInstanceId, provinceCode, cityCode, startTime, endTime);
        
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 1. 查询省内环节信息
            List<Order> orders = findOrdersByConditions(productCode, businessNumber, orderNumber, 
                    accessNumber, productInstanceId, provinceCode, cityCode, startTime, endTime);
            
            if (orders.isEmpty()) {
                result.put("provinceSteps", new ArrayList<>());
                result.put("groupVisualizationData", new HashMap<>());
                result.put("message", "未找到匹配的订单信息");
                return result;
            }
            
            // 取第一个匹配的订单作为主要订单
            Order mainOrder = orders.get(0);
            
            // 2. 获取省内环节步骤信息
            List<Map<String, Object>> provinceSteps = getProvinceSteps(mainOrder);
            result.put("provinceSteps", provinceSteps);
            
            // 3. 查询集团战新可视化数据
            Map<String, Object> groupData = queryGroupVisualizationData(productCode, businessNumber, 
                    orderNumber, accessNumber, productInstanceId, provinceCode, cityCode, startTime, endTime);
            result.put("groupVisualizationData", groupData);
            
            // 4. 添加订单基本信息
            result.put("orderInfo", mainOrder);
            result.put("totalOrders", orders.size());
            
            log.info("查询新业务省内段环节信息完成，订单数: {}", orders.size());
            return result;
            
        } catch (Exception e) {
            log.error("查询新业务省内段环节信息失败", e);
            throw new RuntimeException("查询环节信息失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> queryGroupVisualizationData(String productCode, String businessNumber, 
                                                          String orderNumber, String accessNumber, 
                                                          String productInstanceId, String provinceCode, 
                                                          String cityCode, String startTime, String endTime) {
        log.info("查询集团战新可视化PG库数据 - 产品编码: {}, 业务号码: {}, 订单流水号: {}, 接入号: {}, 产品实例ID: {}, 省份: {}, 地市: {}, 时间范围: {} - {}", 
                productCode, businessNumber, orderNumber, accessNumber, productInstanceId, provinceCode, cityCode, startTime, endTime);
        
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 模拟集团战新可视化数据（实际应该调用集团接口）
            // TODO: 实际实现时需要调用集团战新可视化PG库接口
            
            // 端到端流程信息
            List<Map<String, Object>> endToEndProcess = new ArrayList<>();
            endToEndProcess.add(createProcessStep("订单受理", "2", "已完成", "2024-01-15 10:00:00"));
            endToEndProcess.add(createProcessStep("资源预占", "2", "已完成", "2024-01-15 10:30:00"));
            endToEndProcess.add(createProcessStep("工程设计", "1", "处理中", "2024-01-15 11:00:00"));
            endToEndProcess.add(createProcessStep("工程施工", "0", "待处理", null));
            endToEndProcess.add(createProcessStep("业务开通", "0", "待处理", null));
            result.put("endToEndProcess", endToEndProcess);
            
            // 客户规划流程信息
            List<Map<String, Object>> customerPlanProcess = new ArrayList<>();
            customerPlanProcess.add(createProcessStep("需求分析", "2", "已完成", "2024-01-15 09:00:00"));
            customerPlanProcess.add(createProcessStep("方案设计", "2", "已完成", "2024-01-15 09:30:00"));
            customerPlanProcess.add(createProcessStep("方案评审", "1", "处理中", "2024-01-15 10:00:00"));
            customerPlanProcess.add(createProcessStep("合同签署", "0", "待处理", null));
            result.put("customerPlanProcess", customerPlanProcess);
            
            // 流程列表信息
            List<Map<String, Object>> processList = new ArrayList<>();
            processList.add(createProcessListItem("省内受理流程", "2", "已完成"));
            processList.add(createProcessListItem("集团审批流程", "1", "处理中"));
            processList.add(createProcessListItem("资源分配流程", "0", "待处理"));
            processList.add(createProcessListItem("工程实施流程", "0", "待处理"));
            result.put("processList", processList);
            
            // 统计信息
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalSteps", 9);
            statistics.put("completedSteps", 4);
            statistics.put("processingSteps", 2);
            statistics.put("pendingSteps", 3);
            statistics.put("completionRate", "44.4%");
            result.put("statistics", statistics);
            
            // 业务详情
            Map<String, Object> businessDetail = new HashMap<>();
            businessDetail.put("productCode", productCode);
            businessDetail.put("productName", getProductNameByCode(productCode));
            businessDetail.put("businessNumber", businessNumber);
            businessDetail.put("orderNumber", orderNumber);
            businessDetail.put("accessNumber", accessNumber);
            businessDetail.put("productInstanceId", productInstanceId);
            businessDetail.put("provinceName", getProvinceNameByCode(provinceCode));
            businessDetail.put("cityName", getCityNameByCode(cityCode));
            result.put("businessDetail", businessDetail);
            
            log.info("查询集团战新可视化PG库数据完成");
            return result;
            
        } catch (Exception e) {
            log.error("查询集团战新可视化PG库数据失败", e);
            throw new RuntimeException("查询集团可视化数据失败: " + e.getMessage());
        }
    }
    
    // 辅助方法：根据条件查询订单
    private List<Order> findOrdersByConditions(String productCode, String businessNumber, 
                                             String orderNumber, String accessNumber, 
                                             String productInstanceId, String provinceCode, 
                                             String cityCode, String startTime, String endTime) {
        Specification<Order> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            if (StringUtils.hasText(productCode)) {
                predicates.add(criteriaBuilder.equal(root.get("productId"), productCode));
            }
            if (StringUtils.hasText(businessNumber)) {
                predicates.add(criteriaBuilder.like(root.get("customerOrderNo"), "%" + businessNumber + "%"));
            }
            if (StringUtils.hasText(orderNumber)) {
                predicates.add(criteriaBuilder.like(root.get("orderNo"), "%" + orderNumber + "%"));
            }
            if (StringUtils.hasText(accessNumber)) {
                predicates.add(criteriaBuilder.like(root.get("customerOrderNo"), "%" + accessNumber + "%"));
            }
            if (StringUtils.hasText(productInstanceId)) {
                predicates.add(criteriaBuilder.like(root.get("description"), "%" + productInstanceId + "%"));
            }
            if (StringUtils.hasText(provinceCode)) {
                predicates.add(criteriaBuilder.equal(root.get("provinceCode"), provinceCode));
            }
            if (StringUtils.hasText(cityCode)) {
                predicates.add(criteriaBuilder.equal(root.get("cityCode"), cityCode));
            }
            
            // 时间范围过滤
            if (StringUtils.hasText(startTime)) {
                try {
                    LocalDateTime start = LocalDateTime.parse(startTime + " 00:00:00", 
                            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime"), start));
                } catch (Exception e) {
                    log.warn("开始时间格式错误: {}", startTime);
                }
            }
            
            if (StringUtils.hasText(endTime)) {
                try {
                    LocalDateTime end = LocalDateTime.parse(endTime + " 23:59:59", 
                            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createTime"), end));
                } catch (Exception e) {
                    log.warn("结束时间格式错误: {}", endTime);
                }
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        return orderRepository.findAll(spec);
    }
    
    // 辅助方法：获取省内环节步骤
    private List<Map<String, Object>> getProvinceSteps(Order order) {
        List<Map<String, Object>> steps = new ArrayList<>();
        
        // 模拟省内环节步骤（实际应该从流程引擎或数据库获取）
        steps.add(createProcessStep("省内订单受理", "2", "已完成", "2024-01-15 10:00:00"));
        steps.add(createProcessStep("省内资源检查", "2", "已完成", "2024-01-15 10:15:00"));
        steps.add(createProcessStep("省内工单派发", "1", "处理中", "2024-01-15 10:30:00"));
        steps.add(createProcessStep("省内施工安排", "0", "待处理", null));
        
        return steps;
    }
    
    // 辅助方法：创建流程步骤
    private Map<String, Object> createProcessStep(String stepName, String status, String statusText, String time) {
        Map<String, Object> step = new HashMap<>();
        step.put("stepName", stepName);
        step.put("status", status);
        step.put("statusText", statusText);
        step.put("time", time);
        return step;
    }
    
    // 辅助方法：创建流程列表项
    private Map<String, Object> createProcessListItem(String processName, String status, String statusText) {
        Map<String, Object> item = new HashMap<>();
        item.put("processName", processName);
        item.put("status", status);
        item.put("statusText", statusText);
        return item;
    }
    
    // 辅助方法：根据产品编码获取产品名称
    private String getProductNameByCode(String productCode) {
        if (StringUtils.hasText(productCode)) {
            switch (productCode) {
                case "PROD001": return "5G专网";
                case "PROD002": return "云专线";
                case "PROD003": return "物联网平台";
                case "PROD004": return "边缘计算";
                default: return "未知产品";
            }
        }
        return "未指定产品";
    }
    
    // 辅助方法：根据省份编码获取省份名称
    private String getProvinceNameByCode(String provinceCode) {
        if (StringUtils.hasText(provinceCode)) {
            switch (provinceCode) {
                case "11": return "北京";
                case "31": return "上海";
                case "44": return "广东";
                case "32": return "江苏";
                case "33": return "浙江";
                default: return "未知省份";
            }
        }
        return "未指定省份";
    }
    
    // 辅助方法：根据地市编码获取地市名称
    private String getCityNameByCode(String cityCode) {
        if (StringUtils.hasText(cityCode)) {
            switch (cityCode) {
                case "1101": return "北京市";
                case "3101": return "上海市";
                case "4401": return "广州市";
                case "4403": return "深圳市";
                case "3201": return "南京市";
                case "3301": return "杭州市";
                default: return "未知地市";
            }
        }
        return "未指定地市";
    }

}