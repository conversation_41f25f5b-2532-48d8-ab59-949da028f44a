import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  Card,
  Form,
  Input,
  Select,
  Button,
  Space,
  Tag,
  DatePicker,
  message,
  Alert,
} from 'antd';
import { SearchOutlined, ReloadOutlined, EyeOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { orderApi, productApi } from '../services/api';
import { Order, Product, OrderQueryParams } from '../types';

const { RangePicker } = DatePicker;
const { Option } = Select;

const OrderList: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [orders, setOrders] = useState<Order[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedProductCode, setSelectedProductCode] = useState<string>('');
  const [queryDescription, setQueryDescription] = useState<string>('');
  const [lastSearchParams, setLastSearchParams] = useState<OrderQueryParams>();
  const [provinces] = useState([
    { code: 'BJ', name: '北京' },
    { code: 'SH', name: '上海' },
    { code: 'GD', name: '广东' },
    { code: 'JS', name: '江苏' },
    { code: 'ZJ', name: '浙江' },
    { code: 'SD', name: '山东' },
    { code: 'HN', name: '河南' },
    { code: 'SC', name: '四川' },
    { code: 'HB', name: '湖北' },
    { code: 'HN2', name: '湖南' },
    { code: 'AH', name: '安徽' },
    { code: 'FJ', name: '福建' },
    { code: 'JX', name: '江西' },
    { code: 'GX', name: '广西' },
    { code: 'HI', name: '海南' },
    { code: 'CQ', name: '重庆' },
    { code: 'GZ', name: '贵州' },
    { code: 'YN', name: '云南' },
    { code: 'XZ', name: '西藏' },
    { code: 'SN', name: '陕西' },
    { code: 'GS', name: '甘肃' },
    { code: 'QH', name: '青海' },
    { code: 'NX', name: '宁夏' },
    { code: 'XJ', name: '新疆' },
    { code: 'NM', name: '内蒙古' },
    { code: 'SX', name: '山西' },
    { code: 'JL', name: '吉林' },
    { code: 'LN', name: '辽宁' },
    { code: 'HL', name: '黑龙江' },
    { code: 'HE', name: '河北' },
    { code: 'TJ', name: '天津' }
  ]);

  useEffect(() => {
    fetchOrders();
    fetchProducts();
  }, [current, pageSize]);

  const fetchOrders = async (params?: OrderQueryParams) => {
    try {
      setLoading(true);
      const queryParams = {
        ...params,
        pageNum: current,
        pageSize: pageSize,
      };
      
      // 根据产品编码决定使用新接口还是旧接口
      const isNewBusinessProduct = params?.productCode && 
        ['CLOUD_PC_PUBLIC', 'CLOUD_EYE', 'SMART_HOME', 'ENTERPRISE_CLOUD', '5G_PACKAGE', 'IOT_PLATFORM', 'AI_ASSISTANT', 'SECURITY_SERVICE'].includes(params.productCode);
      
      if (isNewBusinessProduct) {
        // 使用新业务省内段订单列表信息查询接口（4.121）
        const response = await orderApi.getProvinceOrderList({
          productCode: params?.productCode || '',
          businessNumber: params.businessAccount,
          orderNumber: params.orderNo,
          accessNumber: params.accessNumber,
          productInstanceId: params.productInstanceId,
          provinceCode: params.provinceCode,
          cityCode: params.areaCode, // 将 areaCode 映射为 cityCode 以匹配接口类型定义
          startTime: params.startTime,
          endTime: params.endTime,
          pageNum: current,
          pageSize: pageSize,
        });
        setOrders(response.data.data || []);
        setTotal(response.data.total || 0);
      } else {
        // 使用兼容的旧接口
        const response = await orderApi.getOrderList(queryParams);
        setOrders(response.data.data || []);
        setTotal(response.data.total || 0);
      }
    } catch (error) {
      message.error('获取订单列表失败: ' + (error as Error).message);
      setOrders([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await productApi.getProductList();
      setProducts(response.data);
    } catch (error) {
      console.error('获取产品列表失败:', error);
    }
  };

  const handleSearch = () => {
    const values = form.getFieldsValue();
    
    // 校验必填字段
    if (!values.productName) {
      message.error('产品名称为必填项');
      return;
    }
    
    if (!values.provinceCode) {
      message.error('省份为必填项');
      return;
    }
    
    if (!values.timeRange || values.timeRange.length !== 2) {
      message.error('开始时间和结束时间为必填项');
      return;
    }
    
    // 校验时间区间不能超过40天
    const startTime = values.timeRange[0];
    const endTime = values.timeRange[1];
    const daysDiff = endTime.diff(startTime, 'day');
    if (daysDiff > 40) {
      message.error('查询时间区间不能超过40天');
      return;
    }
    
    // 根据产品类型校验必填字段（三选一）
    const selectedProduct = products.find(product => product.productName === values.productName);
    if (selectedProduct) {
      const requiredFields = getRequiredFields(selectedProduct.productCode);
      const hasRequiredField = requiredFields.some(field => {
        const fieldValue = values[field];
        return fieldValue && fieldValue.trim() !== '';
      });
      
      if (!hasRequiredField) {
        message.error(getQueryDescription(selectedProduct.productCode));
        return;
      }
    }
    
    const params: OrderQueryParams = {
      ...values,
      productCode: selectedProduct?.productCode,
      startTime: startTime.format('YYYY-MM-DD HH:mm:ss'),
      endTime: endTime.format('YYYY-MM-DD HH:mm:ss'),
    };
    delete params.timeRange;
    setLastSearchParams(params);
    setCurrent(1);
    fetchOrders(params);
  };

  const handleReset = () => {
    form.resetFields();
    setSelectedProductCode('');
    setQueryDescription('');
    setLastSearchParams(undefined);
    setCurrent(1);
    fetchOrders();
  };

  const handleProductChange = (productName: string) => {
    const selectedProduct = products.find(product => product.productName === productName);
    if (selectedProduct) {
      setSelectedProductCode(selectedProduct.productCode);
      // 根据产品编码设置查询说明
      const queryDesc = getQueryDescription(selectedProduct.productCode);
      setQueryDescription(queryDesc);
    } else {
      setSelectedProductCode('');
      setQueryDescription('');
    }
  };

  // 根据产品编码获取查询说明
  const getQueryDescription = (productCode: string): string => {
    switch (productCode) {
      case 'CLOUD_PC_PUBLIC':
        return '云电脑公众版业务订单流水号、产品实例号、业务号码三选一必填';
      case 'CLOUD_EYE':
        return '天翼云眼业务订单流水号、业务号码、接入号三选一必填';
      case 'SMART_HOME':
        return '智慧家庭业务订单流水号、业务号码、接入号三选一必填';
      case 'ENTERPRISE_CLOUD':
        return '企业云业务订单流水号、产品实例号、业务号码三选一必填';
      case '5G_PACKAGE':
        return '5G套餐业务订单流水号、业务号码、接入号三选一必填';
      case 'IOT_PLATFORM':
        return '物联网平台业务订单流水号、产品实例号、业务号码三选一必填';
      case 'AI_ASSISTANT':
        return 'AI助手业务订单流水号、业务号码、接入号三选一必填';
      case 'SECURITY_SERVICE':
        return '安全服务业务订单流水号、产品实例号、业务号码三选一必填';
      default:
        return '业务订单流水号、产品实例号、业务号码三选一必填';
    }
  };

  // 根据产品编码获取必填字段
  const getRequiredFields = (productCode: string): string[] => {
    switch (productCode) {
      case 'CLOUD_PC_PUBLIC':
      case 'ENTERPRISE_CLOUD':
      case 'IOT_PLATFORM':
      case 'SECURITY_SERVICE':
        return ['orderNo', 'productInstanceId', 'businessAccount'];
      case 'CLOUD_EYE':
      case 'SMART_HOME':
      case '5G_PACKAGE':
      case 'AI_ASSISTANT':
        return ['orderNo', 'businessAccount', 'accessNumber'];
      default:
        return ['orderNo', 'productInstanceId', 'businessAccount'];
    }
  };

  const handleViewDetail = (record: Order) => {
    navigate(`/orders/${record.orderNo}`);
  };

  const getStatusColor = (status: string) => {
    const colorMap: { [key: string]: string } = {
      '待处理': 'orange',
      '处理中': 'blue',
      '已完成': 'green',
      '已取消': 'red',
      '异常': 'red',
    };
    return colorMap[status] || 'default';
  };

  const getStepStatusColor = (status: string | number) => {
    const colorMap: { [key: string]: string } = {
      'PENDING': 'orange',
      'PROCESSING': 'blue',
      'COMPLETED': 'green',
      'EXCEPTION': 'red',
      '0': 'orange',
      '1': 'blue',
      '2': 'green',
      '3': 'warning',
      '4': 'red',
    };
    return colorMap[String(status)] || 'default';
  };

  const getStepStatusText = (status: string | number) => {
    const textMap: { [key: string]: string } = {
      'PENDING': '待处理',
      'PROCESSING': '处理中',
      'COMPLETED': '已完成',
      'EXCEPTION': '异常',
      '0': '待处理',
      '1': '处理中',
      '2': '已完成',
      '3': '已跳过',
      '4': '异常',
    };
    return textMap[String(status)] || String(status);
  };

  // 安全渲染文本，处理HTML转义和空值
  const renderSafeText = (text: any, defaultValue: string = '-') => {
    if (text === null || text === undefined || text === '') {
      return defaultValue;
    }
    // 转换为字符串并进行HTML转义
    const safeText = String(text)
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;');
    return <span dangerouslySetInnerHTML={{ __html: safeText }} />;
  };

  const columns: ColumnsType<Order> = [
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      width: 120,
      fixed: 'left',
      render: (text: string) => renderSafeText(text),
    },
    {      title: '省份',      dataIndex: 'provinceName',      key: 'provinceName',      width: 80,      render: (text: string) => renderSafeText(text),    },    {      title: '地市',      dataIndex: 'cityName',      key: 'cityName',      width: 80,      render: (text: string) => renderSafeText(text),    },
    {
      title: '订单流水号',
      dataIndex: 'orderNo',
      key: 'orderNo',
      width: 150,
      render: (text: string) => renderSafeText(text),
    },
    {      title: '客户订单号',      dataIndex: 'custOrderNo',      key: 'custOrderNo',      width: 150,      render: (text: string) => renderSafeText(text),    },    {      title: '客户名称',      dataIndex: 'customerName',      key: 'customerName',      width: 120,      render: (text: string) => renderSafeText(text),    },    {      title: '客户ID',      dataIndex: 'customerId',      key: 'customerId',      width: 150,      render: (text: string) => renderSafeText(text),    },    {      title: '业务类型',      dataIndex: 'businessType',      key: 'businessType',      width: 120,      render: (text: string) => renderSafeText(text),    },    {      title: '当前环节',      dataIndex: 'currentStep',      key: 'currentStep',      width: 150,      render: (text: string) => renderSafeText(text),    },    {      title: '订单金额',      dataIndex: 'orderAmount',      key: 'orderAmount',      width: 120,      render: (amount: number) => amount ? `¥${amount.toFixed(2)}` : '-',    },
    {      title: '工单状态',      dataIndex: 'orderStatus',      key: 'orderStatus',      width: 100,      render: (status: number) => {        const statusMap: { [key: number]: { text: string; color: string } } = {          0: { text: '待处理', color: 'default' },          1: { text: '处理中', color: 'processing' },          2: { text: '已完成', color: 'success' },          3: { text: '已取消', color: 'default' },          4: { text: '异常', color: 'error' }        };        const statusInfo = statusMap[status] || { text: '未知', color: 'default' };        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;      },    },    {      title: '流程实例ID',      dataIndex: 'processInstanceId',      key: 'processInstanceId',      width: 180,      render: (text: string) => renderSafeText(text),    },    {      title: '创建时间',      dataIndex: 'createTime',      key: 'createTime',      width: 150,      render: (time: string) => time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-',    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      fixed: 'right',
      render: (_, record) => (
        <Button
          type="link"
          icon={<EyeOutlined />}
          onClick={() => handleViewDetail(record)}
        >
          详情
        </Button>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card title="战新业务省内重点环节可视化查询" style={{ marginBottom: '16px' }}>
        <Form form={form} layout="vertical" style={{ marginBottom: '16px' }}>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
            <Form.Item 
              name="productName" 
              label="产品名称" 
              rules={[{ required: true, message: '产品名称为必填项' }]}
              style={{ minWidth: '200px' }}
            >
              <Select 
                placeholder="请选择产品" 
                onChange={handleProductChange}
                style={{ width: '100%' }}
              >
                {products.map(product => (
                  <Option key={product.productCode} value={product.productName}>
                    {product.productName}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            
            <Form.Item name="accessNumber" label="接入号" style={{ minWidth: '200px' }}>
              <Input placeholder="请输入接入号" />
            </Form.Item>
            
            <Form.Item name="orderNo" label="订单流水号" style={{ minWidth: '200px' }}>
              <Input placeholder="请输入订单流水号" />
            </Form.Item>
            
            <Form.Item name="productInstanceId" label="产品实例号" style={{ minWidth: '200px' }}>
              <Input placeholder="请输入产品实例号" />
            </Form.Item>
            
            <Form.Item name="businessAccount" label="业务号码" style={{ minWidth: '200px' }}>
              <Input placeholder="请输入业务号码" />
            </Form.Item>
            
            <Form.Item 
              name="provinceCode" 
              label="省份" 
              rules={[{ required: true, message: '省份为必填项' }]}
              style={{ minWidth: '150px' }}
            >
              <Select placeholder="请选择省份">
                {provinces.map(province => (
                  <Option key={province.code} value={province.code}>
                    {province.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            
            <Form.Item name="areaCode" label="地市" style={{ minWidth: '150px' }}>
              <Input placeholder="请输入地市编码" />
            </Form.Item>
            
            <Form.Item 
              name="timeRange" 
              label="时间范围" 
              rules={[{ required: true, message: '开始时间和结束时间为必填项' }]}
              style={{ minWidth: '350px' }}
            >
              <RangePicker
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                style={{ width: '100%' }}
                placeholder={['开始时间', '结束时间']}
              />
            </Form.Item>
          </div>
          
          {queryDescription && (
            <Alert
              message="查询说明"
              description={queryDescription}
              type="info"
              showIcon
              style={{ marginBottom: '16px' }}
            />
          )}
          
          <Form.Item>
            <Space>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleSearch}
                loading={loading}
              >
                查询
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      <Card>
        <Table
          columns={columns}
          dataSource={orders}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1500 }}
          pagination={{
            current,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, size) => {
              setCurrent(page);
              if (size && size !== pageSize) {
                setPageSize(size);
                setCurrent(1); // 改变页面大小时重置到第一页
              }
              // 重新获取数据
              fetchOrders(lastSearchParams);
            },
            onShowSizeChange: (current, size) => {
              setPageSize(size);
              setCurrent(1);
              fetchOrders(lastSearchParams);
            },
          }}
        />
      </Card>


    </div>
  );
};

export default OrderList;