package com.iwhalecloud.pdos.service.impl;

import com.iwhalecloud.pdos.dto.PageResult;
import com.iwhalecloud.pdos.entity.Product;
import com.iwhalecloud.pdos.repository.ProductRepository;
import com.iwhalecloud.pdos.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 产品服务实现类
 */
@Service
@Slf4j
public class ProductServiceImpl implements ProductService {
    
    @Autowired
    private ProductRepository productRepository;
    
    @Override
    public List<Product> getProducts(String productName, String productType, Integer status) {
        try {
            if (StringUtils.hasText(productName) || StringUtils.hasText(productType) || status != null) {
                // 使用分页查询但只返回数据列表
                Pageable pageable = PageRequest.of(0, 1000, Sort.by(Sort.Direction.ASC, "productName"));
                Page<Product> page = productRepository.findByConditions(productName, productType, status, pageable);
                return page.getContent();
            } else {
                // 查询所有启用状态的产品
                return productRepository.findByStatus(1);
            }
        } catch (Exception e) {
            log.error("获取产品列表失败", e);
            throw new RuntimeException("获取产品列表失败: " + e.getMessage());
        }
    }
    
    @Override
    public PageResult<Product> getProductsPage(String productName, String productType, Integer status, int pageNum, int pageSize) {
        try {
            Pageable pageable = PageRequest.of(pageNum - 1, pageSize, 
                Sort.by(Sort.Direction.ASC, "productName"));
            
            Page<Product> page = productRepository.findByConditions(productName, productType, status, pageable);
            return PageResult.of(page);
        } catch (Exception e) {
            log.error("分页查询产品失败", e);
            return PageResult.empty(pageNum, pageSize);
        }
    }
    
    @Override
    public Optional<Product> getProductById(Long id) {
        try {
            return productRepository.findById(id);
        } catch (Exception e) {
            log.error("根据ID查询产品失败", e);
            return Optional.empty();
        }
    }
    
    @Override
    public Optional<Product> getProductByCode(String productCode) {
        try {
            return productRepository.findByProductCode(productCode);
        } catch (Exception e) {
            log.error("根据产品编码查询产品失败", e);
            return Optional.empty();
        }
    }
    
    @Override
    public Product createProduct(Product product) {
        try {
            // 检查产品编码是否已存在
            if (StringUtils.hasText(product.getProductCode())) {
                Optional<Product> existing = productRepository.findByProductCode(product.getProductCode());
                if (existing.isPresent()) {
                    throw new RuntimeException("产品编码已存在: " + product.getProductCode());
                }
            }
            
            // 设置创建时间
            product.setCreateTime(LocalDateTime.now());
            product.setUpdateTime(LocalDateTime.now());
            
            return productRepository.save(product);
        } catch (Exception e) {
            log.error("创建产品失败", e);
            throw new RuntimeException("创建产品失败: " + e.getMessage());
        }
    }
    
    @Override
    public Product updateProduct(Product product) {
        try {
            // 检查产品是否存在
            Product existing = productRepository.findById(product.getId())
                .orElseThrow(() -> new RuntimeException("产品不存在"));
            
            // 检查产品编码是否被其他产品使用
            if (StringUtils.hasText(product.getProductCode()) && 
                !product.getProductCode().equals(existing.getProductCode())) {
                Optional<Product> codeExists = productRepository.findByProductCode(product.getProductCode());
                if (codeExists.isPresent()) {
                    throw new RuntimeException("产品编码已存在: " + product.getProductCode());
                }
            }
            
            // 更新字段
            existing.setProductCode(product.getProductCode());
            existing.setProductName(product.getProductName());
            existing.setProductType(product.getProductType());
            existing.setStatus(product.getStatus());
            existing.setDescription(product.getDescription());
            existing.setUpdateTime(LocalDateTime.now());
            existing.setUpdateBy(product.getUpdateBy());
            
            return productRepository.save(existing);
        } catch (Exception e) {
            log.error("更新产品失败", e);
            throw new RuntimeException("更新产品失败: " + e.getMessage());
        }
    }
    
    @Override
    public void deleteProduct(Long id) {
        try {
            Product product = productRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("产品不存在"));
            
            // 软删除：设置状态为停用
            product.setStatus(0);
            product.setUpdateTime(LocalDateTime.now());
            productRepository.save(product);
            
            // 如果需要物理删除，使用下面的代码
            // productRepository.deleteById(id);
        } catch (Exception e) {
            log.error("删除产品失败", e);
            throw new RuntimeException("删除产品失败: " + e.getMessage());
        }
    }
    
    @Override
    public List<Object[]> getProductTypeStatistics() {
        try {
            return productRepository.countByProductType();
        } catch (Exception e) {
            log.error("获取产品类型统计失败", e);
            throw new RuntimeException("获取产品类型统计失败: " + e.getMessage());
        }
    }
}