@echo off
chcp 65001 >nul
echo ========================================
echo PDOS Frontend Server Startup Script
echo ========================================
echo.

REM Check port availability
echo [INFO] Checking if port 3002 is available...
netstat -an | findstr ":3002" >nul
if %errorlevel% equ 0 (
    echo [ERROR] Port 3002 is already in use
    echo [ERROR] Please stop the existing service or change the port
    pause
    exit /b 1
)
echo [OK] Port 3002 is available
echo.

REM Check Node.js environment
echo [INFO] Checking Node.js environment...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo [ERROR] Please install Node.js 14 or higher
    pause
    exit /b 1
)
echo [OK] Node.js environment detected

REM Check npm environment
echo [INFO] Checking npm environment...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm is not installed or not in PATH
    echo [ERROR] Please install npm
    pause
    exit /b 1
)
echo [OK] npm environment detected

REM Switch to frontend project directory
cd /d "%~dp0\pdos-frontend"
echo [INFO] Current directory: %CD%

REM Check package.json file
if not exist "package.json" (
    echo [ERROR] package.json not found in pdos-frontend directory
    echo [ERROR] Please ensure the frontend project structure is correct
    pause
    exit /b 1
)

REM Check if node_modules exists, install dependencies if not
if not exist "node_modules" (
    echo [INFO] node_modules not found, installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install dependencies
        pause
        exit /b 1
    )
    echo [OK] Dependencies installed successfully
) else (
    echo [OK] Dependencies already installed
)

REM Start frontend development server
echo.
echo [INFO] Starting PDOS Frontend Server on port 3002...
echo [INFO] Frontend URL: http://localhost:3002
echo [INFO] Backend API: http://localhost:8888/api
echo [INFO] Press Ctrl+C to stop the server
echo.
echo [INFO] Starting React development server...
echo ========================================
npm start

echo.
echo ========================================
echo [INFO] Frontend server stopped
echo [INFO] Thank you for using PDOS Frontend Server
pause