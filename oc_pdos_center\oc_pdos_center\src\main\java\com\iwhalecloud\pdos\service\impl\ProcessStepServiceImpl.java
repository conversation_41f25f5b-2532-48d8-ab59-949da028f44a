package com.iwhalecloud.pdos.service.impl;

import com.iwhalecloud.pdos.entity.ProcessStep;
import com.iwhalecloud.pdos.repository.ProcessStepRepository;
import com.iwhalecloud.pdos.service.ProcessStepService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 流程步骤服务实现类
 */
@Service
@Slf4j
public class ProcessStepServiceImpl implements ProcessStepService {
    
    @Autowired
    private ProcessStepRepository processStepRepository;
    
    @Override
    public List<ProcessStep> getOrderSteps(Long orderId) {
        try {
            return processStepRepository.findByOrderIdOrderByStepOrder(orderId);
        } catch (Exception e) {
            log.error("获取订单流程步骤失败", e);
            throw new RuntimeException("获取订单流程步骤失败: " + e.getMessage());
        }
    }
    
    @Override
    public List<ProcessStep> getOrderStepsByOrderNo(String orderNo) {
        try {
            return processStepRepository.findByOrderNoOrderByStepOrder(orderNo);
        } catch (Exception e) {
            log.error("根据订单号获取流程步骤失败", e);
            throw new RuntimeException("根据订单号获取流程步骤失败: " + e.getMessage());
        }
    }
    
    @Override
    public Optional<ProcessStep> getCurrentStep(Long orderId) {
        try {
            List<ProcessStep> currentSteps = processStepRepository.findByOrderIdAndStepStatus(orderId);
            return currentSteps.isEmpty() ? Optional.empty() : Optional.of(currentSteps.get(0));
        } catch (Exception e) {
            log.error("获取当前步骤失败", e);
            return Optional.empty();
        }
    }
    
    @Override
    public List<ProcessStep> getCompletedSteps(Long orderId) {
        try {
            return processStepRepository.findCompletedStepsByOrderId(orderId);
        } catch (Exception e) {
            log.error("获取已完成步骤失败", e);
            throw new RuntimeException("获取已完成步骤失败: " + e.getMessage());
        }
    }
    
    @Override
    public List<ProcessStep> getExceptionSteps(Long orderId) {
        try {
            return processStepRepository.findErrorStepsByOrderId(orderId);
        } catch (Exception e) {
            log.error("获取异常步骤失败", e);
            throw new RuntimeException("获取异常步骤失败: " + e.getMessage());
        }
    }
    
    @Override
    public ProcessStep createProcessStep(ProcessStep processStep) {
        try {
            // 设置创建时间
            if (processStep.getCreateTime() == null) {
                processStep.setCreateTime(LocalDateTime.now());
            }
            
            // 设置默认状态
            if (processStep.getStepStatus() == null) {
                processStep.setStepStatus(0); // 待处理状态
            }
            
            return processStepRepository.save(processStep);
        } catch (Exception e) {
            log.error("创建流程步骤失败", e);
            throw new RuntimeException("创建流程步骤失败: " + e.getMessage());
        }
    }
    
    @Override
    public void updateStepStatus(Long stepId, Integer status) {
        try {
            ProcessStep step = processStepRepository.findById(stepId)
                .orElseThrow(() -> new RuntimeException("流程步骤不存在"));
            
            step.setStepStatus(status);
            step.setUpdateTime(LocalDateTime.now());
            
            processStepRepository.save(step);
        } catch (Exception e) {
            log.error("更新步骤状态失败", e);
            throw new RuntimeException("更新步骤状态失败: " + e.getMessage());
        }
    }
    
    @Override
    public ProcessStep updateProcessStep(ProcessStep processStep) {
        try {
            ProcessStep existing = processStepRepository.findById(processStep.getId())
                .orElseThrow(() -> new RuntimeException("流程步骤不存在"));
            
            // 更新字段
            existing.setStepCode(processStep.getStepCode());
            existing.setStepName(processStep.getStepName());
            existing.setStepStatus(processStep.getStepStatus());
            existing.setHandler(processStep.getHandler());
            existing.setHandlerDept(processStep.getHandlerDept());
            existing.setRemark(processStep.getRemark());
            
            return processStepRepository.save(existing);
        } catch (Exception e) {
            log.error("更新流程步骤失败", e);
            throw new RuntimeException("更新流程步骤失败: " + e.getMessage());
        }
    }
    
    @Override
    public void completeStep(Long stepId, String result, String remark) {
        try {
            ProcessStep step = processStepRepository.findById(stepId)
                .orElseThrow(() -> new RuntimeException("流程步骤不存在"));
            
            step.setStepStatus(2); // 已完成状态
            step.setStepResult(result);
            step.setRemark(remark);
            step.setEndTime(LocalDateTime.now());
            
            processStepRepository.save(step);
        } catch (Exception e) {
            log.error("完成流程步骤失败", e);
            throw new RuntimeException("完成流程步骤失败: " + e.getMessage());
        }
    }
    
    @Override
    public void markStepError(Long stepId, String errorMessage) {
        try {
            ProcessStep step = processStepRepository.findById(stepId)
                .orElseThrow(() -> new RuntimeException("流程步骤不存在"));
            
            step.setStepStatus(4); // 异常状态
            step.setErrorMessage(errorMessage);
            step.setUpdateTime(LocalDateTime.now());
            
            processStepRepository.save(step);
        } catch (Exception e) {
            log.error("标记步骤异常失败", e);
            throw new RuntimeException("标记步骤异常失败: " + e.getMessage());
        }
    }
    
    @Override
    public List<Object[]> getStepDurationStatistics() {
        try {
            return processStepRepository.getStepDurationStatistics();
        } catch (Exception e) {
            log.error("获取步骤处理时长统计失败", e);
            throw new RuntimeException("获取步骤处理时长统计失败: " + e.getMessage());
        }
    }
    
    @Override
    public List<Object[]> getDeptEfficiencyStatistics() {
        try {
            return processStepRepository.getDeptEfficiencyStatistics();
        } catch (Exception e) {
            log.error("获取部门处理效率统计失败", e);
            throw new RuntimeException("获取部门处理效率统计失败: " + e.getMessage());
        }
    }
    
    @Override
    public List<ProcessStep> getTimeoutSteps(Long timeoutMinutes) {
        try {
            LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(timeoutMinutes);
            return processStepRepository.findTimeoutSteps(timeoutThreshold);
        } catch (Exception e) {
            log.error("获取超时步骤失败", e);
            throw new RuntimeException("获取超时步骤失败: " + e.getMessage());
        }
    }
    
    @Override
    public List<Object[]> getStepStatusStatistics() {
        try {
            return processStepRepository.getStepStatusStatistics();
        } catch (Exception e) {
            log.error("获取步骤状态统计失败", e);
            throw new RuntimeException("获取步骤状态统计失败: " + e.getMessage());
        }
    }
}