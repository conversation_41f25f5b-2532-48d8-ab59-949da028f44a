package com.iwhalecloud.pdos.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iwhalecloud.pdos.dto.Result;
import com.iwhalecloud.pdos.dto.PageResult;
import com.iwhalecloud.pdos.entity.Order;
import com.iwhalecloud.pdos.entity.Product;
import com.iwhalecloud.pdos.entity.ProcessStep;
import com.iwhalecloud.pdos.service.PdosCenterOrderService;
import com.iwhalecloud.pdos.service.ProductService;
import com.iwhalecloud.pdos.service.ProcessStepService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.nio.charset.StandardCharsets;
import org.springframework.util.StreamUtils;

@RestController
@RequestMapping("/pdos")
@Slf4j
@CrossOrigin(origins = "http://localhost:3002")
public class PdosCenterOrderController {

    @Autowired
    private PdosCenterOrderService pdosCenterOrderService;
    
    @Autowired
    private ProductService productService;
    
    @Autowired
    private ProcessStepService processStepService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 根据产品编码校验必填字段
     */
    private boolean validateRequiredFields(String productCode, String businessAccount, String custOrderCode, String accNum, String prodInstId) {
        switch (productCode) {
            case "CLOUD_PC_PUBLIC": // 云电脑公众版
                return StringUtils.isNotBlank(custOrderCode) || StringUtils.isNotBlank(prodInstId) || StringUtils.isNotBlank(businessAccount);
            case "CLOUD_EYE": // 天翼云眼
            case "SMART_HOME": // 天翼看家
            case "VIDEO_CONFERENCE": // 天翼视联
                return StringUtils.isNotBlank(custOrderCode) || StringUtils.isNotBlank(businessAccount) || StringUtils.isNotBlank(accNum);
            default:
                return StringUtils.isNotBlank(custOrderCode) || StringUtils.isNotBlank(prodInstId) || StringUtils.isNotBlank(businessAccount);
        }
    }
    
    /**
     * 获取产品必填字段提示信息
     */
    private String getProductRequiredFieldsMessage(String productCode) {
        switch (productCode) {
            case "CLOUD_PC_PUBLIC":
                return "云电脑公众版业务订单流水号、产品实例号、业务号码三选一必填";
            case "CLOUD_EYE":
                return "天翼云眼业务订单流水号、业务号码、接入号三选一必填";
            case "SMART_HOME":
                return "天翼看家业务订单流水号、业务号码、接入号三选一必填";
            case "VIDEO_CONFERENCE":
                return "天翼视联业务订单流水号、业务号码、接入号三选一必填";
            default:
                return "业务订单流水号、产品实例号、业务号码三选一必填";
        }
    }

    /**
     * 4.121 新业务省内段订单列表信息查询接口
     * 按照接口规范实现省内段订单列表查询
     */
    @PostMapping("/queryCenterOrderList")
    public Result<Map<String, Object>> queryCenterOrderList(@RequestBody Map<String, Object> params) {
        try {
            // 校验必填参数
            String productCode = (String) params.get("productCode");
            String provinceId = (String) params.get("provinceId");
            String startTime = (String) params.get("startTime");
            String endTime = (String) params.get("endTime");
            
            if (StringUtils.isBlank(productCode)) {
                return Result.error("产品编码为必填项");
            }
            if (StringUtils.isBlank(provinceId)) {
                return Result.error("省份编码为必填项");
            }
            if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
                return Result.error("开始时间和结束时间为必填项");
            }
            
            // 校验时间区间不超过40天
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime start = LocalDateTime.parse(startTime, formatter);
            LocalDateTime end = LocalDateTime.parse(endTime, formatter);
            long daysDiff = java.time.Duration.between(start, end).toDays();
            if (daysDiff > 40) {
                return Result.error("查询时间区间不能超过40天");
            }
            
            // 根据产品编码校验三选一必填字段
            String businessAccount = (String) params.get("businessAccount");
            String custOrderCode = (String) params.get("custOrderCode");
            String accNum = (String) params.get("accNum");
            String prodInstId = (String) params.get("prodInstId");
            
            boolean hasRequiredField = validateRequiredFields(productCode, businessAccount, custOrderCode, accNum, prodInstId);
            if (!hasRequiredField) {
                return Result.error(getProductRequiredFieldsMessage(productCode));
            }
            
            // 解析分页参数
            int pageNum = (Integer) params.getOrDefault("pageNum", 1);
            int pageSize = (Integer) params.getOrDefault("pageSize", 20);
            
            // 解析其他参数
            String businessNumber = businessAccount;
            String orderNumber = custOrderCode;
            String accessNumber = accNum;
            String productInstanceId = prodInstId;
            String cityCode = (String) params.get("cityCode");
            
            // 调用省内段订单列表查询服务
            Page<Order> orderPage = pdosCenterOrderService.queryProvinceOrderList(
                productCode, businessNumber, orderNumber, accessNumber,
                productInstanceId, provinceId, cityCode, startTime, endTime,
                pageNum, pageSize);

            // 检查查询结果
            if (orderPage == null) {
                log.error("查询省内段订单列表返回null");
                return Result.error("查询省内段订单列表失败: 服务返回空结果");
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("data", orderPage.getContent());
            result.put("total", orderPage.getTotalElements());
            result.put("pageNum", pageNum);
            result.put("pageSize", pageSize);
            result.put("totalPages", orderPage.getTotalPages());
            result.put("hasNext", orderPage.hasNext());
            result.put("hasPrevious", orderPage.hasPrevious());
            result.put("isFirst", orderPage.isFirst());
            result.put("isLast", orderPage.isLast());
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询省内段订单列表失败", e);
            return Result.error("查询省内段订单列表失败: " + e.getMessage());
        }
    }

    /**
     * 4.122 新业务省内段环节信息查询接口
     * 按照接口规范实现省内段环节信息查询
     */
    @PostMapping("/queryCenterOrderDetails")
    public Result<Map<String, Object>> queryCenterOrderDetails(@RequestBody Map<String, Object> params) {
        try {
            // 获取省编排唯一流水号（必填）
            String provinceSerialId = (String) params.get("provinceSerialId");
            if (StringUtils.isBlank(provinceSerialId)) {
                return Result.error("省编排唯一流水号不能为空");
            }
            
            // 从参数中提取所需字段
            String productCode = (String) params.get("productCode");
            String businessNumber = (String) params.get("businessNumber");
            String orderNumber = (String) params.get("orderNumber");
            String accessNumber = (String) params.get("accessNumber");
            String productInstanceId = (String) params.get("productInstanceId");
            String provinceCode = (String) params.get("provinceCode");
            String cityCode = (String) params.get("cityCode");
            String startTime = (String) params.get("startTime");
            String endTime = (String) params.get("endTime");
            
            // 调用省内段环节信息查询服务
            Map<String, Object> result = pdosCenterOrderService.queryProvinceOrderDetails(
                productCode, businessNumber, orderNumber, accessNumber, 
                productInstanceId, provinceCode, cityCode, startTime, endTime);
            
            // 同时查询集团可视化数据进行融合展示
            Map<String, Object> groupData = pdosCenterOrderService.queryGroupVisualizationData(
                productCode, businessNumber, orderNumber, accessNumber, 
                productInstanceId, provinceCode, cityCode, startTime, endTime);
            if (groupData != null && !groupData.isEmpty()) {
                result.put("groupVisualizationData", groupData);
            }
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询省内段环节信息失败", e);
            return Result.error("查询省内段环节信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取产品列表
     */
    @GetMapping("/products")
    public Result<List<Product>> getProducts(
            @RequestParam(required = false) String productName,
            @RequestParam(required = false) String productType,
            @RequestParam(defaultValue = "1") Integer status) {
        try {
            List<Product> products = productService.getProducts(productName, productType, status);
            return Result.success(products);
        } catch (Exception e) {
            log.error("获取产品列表失败", e);
            return Result.error("获取产品列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取统计数据
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getStatistics() {
        try {
            Map<String, Object> statistics = pdosCenterOrderService.getStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取订单流程步骤
     */
    @GetMapping("/order/{orderId}/steps")
    public Result<List<ProcessStep>> getOrderSteps(@PathVariable Long orderId) {
        try {
            List<ProcessStep> steps = processStepService.getOrderSteps(orderId);
            return Result.success(steps);
        } catch (Exception e) {
            log.error("获取订单流程步骤失败", e);
            return Result.error("获取订单流程步骤失败: " + e.getMessage());
        }
    }

//    @PostMapping("/queryCenterOrderList")
//    public ResponseEntity<Map<String, Object>> queryCenterOrderList(@RequestBody Map<String, Object> params) {
//        int pageNum = MapUtils.getInteger(params, "pageNum", 1);
//        int pageSize = MapUtils.getInteger(params, "pageSize", 20);
//        PageResult<Map> page = pdosCenterOrderService.queryCenterOrderList(params, pageNum, pageSize);
//        Map<String, Object> result = new HashMap<>();
//        result.put("code", 0);
//        result.put("msg", "success");
//        result.put("total", page.getTotal());
//        result.put("custOrderInfo", page.getData());
//        return new ResponseEntity<>(result, HttpStatus.OK);
//    }
//
//    @PostMapping("/queryCenterOrderDetails")
//    public Object queryCenterOrderDetails(@RequestBody Map<String, Object> params) {
//
//        return pdosCenterOrderService.queryCenterOrderDetails(params);
//    }

}