package com.iwhalecloud.pdos.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 产品实体类
 * 对应战新业务产品信息
 */
@Entity
@Table(name = "t_product")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Product {
    
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;
    
    /**
     * 产品编码
     */
    @Column(name = "product_code", unique = true, nullable = false, length = 50)
    private String productCode;
    
    /**
     * 产品名称
     */
    @Column(name = "product_name", nullable = false, length = 100)
    private String productName;
    
    /**
     * 产品类型
     */
    @Column(name = "product_type", length = 50)
    private String productType;
    
    /**
     * 产品状态：0-停用，1-启用
     */
    @Column(name = "status", nullable = false)
    private Integer status = 1;
    
    /**
     * 产品描述
     */
    @Column(name = "description", length = 500)
    private String description;
    
    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    @Column(name = "create_by", length = 50)
    private String createBy;
    
    /**
     * 更新人
     */
    @Column(name = "update_by", length = 50)
    private String updateBy;
}