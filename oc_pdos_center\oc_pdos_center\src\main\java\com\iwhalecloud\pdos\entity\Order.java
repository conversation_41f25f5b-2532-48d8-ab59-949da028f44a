package com.iwhalecloud.pdos.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单实体类
 * 对应战新业务订单信息
 */
@Entity
@Table(name = "t_order")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Order {
    
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;
    
    /**
     * 订单号
     */
    @Column(name = "order_no", unique = true, nullable = false, length = 50)
    private String orderNo;
    
    /**
     * 客户订单号
     */
    @Column(name = "cust_order_no", length = 50)
    private String custOrderNo;
    
    /**
     * 产品ID
     */
    @Column(name = "product_id", nullable = false)
    private Long productId;
    
    /**
     * 产品名称（冗余字段，便于查询）
     */
    @Column(name = "product_name", length = 100)
    private String productName;
    
    /**
     * 客户ID
     */
    @Column(name = "customer_id", length = 50)
    private String customerId;
    
    /**
     * 客户名称
     */
    @Column(name = "customer_name", length = 100)
    private String customerName;
    
    /**
     * 订单状态：0-待处理，1-处理中，2-已完成，3-已取消，4-异常
     */
    @Column(name = "order_status", nullable = false)
    private Integer orderStatus = 0;
    
    /**
     * 订单金额
     */
    @Column(name = "order_amount", precision = 10, scale = 2)
    private BigDecimal orderAmount;
    
    /**
     * 省份编码
     */
    @Column(name = "province_code", length = 10)
    private String provinceCode;
    
    /**
     * 省份名称
     */
    @Column(name = "province_name", length = 50)
    private String provinceName;
    
    /**
     * 地市编码
     */
    @Column(name = "city_code", length = 10)
    private String cityCode;
    
    /**
     * 地市名称
     */
    @Column(name = "city_name", length = 50)
    private String cityName;
    
    /**
     * 业务类型
     */
    @Column(name = "business_type", length = 50)
    private String businessType;
    
    /**
     * 流程实例ID
     */
    @Column(name = "process_instance_id", length = 100)
    private String processInstanceId;
    
    /**
     * 当前环节
     */
    @Column(name = "current_step", length = 100)
    private String currentStep;
    
    /**
     * 订单描述
     */
    @Column(name = "description", length = 500)
    private String description;
    
    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;
    
    /**
     * 完成时间
     */
    @Column(name = "complete_time")
    private LocalDateTime completeTime;
    
    /**
     * 创建人
     */
    @Column(name = "create_by", length = 50)
    private String createBy;
    
    /**
     * 更新人
     */
    @Column(name = "update_by", length = 50)
    private String updateBy;
}