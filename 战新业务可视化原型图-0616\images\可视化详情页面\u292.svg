﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="25px" height="20px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient gradientUnits="userSpaceOnUse" x1="12.5" y1="0" x2="12.5" y2="20" id="LinearGradient13">
      <stop id="Stop14" stop-color="#ffffff" offset="0" />
      <stop id="Stop15" stop-color="#f2f2f2" offset="0" />
      <stop id="Stop16" stop-color="#e4e4e4" offset="1" />
      <stop id="Stop17" stop-color="#ffffff" offset="1" />
    </linearGradient>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip18">
      <path d="M 0 20  L 12.5 0  L 25 20  L 0 20  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -103 -1904 )">
    <path d="M 0 20  L 12.5 0  L 25 20  L 0 20  Z " fill-rule="nonzero" fill="url(#LinearGradient13)" stroke="none" transform="matrix(1 0 0 1 103 1904 )" class="fill" />
    <path d="M 0 20  L 12.5 0  L 25 20  L 0 20  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 103 1904 )" class="stroke" mask="url(#Clip18)" />
  </g>
</svg>