import axios, { AxiosResponse } from 'axios';
import { ApiResponse, PageResult, Order, Product, ProcessStep, OrderQueryParams, Statistics, OrderDetail } from '../types';

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:8888/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加token等认证信息
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response;
    if (data.code === 0) {
      return response;
    } else {
      // 处理业务错误
      throw new Error(data.message || '请求失败');
    }
  },
  (error) => {
    // 处理HTTP错误
    const message = error.response?.data?.message || error.message || '网络错误';
    throw new Error(message);
  }
);

// API接口定义
export const orderApi = {
  // 查询订单列表（兼容旧接口）
  getOrderList: (params: OrderQueryParams): Promise<ApiResponse<PageResult<Order>>> => {
    return api.post('/pdos/queryCenterOrderList', params).then(res => res.data);
  },

  // 新业务省内段订单列表信息查询接口（4.121）
  getProvinceOrderList: (params: {
    productCode: string;
    businessNumber?: string;
    orderNumber?: string;
    accessNumber?: string;
    productInstanceId?: string;
    provinceCode?: string;
    cityCode?: string;
    startTime?: string;
    endTime?: string;
    pageNum: number;
    pageSize: number;
  }): Promise<ApiResponse<PageResult<Order>>> => {
    return api.post('/pdos/queryProvinceOrderList', params).then(res => res.data);
  },

  // 查询订单详情（兼容旧接口）
  getOrderDetail: (orderNo: string): Promise<ApiResponse<OrderDetail>> => {
    return api.post('/pdos/queryCenterOrderDetails', { orderNo }).then(res => res.data);
  },

  // 新业务省内段环节信息查询接口（4.122）
  getProvinceOrderDetails: (params: {
    productCode: string;
    businessNumber?: string;
    orderNumber?: string;
    accessNumber?: string;
    productInstanceId?: string;
    provinceCode?: string;
    cityCode?: string;
    startTime?: string;
    endTime?: string;
  }): Promise<ApiResponse<any>> => {
    return api.post('/pdos/queryProvinceOrderDetails', params).then(res => res.data);
  },

  // 查询集团战新可视化PG库数据接口
  getGroupVisualizationData: (params: {
    productCode: string;
    businessNumber?: string;
    orderNumber?: string;
    accessNumber?: string;
    productInstanceId?: string;
    provinceCode?: string;
    cityCode?: string;
    startTime?: string;
    endTime?: string;
  }): Promise<ApiResponse<any>> => {
    return api.post('/pdos/queryGroupVisualizationData', params).then(res => res.data);
  },

  // 获取订单流程步骤
  getOrderSteps: (orderId: number): Promise<ApiResponse<ProcessStep[]>> => {
    return api.get(`/pdos/order/${orderId}/steps`).then(res => res.data);
  },

  // 获取统计数据
  getStatistics: (): Promise<ApiResponse<Statistics>> => {
    return api.get('/pdos/statistics').then(res => res.data);
  },
};

export const productApi = {
  // 获取产品列表
  getProductList: (params?: { productName?: string; productType?: string; status?: number }): Promise<ApiResponse<Product[]>> => {
    return api.get('/pdos/products', { params }).then(res => res.data);
  },
};

export default api;