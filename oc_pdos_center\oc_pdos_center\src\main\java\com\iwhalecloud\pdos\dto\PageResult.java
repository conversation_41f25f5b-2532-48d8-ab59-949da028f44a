package com.iwhalecloud.pdos.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 分页结果类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageResult<T> {
    
    /**
     * 当前页码（从1开始）
     */
    private Integer pageNum;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 总页数
     */
    private Integer totalPages;
    
    /**
     * 数据列表
     */
    private List<T> data;
    
    /**
     * 是否有下一页
     */
    private Boolean hasNext;
    
    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;
    
    /**
     * 是否为第一页
     */
    private Boolean isFirst;
    
    /**
     * 是否为最后一页
     */
    private Boolean isLast;
    
    /**
     * 从Spring Data Page对象构建PageResult
     */
    public static <T> PageResult<T> of(Page<T> page) {
        PageResult<T> result = new PageResult<>();
        result.setPageNum(page.getNumber() + 1); // Spring Data页码从0开始，转换为从1开始
        result.setPageSize(page.getSize());
        result.setTotal(page.getTotalElements());
        result.setTotalPages(page.getTotalPages());
        result.setData(page.getContent());
        result.setHasNext(page.hasNext());
        result.setHasPrevious(page.hasPrevious());
        result.setIsFirst(page.isFirst());
        result.setIsLast(page.isLast());
        return result;
    }
    
    /**
     * 构建空的分页结果
     */
    public static <T> PageResult<T> empty(Integer pageNum, Integer pageSize) {
        PageResult<T> result = new PageResult<>();
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);
        result.setTotal(0L);
        result.setTotalPages(0);
        result.setData(List.of());
        result.setHasNext(false);
        result.setHasPrevious(false);
        result.setIsFirst(true);
        result.setIsLast(true);
        return result;
    }
}