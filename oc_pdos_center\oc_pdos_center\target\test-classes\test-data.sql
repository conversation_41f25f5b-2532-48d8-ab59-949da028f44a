-- 测试数据初始化SQL
-- 用于集成测试的基础数据

-- 清理现有数据
DELETE FROM t_process_step;
DELETE FROM t_order;
DELETE FROM t_product;

-- 插入测试产品数据
INSERT INTO t_product (id, product_code, product_name, product_type, status, description, create_time, update_time, create_by, update_by) VALUES
(1, 'PROD001', '5G专线产品', 'NETWORK', 1, '5G专线接入产品', '2024-01-01 00:00:00', '2024-01-01 00:00:00', 'system', 'system'),
(2, 'PROD002', '云专线产品', 'CLOUD', 1, '云专线接入产品', '2024-01-01 00:00:00', '2024-01-01 00:00:00', 'system', 'system'),
(3, 'PROD003', 'SD-WAN产品', 'NETWORK', 1, 'SD-WAN网络产品', '2024-01-01 00:00:00', '2024-01-01 00:00:00', 'system', 'system');

-- 插入测试订单数据
INSERT INTO t_order (id, order_no, cust_order_no, product_id, product_name, customer_id, customer_name, order_status, order_amount, province_code, province_name, city_code, city_name, business_type, process_instance_id, current_step, description, create_time, update_time, complete_time, create_by, update_by) VALUES
(1, 'ORDER001', 'CUST001', 1, '5G专线产品', 'CUSTOMER001', '测试客户1', 1, 10000.00, '110000', '北京市', '110100', '东城区', 'NEW_BUSINESS', 'PROC001', 'STEP001', '测试订单1', '2024-01-15 10:00:00', '2024-01-15 10:00:00', NULL, 'user1', 'user1'),
(2, 'ORDER002', 'CUST002', 2, '云专线产品', 'CUSTOMER002', '测试客户2', 2, 20000.00, '310000', '上海市', '310100', '黄浦区', 'CHANGE_BUSINESS', 'PROC002', 'STEP002', '测试订单2', '2024-01-16 11:00:00', '2024-01-16 11:00:00', '2024-01-16 15:00:00', 'user2', 'user2'),
(3, 'ORDER003', 'CUST003', 3, 'SD-WAN产品', 'CUSTOMER003', '测试客户3', 0, 15000.00, '440000', '广东省', '440100', '广州市', 'NEW_BUSINESS', 'PROC003', 'STEP001', '测试订单3', '2024-01-17 09:00:00', '2024-01-17 09:00:00', NULL, 'user3', 'user3'),
(4, 'ORDER004', 'CUST004', 1, '5G专线产品', 'CUSTOMER004', '测试客户4', 3, 12000.00, '110000', '北京市', '110200', '西城区', 'CANCEL_BUSINESS', 'PROC004', 'STEP003', '测试订单4', '2024-01-18 14:00:00', '2024-01-18 14:00:00', NULL, 'user4', 'user4'),
(5, 'ORDER005', 'CUST005', 2, '云专线产品', 'CUSTOMER005', '测试客户5', 4, 25000.00, '320000', '江苏省', '320100', '南京市', 'NEW_BUSINESS', 'PROC005', 'STEP002', '测试订单5', '2024-01-19 16:00:00', '2024-01-19 16:00:00', NULL, 'user5', 'user5');

-- 插入测试流程步骤数据
INSERT INTO t_process_step (id, order_id, order_no, step_code, step_name, step_status, step_type, step_order, handler, handler_dept, start_time, end_time, duration_minutes, step_result, remark, error_message, create_time, update_time, create_by, update_by) VALUES
-- ORDER001的流程步骤
(1, 1, 'ORDER001', 'STEP001', '省CRM受理', 2, 'PROVINCE_CRM', 1, 'handler1', '省公司CRM部', '2024-01-15 10:00:00', '2024-01-15 10:30:00', 30, 'SUCCESS', '受理成功', NULL, '2024-01-15 10:00:00', '2024-01-15 10:30:00', 'system', 'system'),
(2, 1, 'ORDER001', 'STEP002', '省编排处理', 1, 'PROVINCE_ORCHESTRATION', 2, 'handler2', '省公司编排部', '2024-01-15 10:30:00', NULL, NULL, NULL, '处理中', NULL, '2024-01-15 10:30:00', '2024-01-15 10:30:00', 'system', 'system'),
(3, 1, 'ORDER001', 'STEP003', '集团编排处理', 0, 'GROUP_ORCHESTRATION', 3, 'handler3', '集团编排部', NULL, NULL, NULL, NULL, '等待处理', NULL, '2024-01-15 10:30:00', '2024-01-15 10:30:00', 'system', 'system'),
(4, 1, 'ORDER001', 'STEP004', '省派单处理', 0, 'PROVINCE_DISPATCH', 4, 'handler4', '省公司派单部', NULL, NULL, NULL, NULL, '等待处理', NULL, '2024-01-15 10:30:00', '2024-01-15 10:30:00', 'system', 'system'),

-- ORDER002的流程步骤
(5, 2, 'ORDER002', 'STEP001', '省CRM受理', 2, 'PROVINCE_CRM', 1, 'handler1', '省公司CRM部', '2024-01-16 11:00:00', '2024-01-16 11:20:00', 20, 'SUCCESS', '受理成功', NULL, '2024-01-16 11:00:00', '2024-01-16 11:20:00', 'system', 'system'),
(6, 2, 'ORDER002', 'STEP002', '省编排处理', 2, 'PROVINCE_ORCHESTRATION', 2, 'handler2', '省公司编排部', '2024-01-16 11:20:00', '2024-01-16 12:00:00', 40, 'SUCCESS', '处理成功', NULL, '2024-01-16 11:20:00', '2024-01-16 12:00:00', 'system', 'system'),
(7, 2, 'ORDER002', 'STEP003', '集团编排处理', 2, 'GROUP_ORCHESTRATION', 3, 'handler3', '集团编排部', '2024-01-16 12:00:00', '2024-01-16 14:00:00', 120, 'SUCCESS', '处理成功', NULL, '2024-01-16 12:00:00', '2024-01-16 14:00:00', 'system', 'system'),
(8, 2, 'ORDER002', 'STEP004', '省派单处理', 2, 'PROVINCE_DISPATCH', 4, 'handler4', '省公司派单部', '2024-01-16 14:00:00', '2024-01-16 15:00:00', 60, 'SUCCESS', '处理成功', NULL, '2024-01-16 14:00:00', '2024-01-16 15:00:00', 'system', 'system'),

-- ORDER003的流程步骤
(9, 3, 'ORDER003', 'STEP001', '省CRM受理', 4, 'PROVINCE_CRM', 1, 'handler1', '省公司CRM部', '2024-01-17 09:00:00', '2024-01-17 09:15:00', 15, 'FAILED', '受理失败', '客户信息不完整', '2024-01-17 09:00:00', '2024-01-17 09:15:00', 'system', 'system'),

-- ORDER004的流程步骤
(10, 4, 'ORDER004', 'STEP001', '省CRM受理', 2, 'PROVINCE_CRM', 1, 'handler1', '省公司CRM部', '2024-01-18 14:00:00', '2024-01-18 14:25:00', 25, 'SUCCESS', '受理成功', NULL, '2024-01-18 14:00:00', '2024-01-18 14:25:00', 'system', 'system'),
(11, 4, 'ORDER004', 'STEP002', '省编排处理', 2, 'PROVINCE_ORCHESTRATION', 2, 'handler2', '省公司编排部', '2024-01-18 14:25:00', '2024-01-18 15:00:00', 35, 'SUCCESS', '处理成功', NULL, '2024-01-18 14:25:00', '2024-01-18 15:00:00', 'system', 'system'),
(12, 4, 'ORDER004', 'STEP003', '集团编排处理', 3, 'GROUP_ORCHESTRATION', 3, 'handler3', '集团编排部', '2024-01-18 15:00:00', '2024-01-18 15:10:00', 10, 'CANCELLED', '订单已取消', '客户主动取消', '2024-01-18 15:00:00', '2024-01-18 15:10:00', 'system', 'system'),

-- ORDER005的流程步骤
(13, 5, 'ORDER005', 'STEP001', '省CRM受理', 2, 'PROVINCE_CRM', 1, 'handler1', '省公司CRM部', '2024-01-19 16:00:00', '2024-01-19 16:30:00', 30, 'SUCCESS', '受理成功', NULL, '2024-01-19 16:00:00', '2024-01-19 16:30:00', 'system', 'system'),
(14, 5, 'ORDER005', 'STEP002', '省编排处理', 4, 'PROVINCE_ORCHESTRATION', 2, 'handler2', '省公司编排部', '2024-01-19 16:30:00', '2024-01-19 17:00:00', 30, 'FAILED', '处理失败', '系统异常', '2024-01-19 16:30:00', '2024-01-19 17:00:00', 'system', 'system');

-- 重置序列（如果使用自增ID）
-- ALTER SEQUENCE products_seq RESTART WITH 4;
-- ALTER SEQUENCE orders_seq RESTART WITH 6;
-- ALTER SEQUENCE process_steps_seq RESTART WITH 15;