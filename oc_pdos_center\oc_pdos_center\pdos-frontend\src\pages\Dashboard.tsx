import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Spin, message } from 'antd';
import { ShoppingCartOutlined, CalendarOutlined, BarChartOutlined, UserOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import { orderApi } from '../services/api';
import { Statistics } from '../types';

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [statistics, setStatistics] = useState<Statistics | null>(null);

  useEffect(() => {
    fetchStatistics();
  }, []);

  const fetchStatistics = async () => {
    try {
      setLoading(true);
      const response = await orderApi.getStatistics();
      setStatistics(response.data);
    } catch (error) {
      message.error('获取统计数据失败: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  // 订单状态分布图表配置
  const getOrderStatusChartOption = () => {
    if (!statistics?.orderStatusStats) return {};
    
    const data = Object.entries(statistics.orderStatusStats).map(([name, value]) => ({
      name,
      value,
    }));

    return {
      title: {
        text: '订单状态分布',
        left: 'center',
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        left: 'left',
      },
      series: [
        {
          name: '订单状态',
          type: 'pie',
          radius: '50%',
          data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    };
  };

  // 省份订单分布图表配置
  const getProvinceChartOption = () => {
    if (!statistics?.provinceStats) return {};
    
    const provinces = Object.keys(statistics.provinceStats);
    const values = Object.values(statistics.provinceStats);

    return {
      title: {
        text: '省份订单分布',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      xAxis: {
        type: 'category',
        data: provinces,
        axisLabel: {
          rotate: 45,
        },
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          name: '订单数量',
          type: 'bar',
          data: values,
          itemStyle: {
            color: '#1890ff',
          },
        },
      ],
    };
  };

  // 产品订单分布图表配置
  const getProductChartOption = () => {
    if (!statistics?.productStats) return {};
    
    const products = Object.keys(statistics.productStats);
    const values = Object.values(statistics.productStats);

    return {
      title: {
        text: '产品订单分布',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      xAxis: {
        type: 'category',
        data: products,
        axisLabel: {
          rotate: 45,
          interval: 0,
        },
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          name: '订单数量',
          type: 'bar',
          data: values,
          itemStyle: {
            color: '#52c41a',
          },
        },
      ],
    };
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <h1 style={{ marginBottom: '24px', fontSize: '24px', fontWeight: 'bold' }}>
        战新业务端到端监控
      </h1>
      
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总订单数"
              value={statistics?.totalOrders || 0}
              prefix={<ShoppingCartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日新增"
              value={statistics?.todayOrders || 0}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="本月新增"
              value={statistics?.monthOrders || 0}
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃客户"
              value={Math.floor((statistics?.totalOrders || 0) * 0.7)}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={16}>
        <Col span={8}>
          <Card title="订单状态分布" style={{ height: '400px' }}>
            <ReactECharts
              option={getOrderStatusChartOption()}
              style={{ height: '320px' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="省份订单分布" style={{ height: '400px' }}>
            <ReactECharts
              option={getProvinceChartOption()}
              style={{ height: '320px' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="产品订单分布" style={{ height: '400px' }}>
            <ReactECharts
              option={getProductChartOption()}
              style={{ height: '320px' }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;