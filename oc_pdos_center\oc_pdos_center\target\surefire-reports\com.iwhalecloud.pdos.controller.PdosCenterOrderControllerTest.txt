-------------------------------------------------------------------------------
Test set: com.iwhalecloud.pdos.controller.PdosCenterOrderControllerTest
-------------------------------------------------------------------------------
Tests run: 5, Failures: 1, Errors: 0, Skipped: 0, Time elapsed: 11.92 s <<< FAILURE! -- in com.iwhalecloud.pdos.controller.PdosCenterOrderControllerTest
com.iwhalecloud.pdos.controller.PdosCenterOrderControllerTest.testQueryCenterOrderList_Success -- Time elapsed: 0.115 s <<< FAILURE!
java.lang.AssertionError: JSON path "$.code" expected:<0> but was:<-1>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:59)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:122)
	at org.springframework.test.util.JsonPathExpectationsHelper.assertValue(JsonPathExpectationsHelper.java:123)
	at org.springframework.test.web.servlet.result.JsonPathResultMatchers.lambda$value$2(JsonPathResultMatchers.java:111)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.iwhalecloud.pdos.controller.PdosCenterOrderControllerTest.testQueryCenterOrderList_Success(PdosCenterOrderControllerTest.java:86)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

