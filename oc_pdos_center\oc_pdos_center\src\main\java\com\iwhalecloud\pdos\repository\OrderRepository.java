package com.iwhalecloud.pdos.repository;

import com.iwhalecloud.pdos.entity.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 订单数据访问层
 */
@Repository
public interface OrderRepository extends JpaRepository<Order, Long>, JpaSpecificationExecutor<Order> {
    
    /**
     * 根据订单号查询订单
     */
    Optional<Order> findByOrderNo(String orderNo);
    
    /**
     * 根据客户订单号查询订单
     */
    Optional<Order> findByCustOrderNo(String custOrderNo);
    
    /**
     * 根据产品ID查询订单列表
     */
    List<Order> findByProductId(Long productId);
    
    /**
     * 根据客户ID查询订单列表
     */
    List<Order> findByCustomerId(String customerId);
    
    /**
     * 根据订单状态查询订单
     */
    Page<Order> findByOrderStatus(Integer orderStatus, Pageable pageable);
    
    /**
     * 根据省份查询订单
     */
    List<Order> findByProvinceCode(String provinceCode);
    
    /**
     * 多条件查询订单
     */
    @Query("SELECT o FROM Order o WHERE " +
           "(:orderNo IS NULL OR o.orderNo LIKE %:orderNo%) AND " +
           "(:custOrderNo IS NULL OR o.custOrderNo LIKE %:custOrderNo%) AND " +
           "(:productName IS NULL OR o.productName LIKE %:productName%) AND " +
           "(:customerName IS NULL OR o.customerName LIKE %:customerName%) AND " +
           "(:orderStatus IS NULL OR o.orderStatus = :orderStatus) AND " +
           "(:provinceCode IS NULL OR o.provinceCode = :provinceCode) AND " +
           "(:businessType IS NULL OR o.businessType = :businessType) AND " +
           "(:startTime IS NULL OR o.createTime >= :startTime) AND " +
           "(:endTime IS NULL OR o.createTime <= :endTime)")
    Page<Order> findByConditions(@Param("orderNo") String orderNo,
                                @Param("custOrderNo") String custOrderNo,
                                @Param("productName") String productName,
                                @Param("customerName") String customerName,
                                @Param("orderStatus") Integer orderStatus,
                                @Param("provinceCode") String provinceCode,
                                @Param("businessType") String businessType,
                                @Param("startTime") LocalDateTime startTime,
                                @Param("endTime") LocalDateTime endTime,
                                Pageable pageable);
    
    /**
     * 统计各状态订单数量
     */
    @Query("SELECT o.orderStatus, COUNT(o) FROM Order o GROUP BY o.orderStatus")
    List<Object[]> countByOrderStatus();
    
    /**
     * 统计各省份订单数量
     */
    @Query("SELECT o.provinceName, COUNT(o) FROM Order o WHERE o.provinceName IS NOT NULL GROUP BY o.provinceName")
    List<Object[]> countByProvince();
    
    /**
     * 统计各产品订单数量
     */
    @Query("SELECT o.productName, COUNT(o) FROM Order o WHERE o.productName IS NOT NULL GROUP BY o.productName")
    List<Object[]> countByProduct();
    
    /**
     * 查询指定时间范围内的订单
     */
    @Query("SELECT o FROM Order o WHERE o.createTime BETWEEN :startTime AND :endTime ORDER BY o.createTime DESC")
    List<Order> findByCreateTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                       @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计今日订单数量
     */
    @Query("SELECT COUNT(o) FROM Order o WHERE date(o.createTime) = date('now')")
    Long countTodayOrders();
    
    /**
     * 统计本月订单数量
     */
    @Query("SELECT COUNT(o) FROM Order o WHERE strftime('%Y-%m', o.createTime) = strftime('%Y-%m', 'now')")
    Long countThisMonthOrders();
}