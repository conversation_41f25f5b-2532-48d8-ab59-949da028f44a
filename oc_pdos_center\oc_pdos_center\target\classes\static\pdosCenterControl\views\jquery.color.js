//=========zflow.color===========
(function($){
   var color = new function(){
       this.colorBlue = "#3498db"; // 蓝色
       this.colorOrange = "#e67e22"; // 橙色
       this.colorWhite = "#ffffff"; // 白色
       this.colorGreen = "#2ecc71"; // 绿色
       this.colorPurple = "#9b59b6"; // 紫色
       this.colorYellow = "#F1CD55"; // 黄色
       this.colorRed = "#e74c3c"; // 红色
       this.colorBlack = "#34495e"; // 黑色
       this.colorGray = "#95a5a6";	//灰色
       this.colorCyan = "#9FE0F6";//青色
       this.colorGreenCyan = "#98FF72";//青绿色
       this.colorBrown ="#250D03";//棕色
       this.colorMoreBlue = "#253074";//深蓝色
       this.colorLitPink = "#EDD5BD"//淡粉色
       this.colorABOLOSH = "#FF8C78";
       this.colorMoreGray = "#bdc3c7";
       this.colorLitGray = "#95a5a6";
       this.colorHardGray = "#7f8c8d";
       this.colorLOCKED = "#4FC5C7";
   };
   $.color = color;
})(jQuery);