package com.iwhalecloud.pdos.service;

import com.iwhalecloud.pdos.entity.ProcessStep;

import java.util.List;
import java.util.Optional;

/**
 * 流程步骤服务接口
 */
public interface ProcessStepService {
    
    /**
     * 获取订单的流程步骤
     */
    List<ProcessStep> getOrderSteps(Long orderId);
    
    /**
     * 根据订单号获取流程步骤
     */
    List<ProcessStep> getOrderStepsByOrderNo(String orderNo);
    
    /**
     * 获取当前进行中的步骤
     */
    Optional<ProcessStep> getCurrentStep(Long orderId);
    
    /**
     * 获取已完成的步骤
     */
    List<ProcessStep> getCompletedSteps(Long orderId);
    
    /**
     * 获取异常步骤
     */
    List<ProcessStep> getExceptionSteps(Long orderId);
    
    /**
     * 创建流程步骤
     */
    ProcessStep createProcessStep(ProcessStep processStep);
    
    /**
     * 更新流程步骤
     */
    ProcessStep updateProcessStep(ProcessStep processStep);
    
    /**
     * 更新步骤状态
     */
    void updateStepStatus(Long stepId, Integer status);
    
    /**
     * 完成步骤
     */
    void completeStep(Long stepId, String result, String remark);
    
    /**
     * 标记步骤异常
     */
    void markStepError(Long stepId, String errorMessage);
    
    /**
     * 获取步骤处理时长统计
     */
    List<Object[]> getStepDurationStatistics();
    
    /**
     * 获取部门处理效率统计
     */
    List<Object[]> getDeptEfficiencyStatistics();
    
    /**
     * 获取超时步骤
     */
    List<ProcessStep> getTimeoutSteps(Long timeoutMinutes);
    
    /**
     * 获取步骤状态统计
     */
    List<Object[]> getStepStatusStatistics();
}