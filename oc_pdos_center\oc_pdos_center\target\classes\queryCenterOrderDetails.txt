{"resultCode": "0", "resultMsg": "处理成功", "systemInfo": [{"systemType": "P_CRM", "tacheGroup": [{"number": "1", "tacheName": "订单受理", "tacheCode": "P_CRM_01", "startTime": "yyyy-MM-dd:HH:mm:ss", "endTime": "yyyy-MM-dd:HH:mm:ss", "status": "3", "errorMsg": "XXX"}, {"number": "2", "tacheName": "全程报竣", "tacheCode": "P_CRM_02", "startTime": "yyyy-MM-dd:HH:mm:ss", "endTime": "yyyy-MM-dd:HH:mm:ss", "status": "3", "errorMsg": "XXX"}]}, {"systemType": "P_OSS", "tacheGroup": [{"number": "1", "tacheName": "省编排收单", "tacheCode": "P_OSS_01", "startTime": "yyyy-MM-dd:HH:mm:ss", "endTime": "yyyy-MM-dd:HH:mm:ss", "status": "3", "errorMsg": "XXX"}, {"number": "2", "tacheName": "省编排拆分", "tacheCode": "P_OSS_02", "startTime": "yyyy-MM-dd:HH:mm:ss", "endTime": "yyyy-MM-dd:HH:mm:ss", "status": "3", "errorMsg": "XXX"}, {"number": "3", "tacheName": "省派发集团", "tacheCode": "P_OSS_03", "startTime": "yyyy-MM-dd:HH:mm:ss", "endTime": "yyyy-MM-dd:HH:mm:ss", "status": "3", "errorMsg": "XXX"}, {"number": "4", "tacheName": "省编排派发综调", "tacheCode": "P_OSS_04", "startTime": "yyyy-MM-dd:HH:mm:ss", "endTime": "yyyy-MM-dd:HH:mm:ss", "status": "3", "errorMsg": "XXX"}, {"number": "5", "tacheName": "省内向CRM报竣", "tacheCode": "P_OSS_05", "startTime": "yyyy-MM-dd:HH:mm:ss", "endTime": "yyyy-MM-dd:HH:mm:ss", "status": "3", "errorMsg": "XXX"}]}, {"systemType": "P_ZD", "tacheGroup": [{"number": "1", "tacheName": "装维派单", "tacheCode": "P_ZD_01", "startTime": "yyyy-MM-dd:HH:mm:ss", "endTime": "yyyy-MM-dd:HH:mm:ss", "status": "3", "errorMsg": "XXX", "dealMan": "张三", "dealPhone": "13XXX09", "finishTime": "yyyy-MM-dd:HH:mm:ss", "bookTime": "yyyy-MM-dd:HH:mm:ss", "hurryMan": "李四", "hurryTime": "yyyy-MM-dd:HH:mm:ss", "changeBookMan": "王五", "changeBookOptTime": "yyyy-MM-dd:HH:mm:ss", "changeBookTime": "yyyy-MM-dd:HH:mm:ss", "visitTime": "yyyy-MM-dd:HH:mm:ss", "remark": "XXX"}, {"number": "2", "tacheName": "预约上门", "tacheCode": "P_ZD_02", "startTime": "yyyy-MM-dd:HH:mm:ss", "endTime": "yyyy-MM-dd:HH:mm:ss", "status": "3", "errorMsg": "XXX", "dealMan": "张三", "dealPhone": "13XXX09", "finishTime": "yyyy-MM-dd:HH:mm:ss", "bookTime": "yyyy-MM-dd:HH:mm:ss", "hurryMan": "李四", "hurryTime": "yyyy-MM-dd:HH:mm:ss", "changeBookMan": "王五", "changeBookOptTime": "yyyy-MM-dd:HH:mm:ss", "changeBookTime": "yyyy-MM-dd:HH:mm:ss", "visitTime": "yyyy-MM-dd:HH:mm:ss", "remark": "XXX"}, {"number": "3", "tacheName": "上门安装施工", "tacheCode": "P_ZD_03", "startTime": "yyyy-MM-dd:HH:mm:ss", "endTime": "yyyy-MM-dd:HH:mm:ss", "status": "3", "errorMsg": "XXX", "dealMan": "张三", "dealPhone": "13XXX09", "finishTime": "yyyy-MM-dd:HH:mm:ss", "bookTime": "yyyy-MM-dd:HH:mm:ss", "hurryMan": "李四", "hurryTime": "yyyy-MM-dd:HH:mm:ss", "changeBookMan": "王五", "changeBookOptTime": "yyyy-MM-dd:HH:mm:ss", "changeBookTime": "yyyy-MM-dd:HH:mm:ss", "visitTime": "yyyy-MM-dd:HH:mm:ss", "remark": "XXX"}, {"number": "4", "tacheName": "装维竣工", "tacheCode": "P_ZD_04", "startTime": "yyyy-MM-dd:HH:mm:ss", "endTime": "yyyy-MM-dd:HH:mm:ss", "status": "3", "errorMsg": "XXX", "dealMan": "张三", "dealPhone": "13XXX09", "finishTime": "yyyy-MM-dd:HH:mm:ss", "bookTime": "yyyy-MM-dd:HH:mm:ss", "hurryMan": "李四", "hurryTime": "yyyy-MM-dd:HH:mm:ss", "changeBookMan": "王五", "changeBookOptTime": "yyyy-MM-dd:HH:mm:ss", "changeBookTime": "yyyy-MM-dd:HH:mm:ss", "visitTime": "yyyy-MM-dd:HH:mm:ss", "remark": "XXX"}]}], "groupQueryRet": {"viewOrderInfo": {"apiName": null, "instanceNum": null, "acceptAreaCode": "8210000", "deviceName": "天翼云眼", "soCode": "450870068", "orderStatusName": "异常", "areaName": null, "children": null, "ndCode": null, "ossRecoderId": null, "orderFinishTime": null, "callbackUrl": null, "timeDifference": null, "ossApiCode": "CLOUD_EYE_ADD", "platformReportStatus": null, "finishTime": null, "orderCreateTime": null, "ossNntCode": "C_7609", "resultDesc": null, "acceptAreaName": "辽宁省", "customerName": null, "platformReportTime": null, "accessNumber": "TYYY8211200000021003", "instPartitionCode": null, "setInstId": null, "businessScen": "**********", "status": null, "instFinishTime": "2025-07-22 20:44:50.61", "isSyn": "N", "aqdnOrderLink": null, "cityCode": null, "resultCode": null, "instStatusName": null, "orderStatus": "10FE", "vCmdSetName": null, "exeSeq": null, "serialId": "tyyy8210000111297095047401", "requestId": null, "vCmdSetCode": null, "platformReportResultMsg": null, "customerId": "***********", "statusName": null, "accNbr": "***********", "crmCode": "*********", "businessScenName": "开通", "outApiCode": null, "apiId": null, "platformReportResultCode": null, "direction": null, "isSuccess": null, "professionalCompany": "视联平台", "postVcmdSetId": null, "instStatus": null, "ossNeId": null, "instCreateTime": "2025-07-22 20:44:47.784", "crmReceptionTime": "2025-06-24 15:28:04.0", "createTime": null, "userDefinedInfo": null, "ossOrderId": 9249857, "servicePk": null, "orderPartitionCode": "10000"}, "viewOrderInfoExtendVoList": [], "viewCustomerNodeList": [{"nodeOrder": 1, "nodeCode": "acceptance", "nodeName": "受理", "nodeStatus": "1", "customerPlanTacheCode": null}, {"nodeOrder": 2, "nodeCode": "configuration", "nodeName": "电信应急建采及云网配置", "nodeStatus": "1", "customerPlanTacheCode": null}, {"nodeOrder": null, "nodeCode": "install", "nodeName": "预约上门及外线安装", "nodeStatus": "0", "customerPlanTacheCode": null}, {"nodeOrder": 3, "nodeCode": "completion", "nodeName": "报竣", "nodeStatus": "0", "customerPlanTacheCode": null}], "viewStreamNodeVoList": [{"nodeOrder": 0, "nodeCode": "start", "nodeName": "开始节点", "nodeStatus": "start", "customerPlanTacheCode": null}, {"nodeOrder": 1, "nodeCode": "TYYY_tacheStart", "nodeName": "工单受理", "nodeStatus": "1", "customerPlanTacheCode": "acceptance"}, {"nodeOrder": 2, "nodeCode": "TYYY_tacheSend", "nodeName": "业务开通", "nodeStatus": "1", "customerPlanTacheCode": "configuration"}, {"nodeOrder": 3, "nodeCode": "TYYY_tacheRecv", "nodeName": "业务报竣", "nodeStatus": "1", "customerPlanTacheCode": "configuration"}, {"nodeOrder": 4, "nodeCode": "TYYY_tacheEnd", "nodeName": "给省内回单", "nodeStatus": "0", "customerPlanTacheCode": "completion"}, {"nodeOrder": 5, "nodeCode": "end", "nodeName": "结束节点", "nodeStatus": "end", "customerPlanTacheCode": null}], "viewTacheProcessVoList": [{"ossOrderId": 0, "recordId": 0, "id": "499334", "linkCode": "TYYY_tacheEnd", "linkName": "给省内回单", "linkSeq": "4", "startTime": "2025-07-22 20:44:50.515", "endTime": "2025-07-22 20:44:50.600", "senderName": "集团编排中心", "receiverName": "省IT系统", "castTime": "0.142秒", "tacheTypeName": "自动", "result": "0", "resultName": "异常", "linkUser": "李亭苇", "linkNumber": "13341996987", "customerPlanTacheCode": "completion", "sendMsg": "{\"effectDate\":\"2025-07-22 20:44:49\",\"transid\":\"tyyy8210000111297095047401\",\"resultCode\":\"0\",\"workOrderId\":\"tyyy8210000111297095047401\",\"custOrderId\":\"*********\",\"orderChannel\":\"12\",\"resultMsg\":\"成功\"}", "recvMsg": "{\"reason\":\"APPID is incorrect\",\"referenceError\":\"\",\"status\":401,\"code\":\"10002\",\"message\":\"JTEOP: Invalid APP ID in request\"}"}, {"ossOrderId": 0, "recordId": 0, "id": "499333", "linkCode": "TYYY_tacheRecv", "linkName": "业务报竣", "linkSeq": "3", "startTime": "2025-07-22 20:44:50.441", "endTime": "2025-07-22 20:44:50.458", "senderName": "视联平台", "receiverName": "集团编排中心", "castTime": "1.828秒", "tacheTypeName": "自动", "result": "1", "resultName": "成功", "linkUser": "沈志军/夏晓冬", "linkNumber": "***********/***********", "customerPlanTacheCode": "configuration", "sendMsg": "{\"uriSuffix\":\"cloudEye\",\"effectDate\":\"2025-07-22 20:44:49\",\"transid\":\"tyyy8210000111297095047401\",\"resultCode\":\"0\",\"workOrderId\":\"tyyy8210000111297095047401\",\"custOrderId\":\"*********\",\"orderChannel\":\"12\",\"resultMsg\":\"成功\"}", "recvMsg": "{\"resultCode\":\"0000\",\"resultMsg\":\"处理成功\"}"}, {"ossOrderId": 0, "recordId": 0, "id": "499332", "linkCode": "TYYY_tacheSend", "linkName": "业务开通", "linkSeq": "2", "startTime": "2025-07-22 20:44:48.435", "endTime": "2025-07-22 20:44:48.630", "senderName": "集团编排中心", "receiverName": "视联平台", "castTime": "0.861秒", "tacheTypeName": "自动", "result": "1", "resultName": "成功", "linkUser": "唐亮", "linkNumber": "***********", "customerPlanTacheCode": "configuration", "sendMsg": "{\"addTime\":\"2025-06-24 15:28:04\",\"province\":\"8210000\",\"custId\":\"***********\",\"businessAccount\":\"***********\",\"lanId\":\"8211200\",\"workOrderId\":\"tyyy8210000111297095047401\",\"custOrderId\":\"*********\",\"orderItems\":[{\"ordProdInsts\":[{\"accNum\":\"TYYY8211200000021003\",\"newAttrs\":[{\"attrId\":\"***********\",\"attrValue\":\"1000\"},{\"attrId\":\"***********\",\"attrValue\":\"1000\"},{\"attrId\":\"***********\",\"attrValue\":\"***********\"},{\"attrId\":\"***********\",\"attrValue\":\"\"},{\"attrId\":\"***********\",\"attrValue\":\"\"}],\"accProdId\":\"**********\",\"prodId\":\"**********\",\"operType\":\"1000\"},{\"accNum\":\"TYYY8211200000021003\",\"newAttrs\":[],\"accProdId\":\"**********\",\"prodId\":\"**********\",\"operType\":\"1000\"},{\"accNum\":\"TYYY8211200000021003\",\"newAttrs\":[],\"accProdId\":\"**********\",\"prodId\":\"**********\",\"operType\":\"1000\"}],\"serviceOfferId\":\"**********\"}],\"orderChannel\":\"12\"}", "recvMsg": "{\"resultCode\":\"0\",\"resultMsg\":\"成功\"}"}, {"ossOrderId": 0, "recordId": 0, "id": "499331", "linkCode": "TYYY_tacheStart", "linkName": "工单受理", "linkSeq": "1", "startTime": "2025-07-22 20:44:47.754", "endTime": "2025-07-22 20:44:47.769", "senderName": "省IT系统", "receiverName": "集团编排中心", "castTime": null, "tacheTypeName": "自动", "result": "1", "resultName": "成功", "linkUser": "沈志军/夏晓冬", "linkNumber": "***********/***********", "customerPlanTacheCode": "acceptance", "sendMsg": "\n{\"addTime\":\"2025-06-24 15:28:04\",\"province\":\"8210000\",\"custId\":\"***********\",\"businessAccount\":\"***********\",\"lanId\":\"8211200\",\"workOrderId\":\"tyyy8210000111297095047401\",\"custOrderId\":\"*********\",\"orderItems\":[{\"ordProdInsts\":[{\"accNum\":\"TYYY8211200000021003\",\"newAttrs\":[{\"attrId\":\"***********\",\"attrValue\":\"1000\"},{\"attrId\":\"***********\",\"attrValue\":\"1000\"},{\"attrId\":\"***********\",\"attrValue\":\"***********\"},{\"attrId\":\"***********\",\"attrValue\":\"\"},{\"attrId\":\"***********\",\"attrValue\":\"\"}],\"accProdId\":\"**********\",\"prodId\":\"**********\",\"operType\":\"1000\"},{\"accNum\":\"TYYY8211200000021003\",\"newAttrs\":[],\"accProdId\":\"**********\",\"prodId\":\"**********\",\"operType\":\"1000\"},{\"accNum\":\"TYYY8211200000021003\",\"newAttrs\":[],\"accProdId\":\"**********\",\"prodId\":\"**********\",\"operType\":\"1000\"}],\"serviceOfferId\":\"**********\"}],\"orderChannel\":\"12\"}\n", "recvMsg": "{\"resultCode\":\"0000\",\"resultMsg\":\"处理成功\"}"}], "longStreamFlag": true}}