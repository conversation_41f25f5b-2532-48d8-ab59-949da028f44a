import axios from 'axios';
import {
  queryOrderList,
  queryOrderDetails,
  getProducts,
} from '../orderService';
import { OrderQueryParams } from '../../types';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// 测试数据
const mockOrderListResponse = {
  data: {
    code: 200,
    message: '成功',
    data: {
      data: [
        {
          id: 1,
          orderNo: 'ORDER001',
          productName: '5G专线产品',
          customerName: '测试客户1',
          orderStatus: 1,
          provinceName: '北京市',
          cityName: '东城区',
          createTime: '2024-01-15 10:00:00',
          updateTime: '2024-01-15 10:00:00',
        },
      ],
      pageNum: 1,
      pageSize: 10,
      total: 1,
      totalPages: 1,
      hasNext: false,
      hasPrevious: false,
      isFirst: true,
      isLast: true,
    },
    timestamp: '2024-01-20 10:00:00',
  },
};

const mockOrderDetailsResponse = {
  data: {
    code: 200,
    message: '成功',
    data: {
      provinceSteps: [
        {
          id: 1,
          stepCode: 'STEP001',
          stepName: '省CRM受理',
          stepStatus: 'COMPLETED',
          startTime: '2024-01-15 10:00:00',
          endTime: '2024-01-15 10:30:00',
          durationMinutes: 30,
        },
      ],
      groupVisualization: {
        totalSteps: 4,
        completedSteps: 1,
        processingSteps: 1,
        pendingSteps: 2,
      },
    },
    timestamp: '2024-01-20 10:00:00',
  },
};

const mockProductsResponse = {
  data: {
    code: 200,
    message: '成功',
    data: [
      {
        id: 1,
        productCode: 'PROD001',
        productName: '5G专线产品',
        productType: 'NETWORK',
        status: 1,
        createTime: '2024-01-01 00:00:00',
        updateTime: '2024-01-01 00:00:00',
      },
      {
        id: 2,
        productCode: 'PROD002',
        productName: '云专线产品',
        productType: 'CLOUD',
        status: 1,
        createTime: '2024-01-01 00:00:00',
        updateTime: '2024-01-01 00:00:00',
      },
    ],
    timestamp: '2024-01-20 10:00:00',
  },
};

describe('orderService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('queryOrderList', () => {
    /**
     * 测试成功查询订单列表
     */
    test('should query order list successfully', async () => {
      // Mock axios响应
      mockedAxios.post.mockResolvedValue(mockOrderListResponse);

      const params: OrderQueryParams = {
        productCode: 'PROD001',
        orderNo: 'ORDER001',
        startTime: '2024-01-01 00:00:00',
        endTime: '2024-12-31 23:59:59',
        current: 1,
        size: 10,
      };

      const result = await queryOrderList(params);

      // 验证API调用
      expect(mockedAxios.post).toHaveBeenCalledWith(
        '/pdos/queryCenterOrderList',
        expect.objectContaining({
          productCode: 'PROD001',
          orderNumber: 'ORDER001',
          startTime: '2024-01-01 00:00:00',
          endTime: '2024-12-31 23:59:59',
          pageNum: 1,
          pageSize: 10,
        })
      );

      // 验证返回结果
      expect(result).toEqual(mockOrderListResponse.data);
    });

    /**
     * 测试查询订单列表时的参数转换
     */
    test('should convert parameters correctly', async () => {
      mockedAxios.post.mockResolvedValue(mockOrderListResponse);

      const params: OrderQueryParams = {
        productCode: 'PROD001',
        businessNumber: '12345',
        accessNumber: 'ACCESS001',
        productInstanceId: 'INST001',
        provinceCode: '110000',
        cityCode: '110100',
        startTime: '2024-01-01 00:00:00',
        endTime: '2024-12-31 23:59:59',
        current: 2,
        size: 20,
      };

      await queryOrderList(params);

      // 验证参数转换
      expect(mockedAxios.post).toHaveBeenCalledWith(
        '/pdos/queryCenterOrderList',
        expect.objectContaining({
          productCode: 'PROD001',
          businessNumber: '12345',
          accessNumber: 'ACCESS001',
          productInstanceId: 'INST001',
          provinceCode: '110000',
          cityCode: '110100',
          startTime: '2024-01-01 00:00:00',
          endTime: '2024-12-31 23:59:59',
          pageNum: 2,
          pageSize: 20,
        })
      );
    });

    /**
     * 测试API错误处理
     */
    test('should handle API errors', async () => {
      const errorMessage = 'Network Error';
      mockedAxios.post.mockRejectedValue(new Error(errorMessage));

      const params: OrderQueryParams = {
        productCode: 'PROD001',
        current: 1,
        size: 10,
      };

      await expect(queryOrderList(params)).rejects.toThrow(errorMessage);
    });

    /**
     * 测试服务器错误响应
     */
    test('should handle server error response', async () => {
      const errorResponse = {
        data: {
          code: 500,
          message: '服务器内部错误',
          data: null,
          timestamp: '2024-01-20 10:00:00',
        },
      };

      mockedAxios.post.mockResolvedValue(errorResponse);

      const params: OrderQueryParams = {
        productCode: 'PROD001',
        current: 1,
        size: 10,
      };

      const result = await queryOrderList(params);
      expect(result.code).toBe(500);
      expect(result.message).toBe('服务器内部错误');
    });
  });

  describe('queryOrderDetails', () => {
    /**
     * 测试成功查询订单详情
     */
    test('should query order details successfully', async () => {
      mockedAxios.post.mockResolvedValue(mockOrderDetailsResponse);

      const params = {
        productCode: 'PROD001',
        orderNumber: 'ORDER001',
        startTime: '2024-01-01 00:00:00',
        endTime: '2024-12-31 23:59:59',
      };

      const result = await queryOrderDetails(params);

      // 验证API调用
      expect(mockedAxios.post).toHaveBeenCalledWith(
        '/pdos/queryCenterOrderDetails',
        params
      );

      // 验证返回结果
      expect(result).toEqual(mockOrderDetailsResponse.data);
    });

    /**
     * 测试查询订单详情时的错误处理
     */
    test('should handle errors when querying order details', async () => {
      const errorMessage = 'API Error';
      mockedAxios.post.mockRejectedValue(new Error(errorMessage));

      const params = {
        productCode: 'PROD001',
        orderNumber: 'ORDER001',
      };

      await expect(queryOrderDetails(params)).rejects.toThrow(errorMessage);
    });
  });

  describe('getProducts', () => {
    /**
     * 测试成功获取产品列表
     */
    test('should get products successfully', async () => {
      mockedAxios.get.mockResolvedValue(mockProductsResponse);

      const result = await getProducts();

      // 验证API调用
      expect(mockedAxios.get).toHaveBeenCalledWith('/pdos/products');

      // 验证返回结果
      expect(result).toEqual(mockProductsResponse.data);
    });

    /**
     * 测试获取产品列表时的错误处理
     */
    test('should handle errors when getting products', async () => {
      const errorMessage = 'Network Error';
      mockedAxios.get.mockRejectedValue(new Error(errorMessage));

      await expect(getProducts()).rejects.toThrow(errorMessage);
    });

    /**
     * 测试空产品列表响应
     */
    test('should handle empty products response', async () => {
      const emptyResponse = {
        data: {
          code: 200,
          message: '成功',
          data: [],
          timestamp: '2024-01-20 10:00:00',
        },
      };

      mockedAxios.get.mockResolvedValue(emptyResponse);

      const result = await getProducts();

      expect(result.data).toEqual([]);
      expect(result.code).toBe(200);
    });
  });

  describe('API Configuration', () => {
    /**
     * 测试请求超时处理
     */
    test('should handle request timeout', async () => {
      const timeoutError = new Error('timeout of 5000ms exceeded');
      timeoutError.name = 'ECONNABORTED';
      mockedAxios.post.mockRejectedValue(timeoutError);

      const params: OrderQueryParams = {
        productCode: 'PROD001',
        current: 1,
        size: 10,
      };

      await expect(queryOrderList(params)).rejects.toThrow('timeout');
    });

    /**
     * 测试网络连接错误
     */
    test('should handle network connection error', async () => {
      const networkError = new Error('Network Error');
      networkError.name = 'NETWORK_ERROR';
      mockedAxios.post.mockRejectedValue(networkError);

      const params: OrderQueryParams = {
        productCode: 'PROD001',
        current: 1,
        size: 10,
      };

      await expect(queryOrderList(params)).rejects.toThrow('Network Error');
    });

    /**
     * 测试HTTP状态码错误
     */
    test('should handle HTTP status code errors', async () => {
      const httpError = {
        response: {
          status: 404,
          statusText: 'Not Found',
          data: {
            code: 404,
            message: '接口不存在',
          },
        },
      };

      mockedAxios.post.mockRejectedValue(httpError);

      const params: OrderQueryParams = {
        productCode: 'PROD001',
        current: 1,
        size: 10,
      };

      await expect(queryOrderList(params)).rejects.toEqual(httpError);
    });
  });

  describe('Parameter Validation', () => {
    /**
     * 测试必填参数验证
     */
    test('should validate required parameters', async () => {
      mockedAxios.post.mockResolvedValue(mockOrderListResponse);

      // 测试缺少productCode的情况
      const invalidParams = {
        current: 1,
        size: 10,
      } as OrderQueryParams;

      await queryOrderList(invalidParams);

      // 验证即使缺少必填参数，也会发送请求（参数验证应该在前端表单层面处理）
      expect(mockedAxios.post).toHaveBeenCalled();
    });

    /**
     * 测试分页参数默认值
     */
    test('should use default pagination parameters', async () => {
      mockedAxios.post.mockResolvedValue(mockOrderListResponse);

      const params: OrderQueryParams = {
        productCode: 'PROD001',
        // 不提供current和size参数
      };

      await queryOrderList(params);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        '/pdos/queryCenterOrderList',
        expect.objectContaining({
          pageNum: 1, // 默认值
          pageSize: 10, // 默认值
        })
      );
    });
  });
});