<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="gz_snapshots" />
      <option name="name" value="gz_snapshots" />
      <option name="url" value="http://10.45.47.187:8087/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="http://10.45.47.187:8087/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="cs" />
      <option name="name" value="cs" />
      <option name="url" value="http://10.45.47.187:8087/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="nj" />
      <option name="name" value="nj" />
      <option name="url" value="http://10.45.47.187:8087/nexus/content/groups/public/" />
    </remote-repository>
  </component>
</project>