﻿<!DOCTYPE html>
<html>
  <head>
    <title>可视化列表页面</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/可视化列表页面/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/可视化列表页面/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (组合) -->
      <div id="u343" class="ax_default" data-left="0" data-top="0" data-width="1868" data-height="876" layer-opacity="1">

        <!-- Unnamed (图片) -->
        <div id="u344" class="ax_default _图片 transition notrs">
          <img id="u344_img" class="img " src="images/可视化列表页面/u344.png"/>
          <div id="u344_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (图片) -->
        <div id="u345" class="ax_default _图片 transition notrs">
          <img id="u345_img" class="img " src="images/可视化列表页面/u345.png"/>
          <div id="u345_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (图片) -->
        <div id="u346" class="ax_default _图片 transition notrs">
          <img id="u346_img" class="img " src="images/可视化列表页面/u346.png"/>
          <div id="u346_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u347" class="ax_default label transition notrs">
          <div id="u347_div" class=""></div>
          <div id="u347_text" class="text ">
            <p><span>产品名称：</span></p>
          </div>
        </div>

        <!-- Unnamed (下拉框) -->
        <div id="u348" class="ax_default droplist transition notrs">
          <div id="u348_div" class=""></div>
          <select id="u348_input" class="u348_input">
            <option class="u348_input_option" value="----请选择----">----请选择----</option>
            <option class="u348_input_option" selected value="云电脑公众版">云电脑公众版</option>
            <option class="u348_input_option" value="天翼云眼">天翼云眼</option>
            <option class="u348_input_option" value="天翼视联">天翼视联</option>
            <option class="u348_input_option" value="天翼看家">天翼看家</option>
          </select>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u349" class="ax_default label transition notrs">
          <div id="u349_div" class=""></div>
          <div id="u349_text" class="text ">
            <p><span>*</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u350" class="ax_default text_field transition notrs">
          <div id="u350_div" class=""></div>
          <input id="u350_input" type="text" value="" class="u350_input"/>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u351" class="ax_default label transition notrs">
          <div id="u351_div" class=""></div>
          <div id="u351_text" class="text ">
            <p><span>业务号码：</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u352" class="ax_default text_field transition notrs">
          <div id="u352_div" class=""></div>
          <input id="u352_input" type="text" value="" class="u352_input"/>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u353" class="ax_default label transition notrs">
          <div id="u353_div" class=""></div>
          <div id="u353_text" class="text ">
            <p><span>订单流水号：</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u354" class="ax_default text_field transition notrs">
          <div id="u354_div" class=""></div>
          <input id="u354_input" type="text" value="" class="u354_input"/>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u355" class="ax_default label transition notrs">
          <div id="u355_div" class=""></div>
          <div id="u355_text" class="text ">
            <p><span>接入号：</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u356" class="ax_default text_field transition notrs">
          <div id="u356_div" class=""></div>
          <input id="u356_input" type="text" value="" class="u356_input"/>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u357" class="ax_default label transition notrs">
          <div id="u357_div" class=""></div>
          <div id="u357_text" class="text ">
            <p><span>产品实例号：</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u358" class="ax_default label transition notrs">
          <div id="u358_div" class=""></div>
          <div id="u358_text" class="text ">
            <p><span>省份：</span></p>
          </div>
        </div>

        <!-- Unnamed (下拉框) -->
        <div id="u359" class="ax_default droplist transition notrs">
          <div id="u359_div" class=""></div>
          <select id="u359_input" class="u359_input">
            <option class="u359_input_option" value="----请选择----">----请选择----</option>
          </select>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u360" class="ax_default label transition notrs">
          <div id="u360_div" class=""></div>
          <div id="u360_text" class="text ">
            <p><span>地市：</span></p>
          </div>
        </div>

        <!-- Unnamed (下拉框) -->
        <div id="u361" class="ax_default droplist transition notrs">
          <div id="u361_div" class=""></div>
          <select id="u361_input" class="u361_input">
            <option class="u361_input_option" value="----请选择----">----请选择----</option>
          </select>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u362" class="ax_default label transition notrs">
          <div id="u362_div" class=""></div>
          <div id="u362_text" class="text ">
            <p><span>*</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u363" class="ax_default text_field transition notrs">
          <div id="u363_div" class=""></div>
          <input id="u363_input" type="text" value="" class="u363_input"/>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u364" class="ax_default label transition notrs">
          <div id="u364_div" class=""></div>
          <div id="u364_text" class="text ">
            <p><span>开始时间：</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u365" class="ax_default label transition notrs">
          <div id="u365_div" class=""></div>
          <div id="u365_text" class="text ">
            <p><span>结束时间：</span></p>
          </div>
        </div>

        <!-- Unnamed (图片) -->
        <div id="u366" class="ax_default _图片 transition notrs">
          <img id="u366_img" class="img " src="images/可视化列表页面/u366.png"/>
          <div id="u366_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u367" class="ax_default text_field transition notrs">
          <div id="u367_div" class=""></div>
          <input id="u367_input" type="text" value="" class="u367_input"/>
        </div>

        <!-- Unnamed (图片) -->
        <div id="u368" class="ax_default _图片 transition notrs">
          <img id="u368_img" class="img " src="images/可视化列表页面/u366.png"/>
          <div id="u368_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (图片) -->
        <div id="u369" class="ax_default _图片 transition notrs">
          <img id="u369_img" class="img " src="images/可视化列表页面/u369.png"/>
          <div id="u369_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u370" class="ax_default label transition notrs">
          <div id="u370_div" class=""></div>
          <div id="u370_text" class="text ">
            <p><span>查询说明：云电脑公众版订单流水号、产品实例号、业务号码三选一必填</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u371" class="ax_default label transition notrs">
          <div id="u371_div" class=""></div>
          <div id="u371_text" class="text ">
            <p><span>*</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u372" class="ax_default label transition notrs">
          <div id="u372_div" class=""></div>
          <div id="u372_text" class="text ">
            <p><span>*</span></p>
          </div>
        </div>

        <!-- Unnamed (图片) -->
        <div id="u373" class="ax_default _图片 transition notrs">
          <img id="u373_img" class="img " src="images/可视化列表页面/u346.png"/>
          <div id="u373_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u374" class="ax_default label transition notrs">
          <div id="u374_div" class=""></div>
          <div id="u374_text" class="text ">
            <p><span>产品名称</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u375" class="ax_default label transition notrs">
          <div id="u375_div" class=""></div>
          <div id="u375_text" class="text ">
            <p><span>订单流水号</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u376" class="ax_default label transition notrs">
          <div id="u376_div" class=""></div>
          <div id="u376_text" class="text ">
            <p><span>省CRM订单流水号</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u377" class="ax_default label transition notrs">
          <div id="u377_div" class=""></div>
          <div id="u377_text" class="text ">
            <p><span>业务号码</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u378" class="ax_default label transition notrs">
          <div id="u378_div" class=""></div>
          <div id="u378_text" class="text ">
            <p><span>省内真实业务号码</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u379" class="ax_default label transition notrs">
          <div id="u379_div" class=""></div>
          <div id="u379_text" class="text ">
            <p><span>省份</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u380" class="ax_default label transition notrs">
          <div id="u380_div" class=""></div>
          <div id="u380_text" class="text ">
            <p><span>地市</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u381" class="ax_default label transition notrs">
          <div id="u381_div" class=""></div>
          <div id="u381_text" class="text ">
            <p><span>接入号</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u382" class="ax_default label transition notrs">
          <div id="u382_div" class=""></div>
          <div id="u382_text" class="text ">
            <p><span>省内真实接入号</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u383" class="ax_default label transition notrs">
          <div id="u383_div" class=""></div>
          <div id="u383_text" class="text ">
            <p><span>产品实例号</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u384" class="ax_default label transition notrs">
          <div id="u384_div" class=""></div>
          <div id="u384_text" class="text ">
            <p><span>省内真实产品实例号</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u385" class="ax_default label transition notrs">
          <div id="u385_div" class=""></div>
          <div id="u385_text" class="text ">
            <p><span>工单状态</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u386" class="ax_default label transition notrs">
          <div id="u386_div" class=""></div>
          <div id="u386_text" class="text ">
            <p><span>省编排唯一流水号</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u387" class="ax_default label transition notrs">
          <div id="u387_div" class=""></div>
          <div id="u387_text" class="text ">
            <p><span>受理时间</span></p>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
