













**{新增战新业务省内重点环节可视化能力}  
****需求分析说明书**

























**2025年6月4日**

目    录

1	概述	5

1.1	背景	5

1.2	需求来源	5

1.3	重要程度	5

1.4	时间要求	5

2	术语、定义	5

3	需求边界	6

4	业务描述	6

4.1	业务目标	6

4.2	业务场景	6

4.3	业务流程	6

5	系统功能	6

5.1	功能列表	6

5.2	功能描述	6

5.2.1.	界面类	6

5.2.2.	业务逻辑类	7

5.2.3.	流程类	7

5.2.4.	报表类	7

5.3	界面原型	7

5.3.1.	界面路径	7

5.3.2.	界面原型说明	7

5.3.2.1.	登录页面改造	7

5.3.2.2.	登录时的校验提示	10

5.3.2.2.1.	用户名登录	10

5.3.2.2.2.	双因子登录登录	12

5.3.2.2.3.	冻结账号或删除账号登录时	16

5.3.2.2.4.	集团服开系统增加限制同一账号多点登录校验	16

5.3.2.3.	后台数据更新逻辑	16

6	、系统接口	17

6.1	接口列表	17

6.2	接口说明	17

6.2.1.	认证接口	17

6.2.2.	发送短信验证码	21

7	信息安全	22

7.1	权限控制	22

7.2	展示加密	23

7.3	存储加密	23

7.4	传输加密	23

8	性能要求	23

8.1	页面性能	23

8.2	功能性能	23

8.3	接口性能	23

9	遗留问题	23

10	参考附件	24



**变更历史**

| **日期** | **版本** | **作者** | **变更内容** | **评审时间** |
| :---: | :---: | :---: | --- | :---: |
| 2025-6-4 | 1.0 | 王兆月 | 新建 | 2025-6 |
| 2025-6-16 | 2.0 | 王兆月 | 去掉客户查询模式 | 2025-6 |
| 2025-6-23 | 3.0 | 王兆月 | 补充接口部分说明 | 2025-6 |


<h1 id="TNUk1">1 概述 </h1>
<h2 id="D8QJC">1.1 背景 </h2>
为打造“零接触、零等待”的客户服务体验，提升客户自服务能力与感知，2024年我们已成功实现跨域组网产品的端到端开通可视化，并完成战新产品集团侧全流程开通可视化部署，使内外部用户可实时监控从订单受理、开通到交付的全周期状态，实现售中环节的可视化、可管控。2025年，我们将进一步深化战新产品的端到端开通可视化能力，覆盖天翼云眼、天翼视联、燃气卫士、智慧社区等11大核心产品，提供从订单受理、资源编排、业务平台配置到装维实施的全链路轨迹追踪与智能告警功能，赋能一线团队，显著提升战新产品的开通效率与运营效能。

<h2 id="bceFX">1.2 需求来源 </h2>
<font style="color:#000000;">集团需求：集团</font>

<h2 id="l34t4">1.3 重要程度 </h2>
<font style="color:#000000;">高。</font>

<h2 id="Kv9EP">1.4 时间要求 </h2>
2025年6月30日前完成试点省份省内环节信息查询并展示。

<h1 id="c3lVC">2 术语、定义</h1>
暂无。

<h1 id="v4oou">3 需求边界</h1>
涉及系统：集团激活、集团编排。

涉及模块（中心）：可视化。

需求局方负责人：张玉峰 云网运营部 IBOC

需求我司负责人：王兆月

系统使用对象：ICNOC、ISOC、IBOC

<h1 id="NBdc8">4 业务描述</h1>
<h2 id="dNIa5">4.1 业务目标</h2>
用户可通过业务开通参数查询省内各系统（省CRM、省编排、省综调等）的工单状态信息，并与集团编排可视化数据融合展示。如专业公司已提供开通可视化能力，则同步集成展示，实现战新业务全流程端到端监控。

分阶段实施计划：

第一阶段（单点登录与基础查询展示）：6月底完成4个产品(云电脑（公众版）、天翼云眼、天翼看家、天翼视联)支持集团激活用户单点登录，查询省内段开通信息，实现集团与省内环节的端到端状态展示。

+ 第二阶段（产品迭代及开通阶段未经过编排可视化展示）：根据实际情况可能会迭代实现手机直连卫星、燃气卫士、智慧社区、量子通信、翼支付、安全大脑省受理等产品相关功能查询能力。同时支持及开通阶段未经过编排可视化展示。
+ 第三阶段（专业公司数据集成及展示）：对接专业公司开通可视化接口，整合其开通数据，实现省内、集团及专业公司环节的全链路可视化展示。
+ 第四阶段（全角色权限扩展）：面向省内客户经理等非战新可视化用户开放查询权限，支持查询省内、集团及业务平台全流程开通信息。

<h2 id="9VRpW">4.2 业务场景</h2>
一、场景分析：

1、按用户分类，可抽象如下**两类用户**使用战新可视化页面功能：

+ 第一类、原战新可视化使用群体，如政支客户等使用场景；
+ 第二类、拓展省内客户经理使用场景；

2、场景关键区别差异：

+ 第二类客户经理使用场景，考虑省份客户经理对于流转的IT流水不如第一类群体熟悉和敏感，多半会通过时间范围、省内CRM真实客户订单编码、办理业务的客户身份证、联系方式等方式查询；
+ 第一类使用群体客户，多半不知道客户联系方式、身份证等信息，但是通常有集团监控页面权限，熟知省份送集团的相关实例流水；

3、设计难点分析：

（1）使用集团侧记录的IT实例流水查询省内系统，因流水不一致导致查询不准或者错误返回，如使用集团记录的订单流水号、产品实例号、业务号码、接入号等，

根因分析是由于当前集团、省份两级交互差异大，

+ 省编排对接集团编排；
+ 省激活对接集团编排；
+ 省采控对接集团编排；

与集团编排交互，传递的流水通常是省内系统的内部流水，省编排利用此流水无法向CRM查询出对应的订单列表；

（2）客户经理输入查询条件，如：订单流水、业务号码，往往是省内CRM能够熟知的流水，可以使用在CRM查询出信息，但是因为流水向下传递偏差，导致无法绑定集团收到省系统传过来的流水。

<h2 id="lbQAa">4.3 业务流程</h2>
![](https://cdn.nlark.com/yuque/0/2025/png/1077819/1754145529463-f0a3ac3b-2730-4d9b-b4ae-928cd9ca8ce8.png)

<h1 id="aw16P">5 系统功能</h1>
<h2 id="kPAYG">5.1 功能列表</h2>
| **一级功能** | **二级功能** | **三级功能** | **功能点** | **优先级** |
| :---: | :---: | :---: | :---: | :---: |
| | | | | |


<h2 id="eEjUG">5.2 功能描述</h2>
<h2 id="0nW8Q">5.3 列表信息查询</h2>
![](https://cdn.nlark.com/yuque/0/2025/png/1077819/1754145529715-aff921d5-f056-4e64-84d9-a70405c0aab8.png)

1. 默认显示查询条件：产品名称、接入号、订单流水号、产品实例号、业务号码、省份、地市、开始时间、结束时间；产品名称和省份必填；
2. 选择产品名称后，下方的“查询说明”内容需要相应的调整，具体内容如下：
3. 产品名称选择“云电脑公众版”显示：“云电脑公众版业务订单流水号、产品实例号、业务号码三选一必填”；
4. 产品名称选择“天翼看家”显示：“天翼看家业务订单流水号、业务号码、接入号三选一必填”；
5. 产品名称选择“天翼云眼”显示：“天翼云眼业务订单流水号、业务号码、接入号三选一必填”；
6. 产品名称选择“天翼视联”显示：“天翼视联业务订单流水号、业务号码、接入号三选一必填”；
7. 点击查询时后台需要根据产品对应的查询条件要求进行校验；
8. 所有的流水集团侧不做强一致性校验，均封装“省内段订单列表信息查询接口”的入参，调用省编排;
9. “开始时间”、“结束时间”必填，且选择时间区间时需要校验限制不能超过40天。
10. 列表信息展示字段

![](https://cdn.nlark.com/yuque/0/2025/png/1077819/1754145529910-67cfe34c-3b9f-4698-ad65-f2512d517813.png)

1. 根据查询条件作为“省内段订单列表信息查询接口”的入参，调用省编排；
2. 根据省编排返回参数，进行列表信息展示，字段包括：产品名称、省份、地市、订单流水号、省CRM订单流水号、业务号码、生呢真实业务号码、接入号、省内真实接入号、产品实例号、省内真实产品实例号、工单状态、省编排唯一流水号、受理时间（其他信息缓存）



<h2 id="mbytX">5.4 环节信息查询</h2>
列表页选择某一条记录进行环节信息可进入详情页展示，主要包含以下几部分内容：

<h3 id="wigBF">5.4.1 工单信息</h3>
![](https://cdn.nlark.com/yuque/0/2025/png/1077819/1754145530087-c34cc4c8-977e-46db-883c-40231ca96023.png)

1. 根据“战新业务省内段订单列表信息查询接口”查询出的列表信息字段在该部分展示，主要包括：产品名称、订单流水号、省CRM订单流水号、业务号码、省内真实业务号码、接入号、省内真实接入号、产品实例号、省内真实产品实例号、营业员姓名、营业员联系方式、协销人姓名、受理渠道、省编排唯一流水号、预约装机时间、预约装机地址等信息
2. 同时通过入参查询集团编排系统可视化数据库匹配未输入的参数信息（如：接口入参订单流水号，此处需要匹配出业务号码、接入号、产品实例号、业务场景、集团工单流水号）等
3. 如果上述条件在集团可视化数据库匹配出多条记录，优先取已完成的记录，如果没有已完成记录取最近时间的一条记录进行展示。

<h3 id="AlP4t">5.4.2 客户规划流程</h3>
![](https://cdn.nlark.com/yuque/0/2025/png/1077819/1754145530264-76ddc62e-64d2-475f-929d-a654f185d802.png)

1、客户规划流程与端到端流程图环节映射关系:

| <font style="color:#FFFFFF;">客户规划流程</font> | <font style="color:#FFFFFF;">端到端流程图环节</font> |
| --- | --- |
| <font style="color:#000000;">受理</font> | <font style="color:#000000;">订单受理</font> |
| <font style="color:#000000;">电信应急建采及云网配置</font> | <font style="color:#000000;">省编排收单</font> |
| <font style="color:#000000;">电信应急建采及云网配置</font> | <font style="color:#000000;">省编排拆单</font> |
| <font style="color:#000000;">电信应急建采及云网配置</font> | <font style="color:#000000;">省派发集团</font> |
| <font style="color:#000000;">电信应急建采及云网配置</font> | <font style="color:#000000;">集团编排开通</font> |
| <font style="color:#000000;">电信应急建采及云网配置</font> | <font style="color:#000000;">省编排派发综调</font> |
| <font style="color:#000000;">预约上门及外线安装</font> | <font style="color:#000000;">省内装维</font> |
| <font style="color:#000000;">报竣</font> | <font style="color:#000000;">省内向CRM报竣</font> |
| <font style="color:#000000;">报竣</font> | <font style="color:#000000;">全程报竣</font> |


2、当所属环节均为已完成时，对应的客户规划环节状态已完成

<h3 id="bBNuj">5.4.3 端到端流程图</h3>
![](https://cdn.nlark.com/yuque/0/2025/png/1077819/1754145530505-f8933bde-acb6-4023-8430-113314149d9a.png)

1. 根据“战新业务省内段环节信息查询接口”，查询省CRM、省综调、省编排环节信息，根据集团可视化接口查询集团编排内部环节信息，当环节状态为“未开始”时灰色，“执行中”时绿色，“异常”红色，“已完成”蓝色。



<h3 id="q1J8d">5.4.4 流程列表</h3>
![](https://cdn.nlark.com/yuque/0/2025/png/1077819/1754145531006-e1a48aca-c4d8-416d-b883-ccac74a9ff24.png)

1. 根据“战新业务省内段环节信息查询接口”，查询省CRM、省综调、省编排环节信息，根据集团可视化接口查询集团编排内部环节信息，并按照环节节点进行展示；
2. 集团编排流程列表信息通过集团可视化内部接口进行查询展示；
3. 流程列表按照：省CRM流程列表、省编排流程列表、集团编排流程列表、省调度流程列表顺序进行展示。

![](https://cdn.nlark.com/yuque/0/2025/png/1077819/1754145531350-5e865136-0b05-4db9-8a2f-1cc078d6a9f6.png)

在“省CRM流程列表”上方新增刷新按钮，如果环节查询失败，点击可以再调接口查询一次

<h2 id="vLqnw">5.5 界面原型</h2>
<h3 id="wwfkf">6 界面路径</h3>
<h3 id="oPq47">7 界面原型说明</h3>


<h1 id="ZRP0v">8 、系统接口</h1>
<h2 id="HuB8S">8.1 接口列表</h2>
| 序号 | 对接系统 | 接口描述 | 功能类别 | 协议版本 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1.  |  |  |  |  |  |


<h2 id="YB69P">8.2 接口说明</h2>
<h3 id="llhxv">8.2.1 查询省内环节接口：</h3>
《集团服务开通系统与省服开系统接口方案DCOOS版本V19.2.docx》

（1）新增“4.121.   新业务省内段订单列表信息查询接口”、“4.122.  新业务省内段环节信息查询接口”、

（2）新增“5.2.129战新业务产品编码”、“5.2.130战新业务环节名称及编码”、“5.2.131专业公司APID信息”

<h3 id="sH9mi">8.2.2 查询集团战新可视化PG库数据接口</h3>
通过入参查询集团编排系统可视化数据库匹配未输入的参数信息（如：接口入参订单流水号，此处需要匹配出业务号码、接入号、产品实例号、业务场景、集团工单流水号）等，如果上述条件在集团可视化数据库匹配出多条记录，<font style="color:#FF0000;">只选取一条记录返回</font>，优先取已完成的记录，如果没有已完成记录取时间区间内最近时间的一条记录进行返回。



<h4 id="suscn">8.2.2.1 输入参数</h4>
| 参数编码 | 参数名称 | 必填 | 数据类型 | 参数说明 |
| --- | --- | --- | --- | --- |
| <font style="color:rgb(0, 0, 0);">productCode</font> | <font style="color:rgb(0, 0, 0);">产品编码</font> | <font style="color:rgb(0, 0, 0);">是</font> | <font style="color:rgb(0, 0, 0);">String</font> | <font style="color:rgb(0, 0, 0);">战新业务产品编码</font> |
| <font style="color:rgb(0, 0, 0);">businessAccount</font> | <font style="color:rgb(0, 0, 0);">业务号码</font> | <font style="color:rgb(0, 0, 0);">否</font> | <font style="color:rgb(0, 0, 0);">String</font> | <font style="color:rgb(0, 0, 0);">订单流水号、业务号码、接入号、产品实例ID依据不同产品实际情况传入；</font> |
| custOrderCode | <font style="color:rgb(0, 0, 0);">订单流水号</font> | <font style="color:rgb(0, 0, 0);">否</font> | <font style="color:rgb(0, 0, 0);">String</font> | <font style="color:rgb(0, 0, 0);">订单流水号、业务号码、接入号、产品实例ID依据不同产品实际情况传入；</font> |
| <font style="color:rgb(0, 0, 0);">accNum</font> | <font style="color:rgb(0, 0, 0);">接入号</font> | <font style="color:rgb(0, 0, 0);">否</font> | <font style="color:rgb(0, 0, 0);">String</font> | <font style="color:rgb(0, 0, 0);">订单流水号、业务号码、接入号、产品实例ID依据不同产品实际情况传入；</font> |
| <font style="color:rgb(0, 0, 0);">prodInstId</font> | <font style="color:rgb(0, 0, 0);">产品实例ID</font> | <font style="color:rgb(0, 0, 0);">否</font> | <font style="color:rgb(0, 0, 0);">String</font> | <font style="color:rgb(0, 0, 0);">订单流水号、业务号码、接入号、产品实例ID依据不同产品实际情况传入；</font> |
| <font style="color:rgb(0, 0, 0);">provinceId</font> | <font style="color:rgb(0, 0, 0);">省份编码</font> | <font style="color:rgb(0, 0, 0);">是</font> | <font style="color:rgb(0, 0, 0);">String</font> |  |
| <font style="color:rgb(0, 0, 0);">areaId</font> | <font style="color:rgb(0, 0, 0);">地市编码</font> | <font style="color:rgb(0, 0, 0);">否</font> | <font style="color:rgb(0, 0, 0);">String</font> | <font style="color:rgb(0, 0, 0);">江苏必填</font> |
| <font style="color:rgb(0, 0, 0);">startTime</font> | <font style="color:rgb(0, 0, 0);">开始时间</font> | <font style="color:rgb(0, 0, 0);">是</font> | <font style="color:rgb(0, 0, 0);">String</font> | 圈定订单查询范围yyyy-MM-dd:HH:mm:ss |
| <font style="color:rgb(0, 0, 0);">endTime</font> | <font style="color:rgb(0, 0, 0);">结束时间</font> | <font style="color:rgb(0, 0, 0);">是</font> | <font style="color:rgb(0, 0, 0);">String</font> | 圈定订单查询范围yyyy-MM-dd:HH:mm:ss |




<h4 id="AHM7V">8.2.2.2 输出参数</h4>
输出为目前战新业务可视化详情页所有内容（包括基础信息、环节信息图、环节信息列表）等。

<h1 id="BIrrH">9 信息安全</h1>
<h2 id="ZRSJe">9.1 权限控制</h2>
暂无

<h2 id="cneUK">9.2 展示加密</h2>
暂无

<h2 id="08RBl">9.3 存储加密</h2>
暂无

<h2 id="DMTLD">9.4 传输加密</h2>
暂无

<h1 id="uPWkg">10 性能要求</h1>
<h2 id="aMzYd">10.1 页面性能</h2>
| **<font style="color:rgb(255, 255, 255);">页面</font>** | **<font style="color:rgb(255, 255, 255);">要求描述</font>** | **<font style="color:rgb(255, 255, 255);">性能要求</font>** |
| --- | --- | --- |
| **暂无** |  |  |




<h2 id="InCso">10.2 功能性能</h2>
| **<font style="color:rgb(255, 255, 255);">系统功能</font>** | **<font style="color:rgb(255, 255, 255);">要求描述</font>** | **<font style="color:rgb(255, 255, 255);">性能要求</font>** |
| --- | --- | --- |
| **暂无** |  |  |


<h2 id="P7KAf">10.3 接口性能</h2>
| **<font style="color:rgb(255, 255, 255);">接口名称</font>** | **<font style="color:rgb(255, 255, 255);">接口描述</font>** | **<font style="color:rgb(255, 255, 255);">性能要求</font>** |
| :---: | :---: | :---: |
| | | |
| | | |
| | | |




<h1 id="wvu4s">11 遗留问题</h1>
暂无

<h1 id="yRhWR">12 参考附件</h1>


