// 模拟API数据服务
import { ApiResponse, PageResult, Order, Product, ProcessStep, Statistics, OrderDetail } from '../types';

// 模拟产品数据
const mockProducts: Product[] = [
  {
    id: 1,
    productCode: 'CLOUD_PC_PUBLIC',
    productName: '云电脑公众版',
    productType: '云计算',
    status: 1,
    description: '面向个人用户的云桌面服务',
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00',
    createBy: 'system',
    updateBy: 'system',
  },
  {
    id: 2,
    productCode: 'CLOUD_EYE',
    productName: '天翼云眼',
    productType: 'AI服务',
    status: 1,
    description: '智能视频监控分析服务',
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00',
    createBy: 'system',
    updateBy: 'system',
  },
  {
    id: 3,
    productCode: 'SMART_HOME',
    productName: '智慧家庭',
    productType: '物联网',
    status: 1,
    description: '家庭智能化解决方案',
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00',
    createBy: 'system',
    updateBy: 'system',
  },
  {
    id: 4,
    productCode: 'ENTERPRISE_CLOUD',
    productName: '企业云',
    productType: '云计算',
    status: 1,
    description: '企业级云服务平台',
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00',
    createBy: 'system',
    updateBy: 'system',
  },
  {
    id: 5,
    productCode: '5G_PACKAGE',
    productName: '5G套餐',
    productType: '通信服务',
    status: 1,
    description: '5G网络通信套餐服务',
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00',
    createBy: 'system',
    updateBy: 'system',
  },
];

// 模拟订单数据
const mockOrders: Order[] = [
  {
    id: 1,
    orderNo: 'ORD00000001',
    custOrderNo: 'CUST00000001',
    productId: 1,
    productName: '云电脑公众版',
    customerId: 'CUST000001',
    customerName: '客户001',
    orderStatus: 1, // 1-处理中
    orderAmount: 1200.00,
    provinceName: '北京',
    provinceCode: 'BJ',
    cityName: '北京市',
    cityCode: 'BJ01',
    businessType: '新装',
    processInstanceId: 'PROC00000001',
    currentStep: '订单审核',
    description: '订单描述信息1',
    createTime: '2024-01-15 09:30:00',
    updateTime: '2024-01-15 10:30:00',
    createBy: 'system',
    updateBy: 'system',
  },
  {
    id: 2,
    orderNo: 'ORD00000002',
    custOrderNo: 'CUST00000002',
    productId: 2,
    productName: '天翼云眼',
    customerId: 'CUST000002',
    customerName: '客户002',
    orderStatus: 2, // 2-已完成
    orderAmount: 2500.00,
    provinceName: '上海',
    provinceCode: 'SH',
    cityName: '上海市',
    cityCode: 'SH01',
    businessType: '续费',
    processInstanceId: 'PROC00000002',
    currentStep: '交付完成',
    description: '订单描述信息2',
    createTime: '2024-01-14 14:20:00',
    updateTime: '2024-01-15 16:45:00',
    createBy: 'system',
    updateBy: 'system',
  },
  {
    id: 3,
    orderNo: 'ORD00000003',
    custOrderNo: 'CUST00000003',
    productId: 3,
    productName: '智慧家庭',
    customerId: 'CUST000003',
    customerName: '客户003',
    orderStatus: 0, // 0-待处理
    orderAmount: 800.00,
    provinceName: '广东',
    provinceCode: 'GD',
    cityName: '广州市',
    cityCode: 'GD01',
    businessType: '升级',
    processInstanceId: 'PROC00000003',
    currentStep: '资源分配',
    description: '订单描述信息3',
    createTime: '2024-01-16 11:15:00',
    updateTime: '2024-01-16 11:15:00',
    createBy: 'system',
    updateBy: 'system',
  },
];

// 模拟流程步骤数据
const mockProcessSteps: ProcessStep[] = [
  {
    id: 1,
    orderId: 1,
    orderNo: 'ORD00000001',
    stepCode: 'ORDER_ACCEPT',
    stepName: '订单受理',
    stepStatus: 'COMPLETED',
    stepType: 'PROVINCE_CRM',
    processor: '张三',
    processorDept: 'CRM部门',
    description: '订单受理完成',
    timestamp: '2024-01-15 09:30:00',
  },
  {
    id: 2,
    orderId: 1,
    orderNo: 'ORD00000001',
    stepCode: 'PROVINCE_RECEIVE',
    stepName: '省编排收单',
    stepStatus: 'COMPLETED',
    stepType: 'PROVINCE_ORCHESTRATION',
    processor: '李四',
    processorDept: '省编排中心',
    description: '省编排收单完成',
    timestamp: '2024-01-15 10:30:00',
  },
  {
    id: 3,
    orderId: 1,
    orderNo: 'ORD00000001',
    stepCode: 'PROVINCE_SPLIT',
    stepName: '省编排拆单',
    stepStatus: 'PROCESSING',
    stepType: 'PROVINCE_ORCHESTRATION',
    processor: '王五',
    processorDept: '省编排中心',
    description: '正在处理中',
    timestamp: '2024-01-15 11:30:00',
  },
  {
    id: 4,
    orderId: 1,
    orderNo: 'ORD00000001',
    stepCode: 'SEND_TO_GROUP',
    stepName: '省派发集团',
    stepStatus: 'PENDING',
    stepType: 'PROVINCE_ORCHESTRATION',
    processor: undefined,
    processorDept: undefined,
    description: '等待处理',
    timestamp: undefined,
  },
  {
    id: 5,
    orderId: 1,
    orderNo: 'ORD00000001',
    stepCode: 'GROUP_ORCHESTRATION',
    stepName: '集团编排开通',
    stepStatus: 'PENDING',
    stepType: 'GROUP_ORCHESTRATION',
    processor: undefined,
    processorDept: undefined,
    description: '等待处理',
    timestamp: undefined,
  },
  {
    id: 6,
    orderId: 2,
    orderNo: 'ORD00000002',
    stepCode: 'ORDER_ACCEPT',
    stepName: '订单受理',
    stepStatus: 'COMPLETED',
    stepType: 'PROVINCE_CRM',
    processor: '李四',
    processorDept: 'CRM部门',
    description: '订单受理完成',
    timestamp: '2024-01-14 14:30:00',
  },
  {
    id: 7,
    orderId: 2,
    orderNo: 'ORD00000002',
    stepCode: 'PROVINCE_RECEIVE',
    stepName: '省编排收单',
    stepStatus: 'COMPLETED',
    stepType: 'PROVINCE_ORCHESTRATION',
    processor: '王五',
    processorDept: '省编排中心',
    description: '省编排收单完成',
    timestamp: '2024-01-14 16:00:00',
  },
  {
    id: 8,
    orderId: 2,
    orderNo: 'ORD00000002',
    stepCode: 'GROUP_ORCHESTRATION',
    stepName: '集团编排开通',
    stepStatus: 'COMPLETED',
    stepType: 'GROUP_ORCHESTRATION',
    processor: '赵六',
    processorDept: '集团编排中心',
    description: '集团编排开通完成',
    timestamp: '2024-01-15 10:30:00',
  },
  {
    id: 9,
    orderId: 2,
    orderNo: 'ORD00000002',
    stepCode: 'FINAL_REPORT',
    stepName: '全程报竣',
    stepStatus: 'COMPLETED',
    stepType: 'PROVINCE_CRM',
    processor: '钱七',
    processorDept: 'CRM部门',
    description: '订单交付完成',
    timestamp: '2024-01-15 16:45:00',
  },
];

// 模拟统计数据
const mockStatistics: Statistics = {
  totalOrders: 50,
  todayOrders: 8,
  monthOrders: 35,
  orderStatusStats: {
    '待处理': 12,
    '处理中': 18,
    '已完成': 15,
    '已取消': 3,
    '异常': 2,
  },
  provinceStats: {
    '北京': 8,
    '上海': 7,
    '广东': 12,
    '江苏': 6,
    '浙江': 5,
    '山东': 4,
    '河南': 3,
    '四川': 3,
    '湖北': 2,
  },
  productStats: {
    '云电脑公众版': 15,
    '天翼云眼': 12,
    '智慧家庭': 8,
    '企业云': 10,
    '5G套餐': 5,
  },
};

// 模拟API延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 模拟API服务
export const mockOrderApi = {
  // 查询订单列表
  getOrderList: async (params: any): Promise<ApiResponse<PageResult<Order>>> => {
    await delay(500); // 模拟网络延迟
    
    let filteredOrders = [...mockOrders];
    
    // 简单的过滤逻辑
    if (params.orderNo) {
      filteredOrders = filteredOrders.filter(order => 
        order.orderNo.includes(params.orderNo)
      );
    }
    if (params.productName) {
      filteredOrders = filteredOrders.filter(order => 
        order.productName.includes(params.productName)
      );
    }
    if (params.orderStatus) {
      filteredOrders = filteredOrders.filter(order => 
        order.orderStatus === params.orderStatus
      );
    }
    
    const pageNum = params.current || 1;
    const pageSize = params.size || 10;
    const total = filteredOrders.length;
    const totalPages = Math.ceil(total / pageSize);
    const start = (pageNum - 1) * pageSize;
    const end = start + pageSize;
    const data = filteredOrders.slice(start, end);
    
    return {
      code: 200,
      message: '查询成功',
      data: {
        pageNum,
        pageSize,
        total,
        totalPages,
        data,
        hasNext: pageNum < totalPages,
        hasPrevious: pageNum > 1,
        isFirst: pageNum === 1,
        isLast: pageNum === totalPages,
      },
      timestamp: new Date().toISOString(),
    };
  },

  // 查询订单详情
  getOrderDetail: async (orderNo: string): Promise<ApiResponse<OrderDetail>> => {
    await delay(300);
    
    const order = mockOrders.find(o => o.orderNo === orderNo);
    if (!order) {
      throw new Error('订单不存在');
    }
    
    const product = mockProducts.find(p => p.id === order.productId)!;
    const steps = mockProcessSteps.filter(s => s.orderNo === orderNo);
    
    // 按stepType分组流程步骤
    const groupedProcessSteps = {
      PROVINCE_CRM: steps.filter(s => s.stepType === 'PROVINCE_CRM'),
      PROVINCE_ORCHESTRATION: steps.filter(s => s.stepType === 'PROVINCE_ORCHESTRATION'),
      GROUP_ORCHESTRATION: steps.filter(s => s.stepType === 'GROUP_ORCHESTRATION'),
      PROVINCE_DISPATCH: steps.filter(s => s.stepType === 'PROVINCE_DISPATCH'),
    };
    
    return {
      code: 200,
      message: '查询成功',
      data: {
        order,
        product,
        steps,
        processSteps: steps,
        groupedProcessSteps,
        completedSteps: steps.filter(s => s.stepStatus === 'COMPLETED').length,
        totalSteps: steps.length,
        progress: steps.length > 0 ? (steps.filter(s => s.stepStatus === 'COMPLETED').length / steps.length * 100) : 0,
      },
      timestamp: new Date().toISOString(),
    };
  },

  // 获取统计数据
  getStatistics: async (): Promise<ApiResponse<Statistics>> => {
    await delay(400);
    
    return {
      code: 200,
      message: '查询成功',
      data: mockStatistics,
      timestamp: new Date().toISOString(),
    };
  },
};

export const mockProductApi = {
  // 获取产品列表
  getProductList: async (): Promise<ApiResponse<Product[]>> => {
    await delay(200);
    
    return {
      code: 200,
      message: '查询成功',
      data: mockProducts,
      timestamp: new Date().toISOString(),
    };
  },
};