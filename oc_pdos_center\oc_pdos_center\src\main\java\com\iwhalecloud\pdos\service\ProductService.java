package com.iwhalecloud.pdos.service;

import com.iwhalecloud.pdos.dto.PageResult;
import com.iwhalecloud.pdos.entity.Product;

import java.util.List;
import java.util.Optional;

/**
 * 产品服务接口
 */
public interface ProductService {
    
    /**
     * 获取产品列表
     */
    List<Product> getProducts(String productName, String productType, Integer status);
    
    /**
     * 分页查询产品
     */
    PageResult<Product> getProductsPage(String productName, String productType, Integer status, int pageNum, int pageSize);
    
    /**
     * 根据ID查询产品
     */
    Optional<Product> getProductById(Long id);
    
    /**
     * 根据产品编码查询产品
     */
    Optional<Product> getProductByCode(String productCode);
    
    /**
     * 创建产品
     */
    Product createProduct(Product product);
    
    /**
     * 更新产品
     */
    Product updateProduct(Product product);
    
    /**
     * 删除产品
     */
    void deleteProduct(Long id);
    
    /**
     * 获取产品类型统计
     */
    List<Object[]> getProductTypeStatistics();
}