package com.iwhalecloud.pdos.repository;

import com.iwhalecloud.pdos.entity.ProcessStep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 流程步骤数据访问层
 */
@Repository
public interface ProcessStepRepository extends JpaRepository<ProcessStep, Long> {
    
    /**
     * 根据订单ID查询流程步骤
     */
    List<ProcessStep> findByOrderIdOrderByStepOrder(Long orderId);
    
    /**
     * 根据订单号查询流程步骤
     */
    List<ProcessStep> findByOrderNoOrderByStepOrder(String orderNo);
    
    /**
     * 根据步骤状态查询
     */
    List<ProcessStep> findByStepStatus(Integer stepStatus);
    
    /**
     * 根据处理人查询步骤
     */
    List<ProcessStep> findByHandler(String handler);
    
    /**
     * 根据处理部门查询步骤
     */
    List<ProcessStep> findByHandlerDept(String handlerDept);
    
    /**
     * 查询订单的当前步骤
     */
    @Query("SELECT ps FROM ProcessStep ps WHERE ps.orderId = :orderId AND ps.stepStatus = 1 ORDER BY ps.stepOrder")
    List<ProcessStep> findByOrderIdAndStepStatus(@Param("orderId") Long orderId);
    
    /**
     * 查询订单的已完成步骤
     */
    @Query("SELECT ps FROM ProcessStep ps WHERE ps.orderId = :orderId AND ps.stepStatus = 2 ORDER BY ps.stepOrder")
    List<ProcessStep> findCompletedStepsByOrderId(@Param("orderId") Long orderId);
    
    /**
     * 查询订单的异常步骤
     */
    @Query("SELECT ps FROM ProcessStep ps WHERE ps.orderId = :orderId AND ps.stepStatus = 4 ORDER BY ps.stepOrder")
    List<ProcessStep> findErrorStepsByOrderId(@Param("orderId") Long orderId);
    
    /**
     * 统计各步骤的处理时长
     */
    @Query("SELECT ps.stepName, COUNT(ps) FROM ProcessStep ps WHERE ps.stepStatus = 2 GROUP BY ps.stepName")
    List<Object[]> getStepDurationStatistics();
    
    /**
     * 统计各部门的处理效率
     */
    @Query("SELECT ps.handlerDept, COUNT(ps) FROM ProcessStep ps WHERE ps.stepStatus = 2 AND ps.handlerDept IS NOT NULL GROUP BY ps.handlerDept")
    List<Object[]> getDeptEfficiencyStatistics();
    
    /**
     * 查询超时的步骤
     */
    @Query("SELECT ps FROM ProcessStep ps WHERE ps.stepStatus = 1 AND ps.startTime < :timeoutThreshold")
    List<ProcessStep> findTimeoutSteps(@Param("timeoutThreshold") java.time.LocalDateTime timeoutThreshold);
    
    /**
     * 统计步骤状态分布
     */
    @Query("SELECT ps.stepStatus, COUNT(ps) FROM ProcessStep ps GROUP BY ps.stepStatus")
    List<Object[]> countByStepStatus();
    
    /**
     * 获取步骤状态统计
     */
    @Query("SELECT ps.stepStatus, COUNT(ps) FROM ProcessStep ps GROUP BY ps.stepStatus")
    List<Object[]> getStepStatusStatistics();
}