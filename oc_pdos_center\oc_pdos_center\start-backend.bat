@echo off
chcp 65001 >nul
echo ========================================
echo PDOS Backend Server Startup Script
echo ========================================
echo.

REM Check port availability
echo [INFO] Checking if port 8888 is available...
netstat -an | findstr ":8888" >nul
if %errorlevel% equ 0 (
    echo [ERROR] Port 8888 is already in use
    echo [ERROR] Please stop the existing service or change the port
    pause
    exit /b 1
)
echo [OK] Port 8888 is available
echo.

REM Check Java environment
echo [INFO] Checking Java environment...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Java is not installed or not in PATH
    echo [ERROR] Please install JDK 17 or higher
    pause
    exit /b 1
)
echo [OK] Java environment detected

REM Check Maven environment
echo [INFO] Checking Maven environment...
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Maven is not installed or not in PATH
    echo [ERROR] Please install Maven 3.6 or higher
    pause
    exit /b 1
)
echo [OK] Maven environment detected

REM Switch to project directory
cd /d "%~dp0"
echo [INFO] Current directory: %CD%

REM Check pom.xml file
if not exist "pom.xml" (
    echo [ERROR] pom.xml not found in current directory
    echo [ERROR] Please run this script from the project root directory
    pause
    exit /b 1
)

REM Compile project
echo [INFO] Compiling project...
mvn clean compile -q
if %errorlevel% neq 0 (
    echo [ERROR] Project compilation failed
    pause
    exit /b 1
)
echo [OK] Project compiled successfully

REM Check database file
echo [INFO] Checking database file...
if exist "pdos_center.db" (
    echo [OK] Database file found: pdos_center.db
) else (
    echo [INFO] Database file not found, will be created on first run
)
echo.

REM Start application
echo [INFO] Starting PDOS Backend Server on port 8888...
echo [INFO] Database: SQLite (pdos_center.db)
echo [INFO] API Base URL: http://localhost:8888/api
echo [INFO] Health Check: http://localhost:8888/api/health
echo [INFO] Press Ctrl+C to stop the server
echo.
echo [INFO] Starting Spring Boot application...
echo ========================================
mvn spring-boot:run

echo.
echo ========================================
echo [INFO] Server stopped
echo [INFO] Thank you for using PDOS Backend Server
pause