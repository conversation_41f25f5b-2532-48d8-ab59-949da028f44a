<!DOCTYPE html>
<html>
<head>
<meta charset=UTF-8>
<!-- 集团云网服务编排中心页面模板 -->
<link rel="stylesheet" href="../styles/pdosCenterControl.css">
<script src="../views/area.js"></script>
<script src="../views/pdosCenterControl.js"></script>
</head>
<body>
<div class="pdos-center-control">
    <div class="header">集团云网服务编排中心</div>
    <form class="query-form">
        <div class="form-grid">
            <div class="form-item">
                <label><span class="required-star">*</span>产品名称：</label>
                <select id="productNameSelect">
                  <option value="6600064002">云电脑公众版</option>
                  <option value="6620622000">天翼看家</option>
                  <option value="6620652000">天翼云眼</option>
                  <option value="6627762009">天翼视联</option>
                  <!-- <option value="6620712091">燃气卫士</option>
                  <option value="6600118011">安全大脑（省受理）</option>
                  <option value="6620712189">天翼智慧社区基础平台服务</option>
                  <option value="6620712190">天翼智慧社区应用服务</option>
                  <option value="6610040000">量子通信</option>
                  <option value="6620722012">手机直连卫星</option>
                  <option value="6600190000">翼支付</option> -->
                </select>
            </div>
            <div class="form-item">
                <label>订单流水号：</label>
                <div class="input-wrapper">
                    <input type="text" id="orderNo" />
                    <span class="clear-btn" onclick="clearInput('orderNo')">×</span>
                </div>
            </div>
            <div class="form-item">
                <label>业务号码：</label>
                <div class="input-wrapper">
                    <input type="text" id="busiNo" />
                    <span class="clear-btn" onclick="clearInput('busiNo')">×</span>
                </div>
            </div>
            <div class="form-item">
                <label>接入号：</label>
                <div class="input-wrapper">
                    <input type="text" id="accessNo" />
                    <span class="clear-btn" onclick="clearInput('accessNo')">×</span>
                </div>
            </div>
            <div class="form-item">
                <label>产品实例号：</label>
                <div class="input-wrapper">
                    <input type="text" id="instanceNo" />
                    <span class="clear-btn" onclick="clearInput('instanceNo')">×</span>
                </div>
            </div>
            <div class="form-item">
                <label><span class="required-star">*</span>省份：</label>
                <div class="input-wrapper">
                    <select id="provinceSelect"><option>---请选择---</option></select>
                    <span class="clear-btn" onclick="clearSelect('provinceSelect')">×</span>
                </div>
            </div>
            <div class="form-item">
                <label>地市：</label>
                <div class="input-wrapper">
                    <select id="citySelect"><option>---请选择---</option></select>
                    <span class="clear-btn" onclick="clearSelect('citySelect')">×</span>
                </div>
            </div>
            <div class="form-item">
                <label><span class="required-star">*</span>开始时间：</label>
                <div class="input-wrapper datetime-wrapper">
                    <input type="datetime-local" id="startDate" step="1" />
                </div>
            </div>
            <div class="form-item end-date-item">
                <label><span class="required-star">*</span>结束时间：</label>
                <div class="input-wrapper datetime-wrapper">
                    <input type="datetime-local" id="endDate" step="1" />
                </div>
            </div>
            <div class="form-item"></div>
        </div>
        <div class="form-actions form-actions-center">
            <span class="tip" id="productTipSpan">查询说明：云电脑公版订单流水号、产品实例号、业务号码三选一必填</span>
            <div class="action-btns">
                <!-- <label class="precise"><input type="checkbox" id="preciseInput" /> 精确查询</label> -->
                <button id="queryBtn" type="button">查询</button>
                <button id="exportBtn" type="button">导出</button>
                <button id="clearBtn" type="button">清空</button>
            </div>
        </div>
    </form>
    <div class="table-section">
        <!-- 表头 -->
        <div class="table-header-wrapper">
            <table class="table-header">
                <colgroup>
                    <col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px">
                </colgroup>
                <thead>
                    <tr>
                        <th title="产品名称">产品名称<span class="col-resize"></span></th>
                        <th title="省份">省份<span class="col-resize"></span></th>
                        <th title="地市">地市<span class="col-resize"></span></th>
                        <th title="订单流水号">订单流水号<span class="col-resize"></span></th>
                        <th title="省CRM订单流水号">省CRM订单流水号<span class="col-resize"></span></th>
                        <th title="业务号码">业务号码<span class="col-resize"></span></th>
                        <th title="省内真实业务号码">省内真实业务号码<span class="col-resize"></span></th>
                        <th title="接入号">接入号<span class="col-resize"></span></th>
                        <th title="省内真实接入号">省内真实接入号<span class="col-resize"></span></th>
                        <th title="产品实例号">产品实例号<span class="col-resize"></span></th>
                        <th title="省内真实产品实例号">省内真实产品实例号<span class="col-resize"></span></th>
                        <th title="工单状态">工单状态<span class="col-resize"></span></th>
                        <th title="省编排唯一流水号">省编排唯一流水号<span class="col-resize"></span></th>
                        <th title="受理时间">受理时间<span class="col-resize"></span></th>
                    </tr>
                </thead>
            </table>
        </div>
        <!-- 内容 -->
        <div class="table-body-wrapper">
            <table class="table-body">
                <colgroup>
                    <col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px"><col style="width:120px">
                </colgroup>
                <tbody id="tableSectionTbody">
                    
                </tbody>
            </table>
            <div class="no-data-tip" id="noDataTip">无数据显示</div>
        </div>
    </div>
    <div class="footer">
        <span id="footerPageInfoSpan">0共0页 0-0 共0条</span>
        <select id="footerPageSizeSelect">
          <option value="10">10</option>
          <option value="20" selected>20</option>
          <option value="30">30</option>
          <option value="50">50</option>
        </select> 每页条 跳转至<input id="footerJumpInput" type="text" style="width:40px;"/>页
    </div>
    <div class="robot-icon" style="position:fixed;bottom:20px;right:20px;"><!-- 右下角机器人 --></div>
</div>

<!-- 查询中悬浮框 -->
<div class="query-loading-overlay" id="queryLoadingOverlay">
    <div class="query-loading-box">
        <div class="query-loading-spinner"></div>
        查询中，请稍候...
    </div>
</div> 

<script>
document.addEventListener('DOMContentLoaded', function() {
    const headerTable = document.querySelector('.table-header');
    const bodyTable = document.querySelector('.table-body');
    const headerCols = headerTable.querySelectorAll('col');
    const bodyCols = bodyTable.querySelectorAll('col');
    const resizers = headerTable.querySelectorAll('.col-resize');
    const headerWrapper = document.querySelector('.table-header-wrapper');
    const bodyWrapper = document.querySelector('.table-body-wrapper');
    
    // 同步表头和表体的滚动
    headerWrapper.addEventListener('scroll', function() {
        bodyWrapper.scrollLeft = headerWrapper.scrollLeft;
    });
    
    bodyWrapper.addEventListener('scroll', function() {
        headerWrapper.scrollLeft = bodyWrapper.scrollLeft;
    });
    
    resizers.forEach((resizer, idx) => {
        resizer.addEventListener('mousedown', function(e) {
            e.preventDefault();
            e.stopPropagation();
            document.body.style.cursor = 'col-resize';
            document.body.style.userSelect = 'none';
            
            const startX = e.clientX;
            const startWidth = parseInt(headerCols[idx].style.width) || 120;
            
            function onMouseMove(e2) {
                e2.preventDefault();
                const deltaX = e2.clientX - startX;
                let newWidth = startWidth + deltaX;
                
                // 限制最小和最大宽度
                newWidth = Math.max(80, Math.min(400, newWidth));
                
                // 设置新的宽度
                headerCols[idx].style.width = newWidth + 'px';
                bodyCols[idx].style.width = newWidth + 'px';
                
                // 更新表格总宽度
                updateTableWidth();
            }
            
            function onMouseUp() {
                document.body.style.cursor = '';
                document.body.style.userSelect = '';
                document.removeEventListener('mousemove', onMouseMove);
                document.removeEventListener('mouseup', onMouseUp);
            }
            
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
        });
    });
    
    // 更新表格总宽度
    function updateTableWidth() {
        let totalWidth = 0;
        headerCols.forEach(col => {
            totalWidth += parseInt(col.style.width) || 120;
        });
        
        // 设置表格最小宽度
        headerTable.style.minWidth = totalWidth + 'px';
        bodyTable.style.minWidth = totalWidth + 'px';
    }
    
    // 将函数暴露为全局函数
    window.updateTableWidth = updateTableWidth;
    
    // 初始化表格宽度
    updateTableWidth();
});

function showNoDataTip(type) {
    var noDataTip = document.getElementById('noDataTip');
    if (!noDataTip) return;
    if (type === 'fail') {
        noDataTip.textContent = '查询失败';
        noDataTip.style.display = 'block';
    } else if (type === 'empty') {
        noDataTip.textContent = '无数据显示';
        noDataTip.style.display = 'block';
    } else {
        noDataTip.style.display = 'none';
    }
}
// 页面初始显示无数据显示
showNoDataTip('empty');
</script> 
</body>
</html> 