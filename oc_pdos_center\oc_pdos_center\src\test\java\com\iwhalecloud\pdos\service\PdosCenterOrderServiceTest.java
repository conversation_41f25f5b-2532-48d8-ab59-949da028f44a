package com.iwhalecloud.pdos.service;

import com.iwhalecloud.pdos.entity.Order;
import com.iwhalecloud.pdos.entity.ProcessStep;
import com.iwhalecloud.pdos.repository.OrderRepository;
import com.iwhalecloud.pdos.repository.ProcessStepRepository;
import com.iwhalecloud.pdos.service.impl.PdosCenterOrderServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PDOS Center Order Service Test Class
 * Test order business logic processing
 */
@ExtendWith(MockitoExtension.class)
class PdosCenterOrderServiceTest {

    @Mock
    private OrderRepository orderRepository;

    @Mock
    private ProcessStepRepository processStepRepository;

    @InjectMocks
    private PdosCenterOrderServiceImpl pdosCenterOrderService;

    private Order testOrder;
    private List<Order> testOrders;
    private ProcessStep testProcessStep;

    @BeforeEach
    void setUp() {
        // Initialize test data
        testOrder = createTestOrder();
        testOrders = Arrays.asList(testOrder);
        testProcessStep = createTestProcessStep();
    }

    @Test
    void testQueryProvinceOrderList_Success() {
        // Prepare test data
        Page<Order> orderPage = new PageImpl<>(testOrders, PageRequest.of(0, 10), 1);
        
        // Mock repository call
        when(orderRepository.findAll(any(Specification.class), any(Pageable.class)))
            .thenReturn(orderPage);
        
        // Execute test
        Page<Order> result = pdosCenterOrderService.queryProvinceOrderList(
            "PROD001", "12345", "ORDER001", "ACCESS001",
            "INST001", "110000", "110100", "2024-01-01 00:00:00", "2024-12-31 23:59:59",
            1, 10
        );
        
        // Verify results
        assertNotNull(result);
        assertEquals(1L, result.getTotalElements());
        assertNotNull(result.getContent());
        assertEquals(1, result.getContent().size());
        
        // Verify repository call
        verify(orderRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
    }

    @Test
    void testQueryProvinceOrderList_EmptyResult() {
        // Prepare empty result
        Page<Order> emptyPage = new PageImpl<>(Collections.emptyList(), PageRequest.of(0, 10), 0);
        
        // Mock repository call
        when(orderRepository.findAll(any(Specification.class), any(Pageable.class)))
            .thenReturn(emptyPage);
        
        // Execute test
        Page<Order> result = pdosCenterOrderService.queryProvinceOrderList(
            "PROD999", null, null, null,
            null, "110000", "110100", "2024-01-01 00:00:00", "2024-12-31 23:59:59",
            1, 10
        );
        
        // Verify results
        assertNotNull(result);
        assertEquals(0L, result.getTotalElements());
        assertTrue(result.getContent().isEmpty());
    }

    @Test
    void testQueryProvinceOrderDetails_Success() {
        // Prepare test data - Mock repository calls that queryProvinceOrderDetails uses internally
        when(orderRepository.findAll(any(Specification.class)))
            .thenReturn(Arrays.asList(testOrder));
        
        // Execute test
        Map<String, Object> result = pdosCenterOrderService.queryProvinceOrderDetails(
            "PROD001", "12345", "ORDER001", "ACCESS001",
            "INST001", "110000", "110100", "2024-01-01 00:00:00", "2024-12-31 23:59:59"
        );
        
        // Verify results
        assertNotNull(result);
        assertTrue(result.containsKey("provinceSteps"));
        assertTrue(result.containsKey("groupVisualizationData"));
    }

    @Test
    void testQueryGroupVisualizationData_Success() {
        // Prepare test data
        List<ProcessStep> steps = Arrays.asList(
            createProcessStepWithStatus(2), // 2-completed
            createProcessStepWithStatus(1), // 1-processing
            createProcessStepWithStatus(0)  // 0-pending
        );
        
        // Mock repository call - queryGroupVisualizationData uses mock data, no need to mock repository
        
        // Execute test
        Map<String, Object> result = pdosCenterOrderService.queryGroupVisualizationData(
            "PROD001", "12345", "ORDER001", "ACCESS001",
            "INST001", "110000", "110100", "2024-01-01 00:00:00", "2024-12-31 23:59:59"
        );
        
        // Verify results
        assertNotNull(result);
        assertTrue(result.containsKey("endToEndProcess"));
        assertTrue(result.containsKey("customerPlanProcess"));
        assertTrue(result.containsKey("processList"));
        assertTrue(result.containsKey("statistics"));
        assertTrue(result.containsKey("businessDetail"));
    }

    @Test
    void testQueryProvinceOrderList_WithPagination() {
        // Prepare pagination test data
        List<Order> moreOrders = Arrays.asList(
            createTestOrder(), createTestOrder(), createTestOrder()
        );
        Page<Order> orderPage = new PageImpl<>(moreOrders, PageRequest.of(1, 2), 5);
        
        // Mock repository call
        when(orderRepository.findAll(any(Specification.class), any(Pageable.class)))
            .thenReturn(orderPage);
        
        // Execute test
        Page<Order> result = pdosCenterOrderService.queryProvinceOrderList(
            "PROD001", null, null, null,
            null, "110000", null, "2024-01-01 00:00:00", "2024-12-31 23:59:59",
            2, 2
        );
        
        // Verify pagination results
        assertNotNull(result);
        assertEquals(5L, result.getTotalElements());
        assertEquals(3, result.getContent().size());
    }

    @Test
    void testQueryProvinceOrderList_DatabaseException() {
        // Mock repository throws exception
        when(orderRepository.findAll(any(Specification.class), any(Pageable.class)))
            .thenThrow(new RuntimeException("Database connection failed"));
        
        // Execute test and verify exception
        assertThrows(RuntimeException.class, () -> {
            pdosCenterOrderService.queryProvinceOrderList(
                "PROD001", "12345", null, null,
                null, "110000", "110100", "2024-01-01 00:00:00", "2024-12-31 23:59:59",
                1, 10
            );
        });
    }

    // Helper method: create test order
    private Order createTestOrder() {
        Order order = new Order();
        order.setId(1L);
        order.setOrderNo("ORDER001");
        order.setProductName("Test Product");
        order.setOrderStatus(1);
        order.setProvinceCode("110000");
        order.setCityCode("110100");
        order.setCreateTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());
        return order;
    }

    // Helper method: create test process step
    private ProcessStep createTestProcessStep() {
        ProcessStep step = new ProcessStep();
        step.setId(1L);
        step.setOrderId(1L);
        step.setOrderNo("ORDER001");
        step.setStepCode("STEP_001");
        step.setStepName("Test Step");
        step.setStepStatus(2); // 2-completed
        step.setCreateTime(LocalDateTime.now());
        return step;
    }
    
    // Helper method: create process step with specified status
    private ProcessStep createProcessStepWithStatus(Integer status) {
        ProcessStep step = new ProcessStep();
        step.setId(System.currentTimeMillis()); // Use timestamp to ensure unique ID
        step.setOrderId(1L);
        step.setOrderNo("ORDER001");
        step.setStepCode("STEP_" + status);
        step.setStepName("Step_" + status);
        step.setStepStatus(status);
        step.setCreateTime(LocalDateTime.now());
        return step;
    }
    
    // Helper method: create mock visualization data
    private Map<String, Object> createMockVisualizationData() {
        Map<String, Object> data = new HashMap<>();
        data.put("totalOrders", 100);
        data.put("completedOrders", 80);
        data.put("processingOrders", 15);
        data.put("pendingOrders", 5);
        return data;
    }
}