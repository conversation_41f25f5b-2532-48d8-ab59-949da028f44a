package com.iwhalecloud.pdos.repository;

import com.iwhalecloud.pdos.entity.Product;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 产品数据访问层
 */
@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {
    
    /**
     * 根据产品编码查询产品
     */
    Optional<Product> findByProductCode(String productCode);
    
    /**
     * 根据产品名称模糊查询
     */
    List<Product> findByProductNameContaining(String productName);
    
    /**
     * 根据产品类型查询
     */
    List<Product> findByProductType(String productType);
    
    /**
     * 根据状态查询产品
     */
    List<Product> findByStatus(Integer status);
    
    /**
     * 分页查询启用状态的产品
     */
    Page<Product> findByStatus(Integer status, Pageable pageable);
    
    /**
     * 根据产品名称和类型查询
     */
    @Query("SELECT p FROM Product p WHERE " +
           "(:productName IS NULL OR p.productName LIKE %:productName%) AND " +
           "(:productType IS NULL OR p.productType = :productType) AND " +
           "(:status IS NULL OR p.status = :status)")
    Page<Product> findByConditions(@Param("productName") String productName,
                                  @Param("productType") String productType,
                                  @Param("status") Integer status,
                                  Pageable pageable);
    
    /**
     * 统计各产品类型的数量
     */
    @Query("SELECT p.productType, COUNT(p) FROM Product p WHERE p.status = 1 GROUP BY p.productType")
    List<Object[]> countByProductType();
}