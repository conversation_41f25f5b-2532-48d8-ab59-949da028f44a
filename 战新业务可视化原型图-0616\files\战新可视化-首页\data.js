﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,j,k,_(l,m,n,o)),p,[],q,_(h,r),s,[t],u,_(v,w,x,y,g,z,A,_(),B,[],C,_(D,E,F,G,H,_(I,J,K,L),M,null,N,_(O,P,Q,P),R,S,T,null,U,V,W,X,Y,Z,ba,V,bb,bc,bd,_(I,J,K,be),bf,V,bg,bc,bh,_(bi,bj,bk,bl,bm,bl,bn,bl,bo,o,K,_(bp,bq,br,bq,bs,bq,bt,bu)),k,_(l,o,n,o),bv,bw),bx,_(),by,_(),bz,_(bA,[_(bB,bC,bD,h,bE,bF,x,bG,bH,bG,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),D,bT,k,_(l,m,n,bU),bV,_(bW,o,bX,bY),M,_(bZ,ca,l,m,n,bU)),bx,_(),cb,_(),cc,_(cd,ce),cf,bj,cg,bj),_(bB,ch,bD,h,bE,bF,x,bG,bH,bG,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),D,bT,k,_(l,ci,n,cj),bV,_(bW,ck,bX,cl),M,_(bZ,cm,l,ci,n,cj)),bx,_(),cb,_(),cc,_(cd,cn),cf,bj,cg,bj),_(bB,co,bD,h,bE,cp,x,cq,bH,cq,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),k,_(l,cr,n,cs),D,ct,bV,_(bW,cu,bX,cv)),bx,_(),cb,_(),cw,bj,cf,bJ,cg,bJ)])),cx,_(),cy,_(cz,_(cA,cB),cC,_(cA,cD),cE,_(cA,cF)));}; 
var b="url",c="战新可视化-首页.html",d="generationDate",e=new Date(1750061234776.761),f="defaultAdaptiveView",g="name",h="",i="cursor",j=1,k="size",l="width",m=1868,n="height",o=0,p="adaptiveViews",q="sketchKeys",r="s0",s="variables",t="OnLoadVariable",u="page",v="packageId",w="1f760a9863124b368092ac4afbeb0fba",x="type",y="Axure:Page",z="战新可视化-首页",A="notes",B="annotations",C="style",D="baseStyle",E="627587b6038d43cca051c114ac41ad32",F="pageAlignment",G="center",H="fill",I="fillType",J="solid",K="color",L=0xFFFFFFFF,M="image",N="imageAlignment",O="horizontal",P="near",Q="vertical",R="imageRepeat",S="auto",T="favicon",U="sketchFactor",V="0",W="colorStyle",X="appliedColor",Y="fontName",Z="Applied Font",ba="borderWidth",bb="borderVisibility",bc="top right bottom left",bd="borderFill",be=0xFF797979,bf="cornerRadius",bg="cornerVisibility",bh="outerShadow",bi="on",bj=false,bk="offsetX",bl=5,bm="offsetY",bn="blurRadius",bo="spread",bp="r",bq=0,br="g",bs="b",bt="a",bu=0.34901960784313724,bv="pageCursor",bw="touch",bx="adaptiveStyles",by="interactionMap",bz="diagram",bA="objects",bB="id",bC="870a96fd578d4a0eb5a7c5ee2541f3a7",bD="label",bE="friendlyType",bF="图片",bG="imageBox",bH="styleType",bI="visible",bJ=true,bK="\"Arial Normal\", \"Arial\", sans-serif",bL="fontWeight",bM="400",bN="fontStyle",bO="normal",bP="fontStretch",bQ="5",bR="foreGroundFill",bS=0xFF333333,bT="75a91ee5b9d042cfa01b8d565fe289c0",bU=963,bV="location",bW="x",bX="y",bY=1,bZ="path",ca="../../images/战新可视化-首页/u0.png",cb="imageOverrides",cc="images",cd="normal~",ce="images/战新可视化-首页/u0.png",cf="autoFitWidth",cg="autoFitHeight",ch="6f907fd31fa94613af8c0baa2b0f38d3",ci=116,cj=96,ck=1200,cl=402,cm="../../images/战新可视化-首页/u1.png",cn="images/战新可视化-首页/u1.png",co="63dfe344a74e4c94bc8bc318e4b60763",cp="矩形",cq="vectorShape",cr=117,cs=15,ct="2285372321d148ec80932747449c36c9",cu=1199,cv=536,cw="generateCompound",cx="masters",cy="objectPaths",cz="870a96fd578d4a0eb5a7c5ee2541f3a7",cA="scriptId",cB="u0",cC="6f907fd31fa94613af8c0baa2b0f38d3",cD="u1",cE="63dfe344a74e4c94bc8bc318e4b60763",cF="u2";
return _creator();
})());