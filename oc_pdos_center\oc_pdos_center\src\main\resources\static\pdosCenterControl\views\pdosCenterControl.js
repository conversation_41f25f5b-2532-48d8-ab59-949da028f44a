// 页面主逻辑脚本，负责表单交互、数据查询、表格渲染等 

// 省市数据直接用 window.provinceCityData

// 动态获取基础URL
function getBaseUrl() {
    var protocol = window.location.protocol;
    var host = window.location.host;
    var contextPath = '/OC-CONTROL-CENTER';
    return protocol + '//' + host + contextPath;
}

function showCenterTip(msg) {
    var tipDiv = document.createElement('div');
    tipDiv.className = 'center-tip-modal';
    if ('textContent' in tipDiv) {
        tipDiv.textContent = msg;
    } else {
        tipDiv.innerText = msg;
    }
    document.body.appendChild(tipDiv);
    setTimeout(function() {
        tipDiv.parentNode && tipDiv.parentNode.removeChild(tipDiv);
    }, 2000);
}

function clearInput(inputId) {
    var input = document.getElementById(inputId);
    if (input) {
        input.value = '';
        // 触发input事件（IE8不支持Event构造器，直接调用回调或略过）
        if (typeof input.oninput === 'function') input.oninput();
    }
}

function clearSelect(selectId) {
    var select = document.getElementById(selectId);
    if (select) {
        select.selectedIndex = 0;
        if (typeof select.onchange === 'function') select.onchange();
    }
}

function formatDateTimeLocal(date) {
    var y = date.getFullYear();
    var m = (date.getMonth() + 1);
    if (m < 10) m = '0' + m;
    var d = date.getDate();
    if (d < 10) d = '0' + d;
    var hh = date.getHours();
    if (hh < 10) hh = '0' + hh;
    var mm = date.getMinutes();
    if (mm < 10) mm = '0' + mm;
    var ss = date.getSeconds();
    if (ss < 10) ss = '0' + ss;
    return y + '-' + m + '-' + d + 'T' + hh + ':' + mm + ':' + ss;
}

function validateDateRange(startDate, endDate) {
    if (!startDate || !endDate) {
        showCenterTip('开始时间和结束时间不能为空');
        return false;
    }
    var start = new Date(startDate);
    var end = new Date(endDate);
    var diffTime = Math.abs(end - start);
    var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    if (diffDays > 40) {
        showCenterTip('查询时间区间不能超过40天');
        return false;
    }
    if (end < start) {
        showCenterTip('结束时间不能早于开始时间');
        return false;
    }
    return true;
}

function addDomReadyListener(fn) {
    if (document.addEventListener) {
        document.addEventListener('DOMContentLoaded', fn, false);
    } else if (document.attachEvent) {
        document.attachEvent('onreadystatechange', function() {
            if (document.readyState === 'complete') fn();
        });
    }
}

addDomReadyListener(function() {
    var provinceSelect = document.getElementById('provinceSelect');
    var citySelect = document.getElementById('citySelect');
    // 填充省份下拉
    var provinceOptions = '<option value="">---请选择---</option>';
    for (var i = 0; i < window.provinceCityData.length; i++) {
        var p = window.provinceCityData[i];
        provinceOptions += '<option value="' + p.code + '">' + p.name + '</option>';
    }
    provinceSelect.innerHTML = provinceOptions;
    citySelect.innerHTML = '<option value="">---请选择---</option>';
    provinceSelect.onchange = function() {
        var val = provinceSelect.value;
        var province = null;
        for (var i = 0; i < window.provinceCityData.length; i++) {
            if (window.provinceCityData[i].code == val) {
                province = window.provinceCityData[i];
                break;
            }
        }
        if (province) {
            var cityOptions = '<option value="">---请选择---</option>';
            for (var j = 0; j < province.cities.length; j++) {
                var c = province.cities[j];
                cityOptions += '<option value="' + c.code + '">' + c.name + '</option>';
            }
            citySelect.innerHTML = cityOptions;
        } else {
            citySelect.innerHTML = '<option value="">---请选择---</option>';
        }
    };

    // 创建隐藏的datetime-local输入框
    function createHiddenDatetimeInput(id, onChange) {
        var input = document.createElement('input');
        input.type = 'datetime-local';
        input.style.position = 'absolute';
        input.style.opacity = '0';
        input.style.pointerEvents = 'auto';
        input.style.width = '160px';
        input.style.height = '40px';
        input.style.zIndex = '9999';
        input.style.left = '-9999px';
        input.id = id + '_hidden';
        input.step = '1';
        document.body.appendChild(input);
        if (input.addEventListener) {
            input.addEventListener('change', function() {
                if (onChange) onChange(input.value);
            });
        } else if (input.attachEvent) {
            input.attachEvent('onchange', function() {
                if (onChange) onChange(input.value);
            });
        }
        return input;
    }

    function toDatetimeLocalStr(date) {
        var y = date.getFullYear();
        var m = (date.getMonth() + 1);
        if (m < 10) m = '0' + m;
        var d = date.getDate();
        if (d < 10) d = '0' + d;
        var hh = date.getHours();
        if (hh < 10) hh = '0' + hh;
        var mm = date.getMinutes();
        if (mm < 10) mm = '0' + mm;
        var ss = date.getSeconds();
        if (ss < 10) ss = '0' + ss;
        return y + '-' + m + '-' + d + 'T' + hh + ':' + mm + ':' + ss;
    }

    // 将datetime-local格式转换为后台需要的yyyy-MM-dd:HH:mm:ss格式
    function convertToBackendFormat(datetimeLocalStr) {
        if (!datetimeLocalStr) return '';
        // 将 yyyy-MM-ddTHH:mm:ss 转换为 yyyy-MM-dd:HH:mm:ss
        return datetimeLocalStr.replace('T', ':');
    }

    var startDateInput = document.getElementById('startDate');
    var endDateInput = document.getElementById('endDate');
    var now = new Date();
    var endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
    var startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    startDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), 0);
    startDateInput.value = toDatetimeLocalStr(startDate);
    endDateInput.value = toDatetimeLocalStr(endDate);

    window.clearInput = function(inputId) {
        if (inputId === 'startDate') {
            startDateInput.value = '';
        } else if (inputId === 'endDate') {
            endDateInput.value = '';
        } else {
            document.getElementById(inputId).value = '';
        }
        validateDateRange(startDateInput.value, endDateInput.value);
    };

    var productSelect = document.getElementById('productNameSelect');
    var tipSpan = document.getElementById('productTipSpan');
    var tipMap = {
        '6600064002': '查询说明：云电脑公众版业务订单流水号、产品实例号、业务号码三选一必填',
        '6620622000': '查询说明：天翼看家业务订单流水号、业务号码、接入号三选一必填',
        '6620652000': '查询说明：天翼云眼业务订单流水号、业务号码、接入号三选一必填',
        '6627762009': '查询说明：天翼视联业务订单流水号、业务号码、接入号三选一必填'
    };
    productSelect.onchange = function() {
        var val = productSelect.value;
        if ('textContent' in tipSpan) {
            tipSpan.textContent = tipMap[val] || '';
        } else {
            tipSpan.innerText = tipMap[val] || '';
        }
    };

    // 初始化时设置默认提示
    if ('textContent' in tipSpan) {
        tipSpan.textContent = tipMap['6600064002'] || '';
    } else {
        tipSpan.innerText = tipMap['6600064002'] || '';
    }

    var pageNum = 1;
    var pageSize = 20;
    var total = 0;
    // 声明全局tableData
    window.tableData = window.tableData || [];
    var tableData = [];
    var tableBody = document.getElementById('tableSectionTbody');
    var pageSizeSelect = document.getElementById('footerPageSizeSelect');
    var jumpInput = document.getElementById('footerJumpInput');
    var pageInfoSpan = document.getElementById('footerPageInfoSpan');

    function renderTable(data) {
        window.tableData = data || [];
        
        // 控制"无数据显示"区域的显示/隐藏
        var noDataTip = document.getElementById('noDataTip');
        if (noDataTip) {
            if (!data || !data.length) {
                noDataTip.style.display = 'block';
                noDataTip.textContent = '无数据显示';
            } else {
                noDataTip.style.display = 'none';
            }
        }
        
        if (!data || !data.length) {
            // 清空表格内容，不插入无数据行，让CSS中的no-data-tip显示
            tableBody.innerHTML = '';
            return;
        }
        var html = '';
        for (var i = 0; i < data.length; i++) {
            var row = data[i];
            var rowStr = encodeURIComponent(JSON.stringify(row));
            html += '<tr ondblclick="openOrderDetail(\'' + rowStr + '\')">' +
                '<td title="' + (row.productName || '') + '">' + (row.productName || '') + '</td>' +
                '<td title="' + (row.province || '') + '">' + (row.province || '') + '</td>' +
                '<td title="' + (row.city || '') + '">' + (row.city || '') + '</td>' +
                '<td title="' + (row.orderNo || '') + '">' + (row.orderNo || '') + '</td>' +
                '<td title="' + (row.crmOrderNo || '') + '">' + (row.crmOrderNo || '') + '</td>' +
                '<td title="' + (row.busiNo || '') + '">' + (row.busiNo || '') + '</td>' +
                '<td title="' + (row.realBusiNo || '') + '">' + (row.realBusiNo || '') + '</td>' +
                '<td title="' + (row.accessNo || '') + '">' + (row.accessNo || '') + '</td>' +
                '<td title="' + (row.realAccessNo || '') + '">' + (row.realAccessNo || '') + '</td>' +
                '<td title="' + (row.instanceNo || '') + '">' + (row.instanceNo || '') + '</td>' +
                '<td title="' + (row.realInstanceNo || '') + '">' + (row.realInstanceNo || '') + '</td>' +
                '<td title="' + (row.orderStatus || '') + '">' + (row.orderStatus || '') + '</td>' +
                '<td title="' + (row.serialId || '') + '">' + (row.serialId || '') + '</td>' +
                '<td title="' + (row.acceptTime || '') + '">' + (row.acceptTime || '') + '</td>' +
            '</tr>';
        }
        tableBody.innerHTML = html;
        
        // 数据渲染后更新表格宽度
        setTimeout(function() {
            var updateTableWidth = window.updateTableWidth;
            if (typeof updateTableWidth === 'function') {
                updateTableWidth();
            }
        }, 100);
    }
    function renderPageInfo() {
        var pageCount = Math.ceil(total / pageSize) || 1;
        var start = total === 0 ? 0 : (pageNum - 1) * pageSize + 1;
        var end = Math.min(pageNum * pageSize, total);
        if ('textContent' in pageInfoSpan) {
            pageInfoSpan.textContent = pageNum + '/' + pageCount + '页 ' + start + '-' + end + ' 共' + total + '条';
        } else {
            pageInfoSpan.innerText = pageNum + '/' + pageCount + '页 ' + start + '-' + end + ' 共' + total + '条';
        }
        pageSizeSelect.value = pageSize;
        jumpInput.value = '';
    }
    function doQuery(goPageNum, goPageSize) {
        pageNum = goPageNum || pageNum;
        pageSize = goPageSize || pageSize;
        var startDateVal = startDateInput.value;
        var endDateVal = endDateInput.value;
        if (!validateDateRange(startDateVal, endDateVal)) {
            return;
        }
        var product = productSelect.value;
        var orderNo = document.getElementById('orderNo').value.replace(/^\s+|\s+$/g, '');
        var instanceNo = document.getElementById('instanceNo').value.replace(/^\s+|\s+$/g, '');
        var busiNo = document.getElementById('busiNo').value.replace(/^\s+|\s+$/g, '');
        var accessNo = document.getElementById('accessNo').value.replace(/^\s+|\s+$/g, '');
        var provinceId = provinceSelect.value;
        if (!product) {
            showCenterTip('请选择产品名称');
            return;
        }
        if (!startDateVal) {
            showCenterTip('请选择开始时间');
            return;
        }
        if (!endDateVal) {
            showCenterTip('请选择结束时间');
            return;
        }
        if (!provinceId) {
            showCenterTip('请选择省份');
            return;
        }
        // 当省份为江苏时，地区号必填
        if (provinceId === '8320000') {
            var cityId = citySelect.value;
            if (!cityId) {
                showCenterTip('江苏省查询时地区号必填');
                return;
            }
        }
        if (product === '6600064002') {
            if (!orderNo && !instanceNo && !busiNo) {
                showCenterTip('云电脑公众版业务订单流水号、产品实例号、业务号码三选一必填');
                return;
            }
        } else if (product === '6620622000') {
            if (!orderNo && !busiNo && !accessNo) {
                showCenterTip('天翼看家业务订单流水号、业务号码、接入号三选一必填');
                return;
            }
        } else if (product === '6620652000') {
            if (!orderNo && !busiNo && !accessNo) {
                showCenterTip('天翼云眼业务订单流水号、业务号码、接入号三选一必填');
                return;
            }
        } else if (product === '6627762009') {
            if (!orderNo && !busiNo && !accessNo) {
                showCenterTip('天翼视联业务订单流水号、业务号码、接入号三选一必填');
                return;
            }
        }
        
        // 显示查询中悬浮框
        var loadingOverlay = document.getElementById('queryLoadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
        }
        
        var params = {
            productCode: product,
            custOrderCode: orderNo,
            businessAccount: busiNo,
            accNum: accessNo,
            prodInstId: instanceNo,
            provinceId: provinceId,
            areaId: citySelect.value,
            startTime: convertToBackendFormat(startDateVal),
            endTime: convertToBackendFormat(endDateVal),
            // precise: document.getElementById('preciseInput').checked ? 1 : 0,
            pageNum: pageNum,
            pageSize: pageSize
        };
        var xhr = window.XMLHttpRequest ? new XMLHttpRequest() : new ActiveXObject('Microsoft.XMLHTTP');
        xhr.open('POST', '/pdos/queryCenterOrderList', true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                // 隐藏查询中悬浮框
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
                
                if (xhr.status === 200) {
                    var data;
                    try {
                        data = JSON.parse(xhr.responseText);
                    } catch (e) {
                        showCenterTip('查询失败，请稍后重试');
                        total = 0;
                        renderPageInfo();
                        return;
                    }
                    var list = (data.custOrderInfo && typeof data.custOrderInfo.length === 'number') ? data.custOrderInfo : [];
                    window.tableData = [];
                    for (var i = 0; i < list.length; i++) {
                        var item = list[i];
                        var provinceName = '';
                        var cityName = '';
                        var province = null;
                        for (var p = 0; p < window.provinceCityData.length; p++) {
                            if (window.provinceCityData[p].code == item.provinceId) {
                                province = window.provinceCityData[p];
                                break;
                            }
                        }
                        if (province) {
                            provinceName = province.name;
                            var city = null;
                            // 将areaId的后两位变为00进行比较，兼容字符串和数字类型
                            var normalizedAreaId = item.areaId;
                            if (normalizedAreaId) {
                                // 确保转换为字符串
                                var areaIdStr = String(normalizedAreaId);
                                if (areaIdStr.length >= 2) {
                                    normalizedAreaId = areaIdStr.substring(0, areaIdStr.length - 2) + '00';
                                }
                            }
                            
                            for (var c = 0; province.cities && c < province.cities.length; c++) {
                                if (province.cities[c].code == normalizedAreaId) {
                                    city = province.cities[c];
                                    break;
                                }
                            }
                            if (city) cityName = city.name;
                        }
                        var rela = item.busiInstRela || {};
                        var statusMap = {
                            '0': '未开始',
                            '1': '执行中',
                            '2': '异常',
                            '3': '已完成',
                            '4': '已退单',
                            '5': '已撤单'
                        };
                        var statusText = statusMap[item.status] || (item.status || '');
                        window.tableData.push({
                            productName: item.productName || '',
                            productCode: item.productCode || product || '',
                            province: provinceName,
                            city: cityName,
                            provinceId: item.provinceId || provinceSelect.value || '',
                            areaId: item.areaId || citySelect.value || '',
                            orderNo: rela.custOrderCode || '',
                            crmOrderNo: rela.realCustOrderCode || '',
                            busiNo: item.businessAccount || '',
                            realBusiNo: rela.realBusinessAccount || '',
                            accessNo: item.accNum || '',
                            realAccessNo: rela.realAccNum || '',
                            instanceNo: item.prodInstId || '',
                            realInstanceNo: rela.realProdInstId || '',
                            orderStatus: statusText,
                            serialId: item.serialId || '',
                            acceptTime: item.appointTime || '',
                            acceptChannel: item.acceptChannel || '',
                            businessScene: item.businessScene || '',
                            saleName: item.saleName || '',
                            crmStaffName: item.crmStaffName || '',
                            crmStaffContact: item.crmStaffContact || '',
                            customerContact: item.customerContact || '',
                            customerName: item.customerName || '',
                            installAddress: item.installAddress || '',
                            appointTime: item.appointTime || '',
                            groupOrderNo: rela.custOrderCode || '',
                            custOrderCode: item.custOrderCode || '',
                            realCustOrderCode: rela.realCustOrderCode || '',
                            realBusinessAccount: rela.realBusinessAccount || '',
                            realAccNum: rela.realAccNum || '',
                            realProdInstId: rela.realProdInstId || ''
                        });
                    }
                    total = window.tableData.length;
                    renderTable(window.tableData);
                    renderPageInfo();
                } else {
                    // HTTP错误处理
                    var errorMsg = '查询失败';
                    if (xhr.status === 400) {
                        errorMsg = '请求参数错误，请检查输入';
                    } else if (xhr.status === 500) {
                        errorMsg = '根据当前条件未查询到订单数据，请调整查询条件后重新查询！';
                    } else if (xhr.status === 404) {
                        errorMsg = '接口不存在，请联系管理员';
                    } else {
                        errorMsg = '查询失败，HTTP状态码：' + xhr.status;
                    }
                    showCenterTip(errorMsg);
                    total = 0;
                    renderPageInfo();
                }
            }
        };
        xhr.onerror = function() {
            // 网络错误时也隐藏悬浮框
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }
            showCenterTip('网络错误，请稍后重试');
        };
        xhr.send(JSON.stringify(params));
    }
    var queryBtn = document.getElementById('queryBtn');
    if (queryBtn) {
        queryBtn.onclick = function() {
            pageNum = 1;
            doQuery(1, pageSize);
        };
    }
    
    // 清空按钮功能
    var clearBtn = document.getElementById('clearBtn');
    if (clearBtn) {
        clearBtn.onclick = function() {
            // 清空订单流水号、业务号码、接入号、产品实例号
            document.getElementById('orderNo').value = '';
            document.getElementById('busiNo').value = '';
            document.getElementById('accessNo').value = '';
            document.getElementById('instanceNo').value = '';
            
            // 重置省份和地市选择
            provinceSelect.selectedIndex = 0;
            citySelect.innerHTML = '<option value="">---请选择---</option>';
            
            // 重置开始时间和结束时间为页面初始值
            var now = new Date();
            var endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
            var startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            startDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), 0);
            startDateInput.value = toDatetimeLocalStr(startDate);
            endDateInput.value = toDatetimeLocalStr(endDate);
            
            // 重置产品名称为第一个选项
            productSelect.selectedIndex = 0;
            if ('textContent' in tipSpan) {
                tipSpan.textContent = tipMap['6600064002'] || '';
            } else {
                tipSpan.innerText = tipMap['6600064002'] || '';
            }
            
            // 取消精确查询勾选
            //document.getElementById('preciseInput').checked = false;
            
            showCenterTip('表单已清空');
        };
    }
    
    if (pageSizeSelect) {
        pageSizeSelect.onchange = function() {
            pageSize = parseInt(pageSizeSelect.value, 10) || 20;
            pageNum = 1;
            doQuery(1, pageSize);
        };
    }
    if (jumpInput) {
        jumpInput.onkeydown = function(e) {
            e = e || window.event;
            var key = e.keyCode || e.which;
            if (key === 13) {
                var val = parseInt(jumpInput.value, 10);
                var pageCount = Math.ceil(total / pageSize) || 1;
                if (val >= 1 && val <= pageCount) {
                    pageNum = val;
                    doQuery(pageNum, pageSize);
                } else {
                    showCenterTip('请输入1-' + pageCount + '之间的页码');
                }
            }
        };
    }
    // 新增：全局函数，供ondblclick调用
    window.openOrderDetail = function(rowStr) {
        try {
            var row = JSON.parse(decodeURIComponent(rowStr));
            // 获取当前查询条件的startTime和endTime
            var startDateInput = document.getElementById('startDate');
            var endDateInput = document.getElementById('endDate');
            row.startTime = startDateInput ? startDateInput.value : '';
            row.endTime = endDateInput ? endDateInput.value : '';
            localStorage.setItem('orderDetailData', JSON.stringify(row));
            window.open('/pdosCenterControl/templates/orderDetail.html', '_blank');
        } catch (e) {
            showCenterTip('打开工单详情失败');
        }
    };
    if (startDateInput.addEventListener) {
        startDateInput.addEventListener('change', function() {
            validateDateRange(startDateInput.value, endDateInput.value);
        });
        endDateInput.addEventListener('change', function() {
            validateDateRange(startDateInput.value, endDateInput.value);
        });
    } else if (startDateInput.attachEvent) {
        startDateInput.attachEvent('onchange', function() {
            validateDateRange(startDateInput.value, endDateInput.value);
        });
        endDateInput.attachEvent('onchange', function() {
            validateDateRange(startDateInput.value, endDateInput.value);
        });
    }

    // ====== 表格列宽拖拽功能 ======
    // 应用到主表格
    // makeTableResizable('.table-section table'); // 注释掉，因为HTML模板中已有拖拽功能
    
    // 初始化时确保"无数据显示"区域状态正确
    // renderTable([]); // 注释掉，因为HTML模板中已有初始化逻辑

    var exportBtn = document.getElementById('exportBtn');
    if (exportBtn) {
        exportBtn.onclick = function() {
            if (!window.tableData || !window.tableData.length) {
                showCenterTip('无可导出的数据');
                return;
            }
            // 生成表头
            var headers = [
                '产品名称','省份','地市','订单流水号','省CRM订单流水号','业务号码','省内真实业务号码','接入号','省内真实接入号','产品实例号','省内真实产品实例号','工单状态','省编排唯一流水号','受理时间'
            ];
            var fields = [
                'productName','province','city','orderNo','crmOrderNo','busiNo','realBusiNo','accessNo','realAccessNo','instanceNo','realInstanceNo','orderStatus','serialId','acceptTime'
            ];
            // 转为CSV字符串
            function toCsvRow(arr) {
                return arr.map(function(val) {
                    if (val == null) val = '';
                    val = String(val).replace(/"/g, '""');
                    if (val.search(/[",\n]/) >= 0) {
                        val = '"' + val + '"';
                    }
                    return val;
                }).join(',');
            }
            var csvRows = [];
            csvRows.push(toCsvRow(headers));
            for (var i = 0; i < window.tableData.length; i++) {
                var row = window.tableData[i];
                var rowArr = fields.map(function(f) { return row[f] || ''; });
                csvRows.push(toCsvRow(rowArr));
            }
            var csvContent = '\uFEFF' + csvRows.join('\r\n'); // 带BOM防止中文乱码
            var blob = new Blob([csvContent], {type: 'text/csv;charset=utf-8;'});
            var link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'PDOS战新可视化列表.csv';
            document.body.appendChild(link);
            link.click();
            setTimeout(function() {
                document.body.removeChild(link);
                URL.revokeObjectURL(link.href);
            }, 100);
        };
    }

    // 让整个输入框点击都弹出日期选择控件
    [startDateInput, endDateInput].forEach(function(input) {
        if (input && typeof input.showPicker === 'function') {
            input.addEventListener('focus', function() {
                input.showPicker();
            });
            input.addEventListener('click', function() {
                input.showPicker();
            });
        }
    });
});

// 添加拖拽手柄样式
(function() {
    var style = document.createElement('style');
    style.textContent = `
    .col-resize-handle {
        background: transparent;
        transition: background 0.2s;
    }
    .col-resize-handle:hover {
        background: #1677c7;
        opacity: 0.5;
    }
    .form-item input[type="datetime-local"] {
        font-size: 12px;
        font-family: inherit;
    }
    `;
    document.head.appendChild(style);
})(); 