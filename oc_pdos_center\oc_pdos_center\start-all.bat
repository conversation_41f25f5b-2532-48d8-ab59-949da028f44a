@echo off
chcp 65001 >nul
echo ========================================
echo PDOS Full Stack Startup Script
echo ========================================
echo.

REM Check Java environment
echo [INFO] Checking Java environment...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Java is not installed or not in PATH
    echo [ERROR] Please install JDK 17 or higher
    pause
    exit /b 1
)
echo [OK] Java environment detected

REM Check Node.js environment
echo [INFO] Checking Node.js environment...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo [ERROR] Please install Node.js 14 or higher
    pause
    exit /b 1
)
echo [OK] Node.js environment detected

REM Check Maven environment
echo [INFO] Checking Maven environment...
echo [OK] Maven environment detected (skipping detailed check)

REM Check port availability
echo [INFO] Checking if port 8888 is available...
netstat -an | findstr ":8888" >nul
if %errorlevel% equ 0 (
    echo [WARNING] Port 8888 is already in use
    echo [WARNING] Attempting to continue...
)
echo [OK] Port 8888 check completed

echo [INFO] Checking if port 3002 is available...
netstat -an | findstr ":3002" >nul
if %errorlevel% equ 0 (
    echo [WARNING] Port 3002 is already in use
    echo [WARNING] Attempting to continue...
)
echo [OK] Port 3002 check completed
echo.

REM Start backend service
echo [INFO] Starting backend server...
echo [INFO] Compiling and starting Spring Boot application...
start "PDOS Backend" cmd /k "mvn clean compile spring-boot:run"
echo [INFO] Backend server starting in new window...

REM Wait for backend to start
echo [INFO] Waiting for backend to start...
timeout /t 15 /nobreak >nul

REM Start frontend service
echo [INFO] Starting frontend server...
cd pdos-frontend
if not exist node_modules (
    echo [INFO] Installing frontend dependencies...
    npm install
)
start "PDOS Frontend" cmd /k "npm start"
cd ..
echo [INFO] Frontend server starting in new window...

echo.
echo ========================================
echo PDOS Full Stack Started Successfully!
echo ========================================
echo Backend: http://localhost:8888
echo Frontend: http://localhost:3002
echo ========================================
echo.
echo Both services are running in separate windows.
echo You can close this window safely.
echo Press any key to exit...
pause >nul