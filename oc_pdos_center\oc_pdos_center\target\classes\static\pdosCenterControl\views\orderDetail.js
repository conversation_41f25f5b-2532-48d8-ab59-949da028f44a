// 工单详情页主逻辑脚本

// zflow流程图XML数据
var customerPlanXML = '<?xml version="1.0" encoding="UTF-8"?>' +
'<Process>' +
'<Activities>' +
    '<Activity id="planNode_accept" defId="planNode_accept" name="受理" tacheId="accept" tacheCode="accept" areaId="customer" isRunning="false" type="Tache" subType="" isParallel="false" state=""/>' +
    '<Activity id="planNode_config" defId="planNode_config" name="电信应急建采及云网配置" tacheId="config" tacheCode="config" areaId="customer" isRunning="false" type="Tache" subType="" isParallel="false" state=""/>' +
    '<Activity id="planNode_install" defId="planNode_install" name="预约上门及外线安装" tacheId="install" tacheCode="install" areaId="customer" isRunning="false" type="Tache" subType="" isParallel="false" state=""/>' +
    '<Activity id="planNode_finish" defId="planNode_finish" name="报竣" tacheId="finish" tacheCode="finish" areaId="customer" isRunning="false" type="Tache" subType="" isParallel="false" state=""/>' +
'</Activities>' +
'<Transitions>' +
    '<Transition id="t1" from="planNode_accept" to="planNode_config"/>' +
    '<Transition id="t2" from="planNode_config" to="planNode_install"/>' +
    '<Transition id="t3" from="planNode_install" to="planNode_finish"/>' +
'</Transitions>' +
'</Process>';

var endToEndXML = '<?xml version="1.0" encoding="UTF-8"?>' +
'<Process>' +
'<Activities>' +
    '<Activity id="tache_START" defId="tache_START" name="开始" tacheId="start" tacheCode="start" areaId="end2end" isRunning="false" type="Start" subType="" isParallel="false" state=""/>' +
    '<Activity id="tache_P_CRM_01" defId="tache_P_CRM_01" name="订单受理" tacheId="crm01" tacheCode="crm01" areaId="end2end" isRunning="false" type="Tache" subType="" isParallel="false" state=""/>' +
    '<Activity id="tache_P_OSS_01" defId="tache_P_OSS_01" name="省编排收单" tacheId="oss01" tacheCode="oss01" areaId="end2end" isRunning="false" type="Tache" subType="" isParallel="false" state=""/>' +
    '<Activity id="tache_P_OSS_02" defId="tache_P_OSS_02" name="省编排拆单" tacheId="oss02" tacheCode="oss02" areaId="end2end" isRunning="false" type="Tache" subType="" isParallel="false" state=""/>' +
    '<Activity id="tache_P_OSS_03" defId="tache_P_OSS_03" name="省派发集团" tacheId="oss03" tacheCode="oss03" areaId="end2end" isRunning="false" type="Tache" subType="" isParallel="false" state=""/>' +
    '<Activity id="tache_P_OSS_04" defId="tache_P_OSS_04" name="省编排派发综调" tacheId="oss04" tacheCode="oss04" areaId="end2end" isRunning="false" type="Tache" subType="" isParallel="false" state=""/>' +
    '<Activity id="tache_P_ZD_01" defId="tache_P_ZD_01" name="省内装维" tacheId="zd01" tacheCode="zd01" areaId="end2end" isRunning="false" type="Tache" subType="" isParallel="false" state=""/>' +
    '<Activity id="tache_P_OSS_05" defId="tache_P_OSS_05" name="省内向CRM报竣" tacheId="oss05" tacheCode="oss05" areaId="end2end" isRunning="false" type="Tache" subType="" isParallel="false" state=""/>' +
    '<Activity id="tache_P_CRM_02" defId="tache_P_CRM_02" name="全程报竣" tacheId="crm02" tacheCode="crm02" areaId="end2end" isRunning="false" type="Tache" subType="" isParallel="false" state=""/>' +
    '<Activity id="tache_END" defId="tache_END" name="结束" tacheId="end" tacheCode="end" areaId="end2end" isRunning="false" type="Finish" subType="" isParallel="false" state=""/>' +
'</Activities>' +
'<Transitions>' +
    '<Transition id="t1" from="tache_START" to="tache_P_CRM_01"/>' +
    '<Transition id="t2" from="tache_P_CRM_01" to="tache_P_OSS_01"/>' +
    '<Transition id="t3" from="tache_P_OSS_01" to="tache_P_OSS_02"/>' +
    '<Transition id="t4" from="tache_P_OSS_02" to="tache_P_OSS_03"/>' +
    '<Transition id="t5" from="tache_P_OSS_03" to="tache_P_OSS_04"/>' +
    '<Transition id="t6" from="tache_P_OSS_04" to="tache_P_ZD_01"/>' +
    '<Transition id="t7" from="tache_P_ZD_01" to="tache_P_OSS_05"/>' +
    '<Transition id="t8" from="tache_P_OSS_05" to="tache_P_CRM_02"/>' +
    '<Transition id="t9" from="tache_P_CRM_02" to="tache_END"/>' +
'</Transitions>' +
'</Process>';

var groupXML = '<?xml version="1.0" encoding="UTF-8"?>' +
'<Process>' +
'<Activities>' +
    '<Activity id="groupNode_start" defId="groupNode_start" name="开始节点" tacheId="start" tacheCode="start" areaId="group" isRunning="false" type="Start" subType="" isParallel="false" state=""/>' +
    '<Activity id="groupNode_common_tacheStart" defId="groupNode_common_tacheStart" name="工单受理" tacheId="tacheStart" tacheCode="tacheStart" areaId="group" isRunning="false" type="Tache" subType="" isParallel="false" state=""/>' +
    '<Activity id="groupNode_common_tacheSend" defId="groupNode_common_tacheSend" name="业务开通" tacheId="tacheSend" tacheCode="tacheSend" areaId="group" isRunning="false" type="Tache" subType="" isParallel="false" state=""/>' +
    '<Activity id="groupNode_common_tacheRecv" defId="groupNode_common_tacheRecv" name="业务报竣" tacheId="tacheRecv" tacheCode="tacheRecv" areaId="group" isRunning="false" type="Tache" subType="" isParallel="false" state=""/>' +
    '<Activity id="groupNode_common_tacheEnd" defId="groupNode_common_tacheEnd" name="给省内回单" tacheId="tacheEnd" tacheCode="tacheEnd" areaId="group" isRunning="false" type="Tache" subType="" isParallel="false" state=""/>' +
    '<Activity id="groupNode_end" defId="groupNode_end" name="结束节点" tacheId="end" tacheCode="end" areaId="group" isRunning="false" type="Finish" subType="" isParallel="false" state=""/>' +
'</Activities>' +
'<Transitions>' +
    '<Transition id="t1" from="groupNode_start" to="groupNode_common_tacheStart"/>' +
    '<Transition id="t2" from="groupNode_common_tacheStart" to="groupNode_common_tacheSend"/>' +
    '<Transition id="t3" from="groupNode_common_tacheSend" to="groupNode_common_tacheRecv"/>' +
    '<Transition id="t4" from="groupNode_common_tacheRecv" to="groupNode_common_tacheEnd"/>' +
    '<Transition id="t5" from="groupNode_common_tacheEnd" to="groupNode_end"/>' +
'</Transitions>' +
'</Process>';

// zflow流程图实例
var customerPlanFlow, end2endFlow, groupFlow;
var initRetryCount = 0;
var maxInitRetries = 3;

// 初始化zflow流程图
function initZFlowDiagrams() {
    try {
        console.log('开始初始化zflow流程图... (尝试次数:', initRetryCount + 1, ')');
        
        // 检查重试次数
        if (initRetryCount >= maxInitRetries) {
            console.error('zflow初始化失败，已达到最大重试次数');
            return;
        }
        initRetryCount++;
        
        // 检查DOM元素是否存在
        var customerPlanElement = document.getElementById('customerPlanFlow');
        var end2endElement = document.getElementById('end2endFlow');
        var groupElement = document.getElementById('groupFlow');
        
        console.log('DOM元素检查:', {
            customerPlanElement: customerPlanElement,
            end2endElement: end2endElement,
            groupElement: groupElement
        });
        
        // 检查容器是否可见
        if (customerPlanElement) {
            console.log('客户规划流程容器尺寸:', {
                offsetWidth: customerPlanElement.offsetWidth,
                offsetHeight: customerPlanElement.offsetHeight,
                clientWidth: customerPlanElement.clientWidth,
                clientHeight: customerPlanElement.clientHeight
            });
        }
        
        if (!customerPlanElement || !end2endElement || !groupElement) {
            console.error('流程图容器元素未找到，延迟初始化');
            setTimeout(initZFlowDiagrams, 500);
            return;
        }
        
        // 检查jQuery、Raphael和zflow是否可用
        console.log('库检查:', {
            jQuery: typeof $,
            Raphael: typeof Raphael,
            zflow: typeof $.fn.zflow,
            zflowLoaded: window.zflowLoaded
        });
        
        // 更详细的检查
        if (typeof $ === 'undefined') {
            console.error('jQuery未加载，延迟初始化');
            setTimeout(initZFlowDiagrams, 500);
            return;
        }
        
        if (typeof Raphael === 'undefined') {
            console.error('Raphael未加载，延迟初始化');
            setTimeout(initZFlowDiagrams, 500);
            return;
        }
        
        // 检查zflow是否加载
        if (typeof $.fn.zflow === 'undefined' || window.zflowLoaded !== true) {
            console.error('zflow未加载，延迟初始化');
            console.log('$.fn内容:', Object.keys($.fn));
            setTimeout(initZFlowDiagrams, 1000);
            return;
        }
        
        console.log('开始初始化客户规划流程...');
        // 客户规划流程
        if (customerPlanFlow) {
            customerPlanFlow.paper.clear();
        }
        customerPlanFlow = $('#customerPlanFlow').zflow(customerPlanXML, {}, {
            iconPath: '../imgs/',
            direction: 'horizontal',
            autsize: true,
            offsetX: 280,
            offsetY: 50,
            nodeSpace: 230, // 节点间距
            branchSize: 120,
            imageSize: 24, // 流程图图标大小
            fontSize: 15,
            textSpace: 10,
            textLineWords: 20,
            textLayout: 'right'
        });
        console.log('客户规划流程初始化完成:', customerPlanFlow);

        console.log('开始初始化端到端流程图...');
        // 端到端流程图
        if (end2endFlow) {
            end2endFlow.paper.clear();
        }
        end2endFlow = $('#end2endFlow').zflow(endToEndXML, {}, {
            iconPath: '../imgs/',
            direction: 'horizontal',
            autsize: true,
            offsetX: 50,
            offsetY: 50,
            nodeSpace: 110,
            branchSize: 70,
            imageSize: 24,
            fontSize: 11,
            textSpace: 4,
            textLineWords: 20,
            textLayout: 'button'
        });
        console.log('端到端流程图初始化完成:', end2endFlow);

        console.log('开始初始化集团流程图...');
        // 集团流程图
        if (groupFlow) {
            groupFlow.paper.clear();
        }
        groupFlow = $('#groupFlow').zflow(groupXML, {}, {
            iconPath: '../imgs/',
            direction: 'horizontal',
            autsize: true,
            offsetX: 50,
            offsetY: 50,
            nodeSpace: 110,
            branchSize: 80,
            imageSize: 24,
            fontSize: 11,
            textSpace: 5,
            textLineWords: 20,
            textLayout: 'button'
        });
        console.log('集团流程图初始化完成:', groupFlow);
        
        // 验证初始化是否成功
        if (customerPlanFlow && customerPlanFlow.paper) {
            console.log('客户规划流程paper对象:', customerPlanFlow.paper);
        }
        if (end2endFlow && end2endFlow.paper) {
            console.log('端到端流程图paper对象:', end2endFlow.paper);
        }
        if (groupFlow && groupFlow.paper) {
            console.log('集团流程图paper对象:', groupFlow.paper);
        }
        
        // 重置重试计数
        initRetryCount = 0;
        console.log('zflow流程图初始化完成');
    } catch (error) {
        console.error('zflow流程图初始化失败:', error);
        // 如果初始化失败，延迟重试
        setTimeout(initZFlowDiagrams, 1000);
    }
}

// 更新zflow节点状态
function updateZFlowNodeState(flowInstance, nodeId, state) {
    if (!flowInstance || !flowInstance.activities || !flowInstance.activities[nodeId]) {
        return;
    }
    
    var activity = flowInstance.activities[nodeId];
    activity.state = state;
    
    // 重新绘制节点
    if (activity.node && activity.node.image) {
        var iconUrl = '../imgs/';
        switch(state) {
            case '10F':
                iconUrl += 'tache_finish.png';
                break;
            case '10I':
                iconUrl += 'tache_doing.png';
                break;
            case '10E':
                iconUrl += 'tache_error.png';
                break;
            case '10X':
            case '10C':
                iconUrl += 'tache_cancel.png';
                break;
            default:
                iconUrl += 'tache_init.png';
                break;
        }
        
        // 更新图标
        activity.node.image.attr('src', iconUrl);
        /** 
        // 更新节点颜色
        var color = '#95a5a6'; // 默认灰色
        switch(state) {
            case '10F':
                color = '#7185B7'; // 蓝色
                break;
            case '10I':
                color = '#2ecc71'; // 绿色
                break;
            case '10E':
                color = '#ff0000'; // 红色
                break;
            case '10X':
            case '10C':
                color = '#e67e22'; // 橙色
                break;
        }
        
        // 更新节点颜色
        activity.color = color;
        
        // 重新绘制节点
        activity.paint(flowInstance.paper);*/
    }
}

// 添加异常行高亮样式和查询中悬浮框样式
function addErrorRowStyles() {
    // 样式已经移到CSS文件中，此函数保留用于兼容性
    // 如果需要动态添加样式，可以在这里添加
}

// 显示查询中悬浮框
function showLoadingOverlay() {
    var overlay = document.getElementById('loading-overlay');
    if (!overlay) {
        // 创建悬浮框HTML
        var overlayHtml = `
            <div id="loading-overlay" class="loading-overlay">
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <p class="loading-text">查询中...</p>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', overlayHtml);
        overlay = document.getElementById('loading-overlay');
    }
    overlay.style.display = 'flex';
}

// 隐藏查询中悬浮框
function hideLoadingOverlay() {
    var overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}

// 动态获取基础URL
function getBaseUrl() {
    var protocol = window.location.protocol;
    var host = window.location.host;
    var contextPath = '/OC-CONTROL-CENTER';
    return protocol + '//' + host + contextPath;
}

// 兼容DOMContentLoaded
function addDomReadyListener(fn) {
    if (document.addEventListener) {
        document.addEventListener('DOMContentLoaded', fn, false);
    } else if (document.attachEvent) {
        document.attachEvent('onreadystatechange', function() {
            if (document.readyState === 'complete') fn();
        });
    }
}
// 兼容textContent/innerText，对于input元素设置value
function setTextContent(el, value) {
    console.log('setTextContent - 元素:', el, '标签名:', el.tagName, '值:', value);
    if (el.tagName === 'INPUT') {
        el.value = value;
        console.log('设置input的value为:', value);
    } else if ('textContent' in el) {
        el.textContent = value;
        console.log('设置textContent为:', value);
    } else {
        el.innerText = value;
        console.log('设置innerText为:', value);
    }
}

addDomReadyListener(function() {
    // 添加异常行高亮样式
    addErrorRowStyles();
    
    // 延迟初始化zflow流程图 - 确保DOM和依赖库完全加载
    setTimeout(function() {
        initZFlowDiagrams();
    }, 1000);
    
    // 处理字段名称显示逻辑
    function formatFieldLabel(label) {
        if (!label) return '';
        var labelStr = String(label);
        if (labelStr.length <= 6) {
            return labelStr;
        } else {
            return labelStr.substring(0, 5) + '...';
        }
    }
    
    // 初始化字段名称显示
    function initFieldLabels() {
        var labels = document.querySelectorAll('.info-item label');
        for (var i = 0; i < labels.length; i++) {
            var label = labels[i];
            var originalText = label.getAttribute('data-original') || label.textContent.replace(':', '');
            if (!label.getAttribute('data-original')) {
                label.setAttribute('data-original', originalText);
            }
            var formattedText = formatFieldLabel(originalText);
            label.textContent = formattedText;
            
            // 为所有字段名添加title属性用于悬浮显示完整内容
            label.title = originalText;
        }
    }
    
    // 页面加载时初始化字段名称
    initFieldLabels();
    
    // 工单信息
    function renderDetail(data) {
        if (!data) return;
        
        console.log('渲染工单信息数据:', data);
        
        // 处理新的API响应结构
        if (data.groupQueryRet && data.groupQueryRet.viewOrderInfo) {
            var viewOrderInfo = data.groupQueryRet.viewOrderInfo;
            console.log('使用新的API响应结构:', viewOrderInfo);
            // 更新工单信息字段
            var groupOrderNoEl = document.getElementById('groupOrderNo');
            var businessSceneEl = document.getElementById('businessScene');
            if (groupOrderNoEl) {
                var serialIdValue = viewOrderInfo.serialId || '';
                setTextContent(groupOrderNoEl, serialIdValue);
                if (serialIdValue) {
                    groupOrderNoEl.title = serialIdValue;
                }
            }
            if (businessSceneEl) {
                var businessSceneValue = viewOrderInfo.businessScenName || '';
                setTextContent(businessSceneEl, businessSceneValue);
                if (businessSceneValue) {
                    businessSceneEl.title = businessSceneValue;
                }
            }
        }
        else{
            console.log('使用原有的字段映射');
            // 原有的字段映射保持不变
            for (var key in data) {
                if (data.hasOwnProperty(key)) {
                    var el = document.getElementById(key);
                    if (el) {
                        console.log('设置字段:', key, '值:', data[key], '元素:', el);
                        setTextContent(el, data[key]);
                        el.title = data[key] == null ? '' : String(data[key]);
                    } else {
                        console.warn('未找到元素:', key);
                    }
                }
            }
        }
        
        // 处理字段名称显示
        var labels = document.querySelectorAll('.info-item label');
        for (var i = 0; i < labels.length; i++) {
            var label = labels[i];
            var originalText = label.getAttribute('data-original') || label.textContent.replace(':', '');
            if (!label.getAttribute('data-original')) {
                label.setAttribute('data-original', originalText);
            }
            var formattedText = formatFieldLabel(originalText);
            label.textContent = formattedText;
            
            // 为所有字段名添加title属性用于悬浮显示完整内容
            label.title = originalText;
        }
    }

//样例报文 流程图 调试开关 true-调试,false-接口调用
var terstState = false;
if(!terstState){

    // 从localStorage获取工单详情数据（跳转前页面放的）
    var detailStr = localStorage.getItem('orderDetailData');
    if (!detailStr) {
        console.error('没有找到工单数据，请从列表页面重新进入');
        return;
    }

    var orderDetailData;
    try {
        orderDetailData = JSON.parse(detailStr);
    } catch (e) {
        console.error('解析工单数据失败:', e);
        return;
    }
    renderDetail(orderDetailData);
    
    // 从URL参数获取工单信息
    var orderNo = orderDetailData.custOrderCode;
    var realCustOrderCode = orderDetailData.realCustOrderCode || '';
    var serialId = orderDetailData.serialId || '';
    var instanceNo = orderDetailData.instanceNo || '';
    var productCode = orderDetailData.productCode || '';
    var busiNo = orderDetailData.busiNo || '';
    var accessNo = orderDetailData.accessNo || '';
    var provinceId = orderDetailData.provinceId || '';
    var areaId = orderDetailData.areaId || '';
    var startTime = orderDetailData.startTime || '';
    var endTime = orderDetailData.endTime || '';
    
    if (!orderNo) {
        console.error('缺少必要的工单参数');
        return;
    }
    
    // 调用接口获取工单详情
    var xhr = window.XMLHttpRequest ? new XMLHttpRequest() : new ActiveXObject('Microsoft.XMLHTTP');
    xhr.open('POST', '/pdos/queryCenterOrderDetails', true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    
    // 显示查询中悬浮框
    showLoadingOverlay();
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            // 隐藏查询中悬浮框
            hideLoadingOverlay();
            
            if (xhr.status === 200) {
                var data;
                try {
                    data = JSON.parse(xhr.responseText);
                } catch (e) { 
                    console.error('解析响应数据失败:', e);
                    return; 
                }
                // 校验resultCode
                if (!data || data.resultCode !== '0') {
                    if (data && data.resultMsg) {
                        alert(data.resultMsg);
                    } else {
                        alert('接口返回异常');
                    }
                    return;
                }
                renderDetail(data);
                // 第一步：渲染集团流程图和流程列表
                if (data.groupQueryRet) {
                    updateGroupFlowStatus(data.groupQueryRet);
                    renderGroupProcessList(data.groupQueryRet);
                }
                // 第二步：渲染流程表格和端到端流程图
                if (data.systemInfo && typeof data.systemInfo.length === 'number') {
                    for (var i = 0; i < data.systemInfo.length; i++) {
                        renderFlowTable(data.systemInfo[i].systemType, data.systemInfo[i].tacheGroup || []);
                    }
                    updateFlowNodeStatus(data.systemInfo);
                }
                // 第三步：根据端到端流程图环节加载客户规划流程
                if (data.systemInfo && typeof data.systemInfo.length === 'number') {
                    updateCustomerPlanStatus();
                }
                if (window.console && window.console.log) window.console.log('详情数据', data);
            } else {
                // 请求失败时也隐藏加载提示
                console.error('请求失败，状态码:', xhr.status);
                alert('请求失败，请稍后重试');
            }
        }
    };
    xhr.send(JSON.stringify({
        serialId: serialId || '',
        prodInstId: instanceNo || '', //item.prodInstId
        productCode: productCode || '',
        custOrderCode: orderNo || '', //item.custOrderCode
        businessAccount: busiNo || '', //item.businessAccount
        accNum: accessNo || '', //item.accNum
        provinceId: provinceId || '',
        areaId: areaId || '',
        startTime: startTime || '',
        endTime: endTime || ''
    })); 

}else{

    var data = {
        "resultCode": "0",
        "resultMsg": "处理成功",
        "systemInfo": [
            {
                "systemType": "P_CRM",
                "tacheGroup": [
                    {
                        "number": "1",
                        "tacheName": "订单受理",
                        "tacheCode": "P_CRM_01",
                        "startTime": "yyyy-MM-dd:HH:mm:ss",
                        "endTime": "yyyy-MM-dd:HH:mm:ss",
                        "status": "3",
                        "errorMsg":"XXX"
                    },
                    {
                        "number": "2",
                        "tacheName": "全程报竣",
                        "tacheCode": "P_CRM_02",
                        "startTime": "yyyy-MM-dd:HH:mm:ss",
                        "endTime": "yyyy-MM-dd:HH:mm:ss",
                        "status": "1",
                        "errorMsg":"XXX"
                    }
                ]
            },
            {
                "systemType": "P_OSS",
                "tacheGroup": [
                    {
                        "number": "1",
                        "tacheName": "省编排收单",
                        "tacheCode": "P_OSS_01",
                        "startTime": "yyyy-MM-dd:HH:mm:ss",
                        "endTime": "yyyy-MM-dd:HH:mm:ss",
                        "status": "3",
                        "errorMsg":"XXX"
                    },
                    {
                        "number": "2",
                        "tacheName": "省编排拆分",
                        "tacheCode": "P_OSS_02",
                        "startTime": "yyyy-MM-dd:HH:mm:ss",
                        "endTime": "yyyy-MM-dd:HH:mm:ss",
                        "status": "2",
                        "errorMsg":"XXX"
                    },
                    {
                        "number": "3",
                        "tacheName": "省派发集团",
                        "tacheCode": "P_OSS_03",
                        "startTime": "yyyy-MM-dd:HH:mm:ss",
                        "endTime": "yyyy-MM-dd:HH:mm:ss",
                        "status": "1",
                        "errorMsg":"XXX"
                    },
                    {
                        "number": "4",
                        "tacheName": "省编排派发综调",
                        "tacheCode": "P_OSS_04",
                        "startTime": "yyyy-MM-dd:HH:mm:ss",
                        "endTime": "yyyy-MM-dd:HH:mm:ss",
                        "status": "0",
                        "errorMsg":"XXX"
                    },
                    {
                        "number": "5",
                        "tacheName": "省内向CRM报竣",
                        "tacheCode": "P_OSS_05",
                        "startTime": "yyyy-MM-dd:HH:mm:ss",
                        "endTime": "yyyy-MM-dd:HH:mm:ss",
                        "status": "3",
                        "errorMsg":"XXX"
                    }
                ]
            },
            {
                "systemType": "P_ZD",
                "tacheGroup": [
                    {
                        "number": "1",
                        "tacheName": "装维派单",
                        "tacheCode": "P_ZD_01",
                        "startTime": "yyyy-MM-dd:HH:mm:ss",
                        "endTime": "yyyy-MM-dd:HH:mm:ss",
                        "status": "3",
                        "errorMsg":"XXX",
                        "dealMan": "张三",
                        "dealPhone": "13XXX09",
                        "finishTime": "yyyy-MM-dd:HH:mm:ss",
                        "bookTime": "yyyy-MM-dd:HH:mm:ss",
                        "hurryMan": "李四",
                        "hurryTime": "yyyy-MM-dd:HH:mm:ss",
                        "changeBookMan": "王五",
                        "changeBookOptTime": "yyyy-MM-dd:HH:mm:ss",
                        "changeBookTime": "yyyy-MM-dd:HH:mm:ss",
                        "visitTime": "yyyy-MM-dd:HH:mm:ss",
                        "remark": "XXX"
                    },
                    {
                        "number": "2",
                        "tacheName": "预约上门",
                        "tacheCode": "P_ZD_02",
                        "startTime": "yyyy-MM-dd:HH:mm:ss",
                        "endTime": "yyyy-MM-dd:HH:mm:ss",
                        "status": "2",
                        "errorMsg":"XXX",
                        "dealMan": "张三",
                        "dealPhone": "13XXX09",
                        "finishTime": "yyyy-MM-dd:HH:mm:ss",
                        "bookTime": "yyyy-MM-dd:HH:mm:ss",
                        "hurryMan": "李四",
                        "hurryTime": "yyyy-MM-dd:HH:mm:ss",
                        "changeBookMan": "王五",
                        "changeBookOptTime": "yyyy-MM-dd:HH:mm:ss",
                        "changeBookTime": "yyyy-MM-dd:HH:mm:ss",
                        "visitTime": "yyyy-MM-dd:HH:mm:ss",
                        "remark": "XXX"
                    },
                    {
                        "number": "3",
                        "tacheName": "上门安装施工",
                        "tacheCode": "P_ZD_03",
                        "startTime": "yyyy-MM-dd:HH:mm:ss",
                        "endTime": "yyyy-MM-dd:HH:mm:ss",
                        "status": "1",
                        "errorMsg":"XXX",
                        "dealMan": "张三",
                        "dealPhone": "13XXX09",
                        "finishTime": "yyyy-MM-dd:HH:mm:ss",
                        "bookTime": "yyyy-MM-dd:HH:mm:ss",
                        "hurryMan": "李四",
                        "hurryTime": "yyyy-MM-dd:HH:mm:ss",
                        "changeBookMan": "王五",
                        "changeBookOptTime": "yyyy-MM-dd:HH:mm:ss",
                        "changeBookTime": "yyyy-MM-dd:HH:mm:ss",
                        "visitTime": "yyyy-MM-dd:HH:mm:ss",
                        "remark": "XXX"
                    },
                    {
                        "number": "4",
                        "tacheName": "装维竣工",
                        "tacheCode": "P_ZD_04",
                        "startTime": "yyyy-MM-dd:HH:mm:ss",
                        "endTime": "yyyy-MM-dd:HH:mm:ss",
                        "status": "0",
                        "errorMsg":"XXX",
                        "dealMan": "张三",
                        "dealPhone": "13XXX09",
                        "finishTime": "yyyy-MM-dd:HH:mm:ss",
                        "bookTime": "yyyy-MM-dd:HH:mm:ss",
                        "hurryMan": "李四",
                        "hurryTime": "yyyy-MM-dd:HH:mm:ss",
                        "changeBookMan": "王五",
                        "changeBookOptTime": "yyyy-MM-dd:HH:mm:ss",
                        "changeBookTime": "yyyy-MM-dd:HH:mm:ss",
                        "visitTime": "yyyy-MM-dd:HH:mm:ss",
                        "remark": "XXX"
                    }
                ]
            }
        ]
    };
    renderDetail(data);
    // 第一步：渲染集团流程图和流程列表
    if (data.groupQueryRet) {
        updateGroupFlowStatus(data.groupQueryRet);
        renderGroupProcessList(data.groupQueryRet);
    }
    // 第二步：渲染流程表格和端到端流程图
    if (data.systemInfo && typeof data.systemInfo.length === 'number') {
        for (var i = 0; i < data.systemInfo.length; i++) {
            renderFlowTable(data.systemInfo[i].systemType, data.systemInfo[i].tacheGroup || []);
        }
        updateFlowNodeStatus(data.systemInfo);
    }
    // 第三步：根据端到端流程图环节加载客户规划流程
    if (data.systemInfo && typeof data.systemInfo.length === 'number') {
        updateCustomerPlanStatus();
    }
    if (window.console && window.console.log) window.console.log('详情数据', data);

}

});

// 渲染流程表格
function renderFlowTable(systemType, tacheGroup) {
    var tableBody;
    if (systemType === 'P_CRM') {
        tableBody = document.getElementById('crmFlowTbody');
    } else if (systemType === 'P_OSS') {
        tableBody = document.getElementById('ossFlowTbody');
    } else if (systemType === 'P_ZD') {
        tableBody = document.getElementById('zdFlowTbody');
    }
    if (!tableBody) return;
    tableBody.innerHTML = '';
    
    // 状态字典映射
    var statusDict = {
        '0': '未开始',
        '1': '执行中',
        '2': '异常',
        '3': '已完成',
        '4': '已退单',
        '5': '已撤单'
    };
    
    // 获取状态显示文本和样式
    function getStatusDisplay(status) {
        var statusText = statusDict[status] || status || '';
        var isError = status === '2'; // 异常状态
        var style = isError ? 'style="color: red; font-weight: bold;"' : '';
        return '<span ' + style + '>' + statusText + '</span>';
    }
    
    if (tacheGroup && typeof tacheGroup.length === 'number') {
        for (var i = 0; i < tacheGroup.length; i++) {
            var item = tacheGroup[i];
            var tr = document.createElement('tr');
            
            // 判断整行是否需要高亮显示（异常状态）
            var isErrorRow = item.status === '2';
            var rowClass = isErrorRow ? 'class="error-row"' : '';
            
            tr.innerHTML =
                '<td title="' + (item.tacheName || '') + '">' + (item.tacheName || '') + '</td>' +
                '<td title="' + (item.startTime || '') + '">' + (item.startTime || '') + '</td>' +
                '<td title="' + (item.endTime || '') + '">' + (item.endTime || '') + '</td>' +
                '<td title="' + (item.status || '') + '">' + getStatusDisplay(item.status) + '</td>' +
                '<td title="' + (item.dealMan || '') + '">' + (item.dealMan || '') + '</td>' +
                '<td title="' + (item.dealPhone || '') + '">' + (item.dealPhone || '') + '</td>' +
                '<td title="' + (item.finishTime || '') + '">' + (item.finishTime || '') + '</td>' +
                '<td title="' + (item.bookTime || '') + '">' + (item.bookTime || '') + '</td>' +
                '<td title="' + (item.hurryMan || '') + '">' + (item.hurryMan || '') + '</td>' +
                '<td title="' + (item.hurryTime || '') + '">' + (item.hurryTime || '') + '</td>' +
                '<td title="' + (item.changeBookInfo || '') + '">' + (item.changeBookInfo || '') + '</td>' +
                '<td title="' + (item.changeBookMan || '') + '">' + (item.changeBookMan || '') + '</td>' +
                '<td title="' + (item.changeBookOptTime || '') + '">' + (item.changeBookOptTime || '') + '</td>' +
                '<td title="' + (item.changeBookTime || '') + '">' + (item.changeBookTime || '') + '</td>' +
                '<td title="' + (item.visitTime || '') + '">' + (item.visitTime || '') + '</td>' +
                '<td title="' + (item.remark || item.errorMsg || '') + '">' + (item.remark || item.errorMsg || '') + '</td>';
            
            if (rowClass) {
                tr.setAttribute('class', 'error-row');
            }
            
            tableBody.appendChild(tr);
        }
    }
    
    // 数据渲染后更新表格宽度
    setTimeout(function() {
        var section = tableBody.closest('.table-section');
        if (section) {
            var headerTable = section.querySelector('.table-header');
            var bodyTable = section.querySelector('.table-body');
            var headerCols = headerTable.querySelectorAll('col');
            if (headerTable && bodyTable && headerCols.length > 0) {
                updateTableWidth(headerTable, bodyTable, headerCols);
            }
        }
    }, 100);
}

function setNodeIcon(node, status) {
    var text = node.getAttribute('data-label') || node.textContent.replace(/^\s+|\s+$/g, '');
    var iconHtml = '';
    var iconBase = '../imgs/';
    if (status === 'error') {
        iconHtml = '<img src="' + iconBase + 'tache_error.png" class="node-icon" alt="异常" />';
    } else if (status === 'blue') {
        iconHtml = '<img src="' + iconBase + 'tache_finish.png" class="node-icon" alt="已完成" />';
    } else if (status === 'processing') {
        iconHtml = '<img src="' + iconBase + 'tache_doing.png" class="node-icon" alt="处理中" />';
    } else if (status === 'start') {
        iconHtml = '<img src="' + iconBase + 'node_start.png" class="node-icon" alt="开始节点" />';
    } else if (status === 'end') {
        iconHtml = '<img src="' + iconBase + 'node_finish.png" class="node-icon" alt="结束节点" />';
    } else {
        iconHtml = '<img src="' + iconBase + 'tache_init.png" class="node-icon" alt="未开始" />';
    }
    node.innerHTML = iconHtml + text;
}

function updateFlowNodeStatus(systemInfo) {
    if (!end2endFlow) {
        console.log('end2endFlow未初始化，延迟重试...');
        setTimeout(function() {
            updateFlowNodeStatus(systemInfo);
        }, 500);
        return;
    }
    
    // 合并所有tacheGroup
    var allTache = [];
    for (var i = 0; i < systemInfo.length; i++) {
        var sys = systemInfo[i];
        if (sys.tacheGroup && typeof sys.tacheGroup.length === 'number') {
            for (var j = 0; j < sys.tacheGroup.length; j++) {
                allTache.push(sys.tacheGroup[j]);
            }
        }
    }
    
    var statusMap = {
        '0': '',     //未开始
        '1': '10I',      // 执行中
        '2': '10E',   // 异常
        '3': '10F',      // 完成
        'start': '10F',
        'end': '10F'
    };
    
    // P_ZD_01特殊逻辑
    var pzd = null, zdTache = [];
    for (var i = 0; i < systemInfo.length; i++) {
        if (systemInfo[i].systemType === 'P_ZD') {
            pzd = systemInfo[i];
            break;
        }
    }
    if (pzd && pzd.tacheGroup && typeof pzd.tacheGroup.length === 'number') {
        zdTache = pzd.tacheGroup;
    }
    
    if (zdTache.length === 0) {
        updateZFlowNodeState(end2endFlow, 'tache_P_ZD_01', '');
    } else {
        var hasError = false, allDone = true, processing = false;
        for (var i = 0; i < zdTache.length; i++) {
            if (zdTache[i].status === '2') hasError = true;
            if (zdTache[i].status === '1') processing = true;
            if (zdTache[i].status !== '3') allDone = false;
        }
        if (hasError) {
            updateZFlowNodeState(end2endFlow, 'tache_P_ZD_01', '10E');
        } else if (allDone) {
            updateZFlowNodeState(end2endFlow, 'tache_P_ZD_01', '10F');
        } else if (processing) {
            updateZFlowNodeState(end2endFlow, 'tache_P_ZD_01', '10I');
        } else {
            updateZFlowNodeState(end2endFlow, 'tache_P_ZD_01', '');
        }
    }
    
    // P_OSS_04特殊逻辑：映射省调度流程列表状态
    if (zdTache.length === 0) {
        updateZFlowNodeState(end2endFlow, 'tache_P_OSS_04', '');
    } else {
        var hasError = false, allDone = true, processing = false;
        for (var i = 0; i < zdTache.length; i++) {
            if (zdTache[i].status === '2') hasError = true;
            if (zdTache[i].status === '1') processing = true;
            if (zdTache[i].status !== '3') allDone = false;
        }
        if (hasError) {
            updateZFlowNodeState(end2endFlow, 'tache_P_OSS_04', '10E');
        } else if (allDone) {
            updateZFlowNodeState(end2endFlow, 'tache_P_OSS_04', '10F');
        } else if (processing) {
            updateZFlowNodeState(end2endFlow, 'tache_P_OSS_04', '10I');
        } else {
            updateZFlowNodeState(end2endFlow, 'tache_P_OSS_04', '');
        }
    }
    
    // 集团流程图环节状态映射到省派发集团节点
    var groupNodeStatusList = [];
    if (window.lastGroupQueryRet && window.lastGroupQueryRet.viewStreamNodeVoList) {
        for (var i = 0; i < window.lastGroupQueryRet.viewStreamNodeVoList.length; i++) {
            var s = window.lastGroupQueryRet.viewStreamNodeVoList[i].nodeStatus;
            // 只统计业务环节（0/1/3/gray），忽略start和end
            if (s === 'start' || s === 'end') continue;
            if (s === '0') groupNodeStatusList.push('10E');
            else if (s === '1') groupNodeStatusList.push('10F');
            else if (s === '3') groupNodeStatusList.push('10I');
            else groupNodeStatusList.push('');
        }
    }
    
    if (groupNodeStatusList.length > 0) {
        var hasError = groupNodeStatusList.indexOf('10E') !== -1;
        var allDone = groupNodeStatusList.every(function(s){return s==='10F'});
        var allGray = groupNodeStatusList.every(function(s){return s===''});
        var status = '10I';
        if (hasError) status = '10E';
        else if (allDone) status = '10F';
        else if (allGray) status = '';
        updateZFlowNodeState(end2endFlow, 'tache_P_OSS_03', status);
    }
    
    // 其他节点常规映射（排除P_ZD_01、P_OSS_03和P_OSS_04）
    for (var i = 0; i < allTache.length; i++) {
        var item = allTache[i];
        if (!item.tacheCode || item.tacheCode === 'P_ZD_01' || item.tacheCode === 'P_OSS_03' || item.tacheCode === 'P_OSS_04') continue;
        var nodeId = 'tache_' + item.tacheCode;
        if (statusMap.hasOwnProperty(item.status)) {
            updateZFlowNodeState(end2endFlow, nodeId, statusMap[item.status]);
        }
    }
}

function updateCustomerPlanStatus() {
    if (!customerPlanFlow) {
        console.log('customerPlanFlow未初始化，延迟重试...');
        setTimeout(function() {
            updateCustomerPlanStatus();
        }, 500);
        return;
    }
    
    var mapping = {
        planNode_accept: ['P_CRM_01'],
        planNode_config: ['P_OSS_01', 'P_OSS_02', 'P_OSS_03', 'P_OSS_04'],
        planNode_install: ['P_ZD_01'],
        planNode_finish: ['P_OSS_05', 'P_CRM_02']
    };
    
    // 获取端到端流程图中节点的状态映射函数
    function getEnd2EndNodeStatus(tacheCode) {
        if (!end2endFlow || !end2endFlow.activities) return '';
        
        var nodeId = 'tache_' + tacheCode;
        var activity = end2endFlow.activities[nodeId];
        if (!activity) return '';
        
        return activity.state || '';
    }
    
    // 更新客户规划流程节点状态
    for (var planNodeId in mapping) {
        var tacheCodes = mapping[planNodeId];
        var hasError = false, allDone = true, processing = false, hasAny = false;
        
        for (var i = 0; i < tacheCodes.length; i++) {
            var status = getEnd2EndNodeStatus(tacheCodes[i]);
            if (status) hasAny = true;
            
            if (status === '10E') hasError = true;
            if (status === '10I') processing = true;
            if (status !== '10F') allDone = false;
        }
        
        var finalStatus = '';
        if (hasError) {
            finalStatus = '10E';
        } else if (allDone && hasAny) {
            finalStatus = '10F';
        } else if (processing) {
            finalStatus = '10I';
        }
        
        updateZFlowNodeState(customerPlanFlow, planNodeId, finalStatus);
    }
}

// 从环节编码中提取产品前缀
function getProductPrefix(nodeCode) {
    if (!nodeCode) return null;
    // 匹配产品前缀，提取下划线前的所有大写字母
    var match = nodeCode.match(/^([A-Z]+)_/);
    return match ? match[1] : null;
}

// 从环节编码中提取环节名称
function getTacheName(nodeCode) {
    if (!nodeCode) return '';
    // 提取下划线后的部分，如tacheStart、tacheSend等
    var parts = nodeCode.split('_');
    return parts.length > 1 ? parts.slice(1).join('_') : '';
}

function updateGroupFlowStatus(groupQueryRet) {
    if (!groupFlow) {
        setTimeout(function() {
            updateGroupFlowStatus(groupQueryRet);
        }, 500);
        return;
    }
    if (!groupQueryRet || !groupQueryRet.viewStreamNodeVoList) return;
    var streamNodes = groupQueryRet.viewStreamNodeVoList;
    var statusMap = {
        '1': '10F',           // 完成 → 已完成
        '0': '10E',           // 异常 → 异常
        'doing': '10I',       // 运行中 → 处理中
        'notExecuted': '',    // 未开始 → 未开始
        'start': '10F',
        'end': '10F'
    };
    for (var i = 0; i < streamNodes.length; i++) {
        var streamNode = streamNodes[i];
        var nodeCode = streamNode.nodeCode;
        var productPrefix = getProductPrefix(nodeCode);
        if (productPrefix) {
            var nodeId = 'groupNode_' + productPrefix + '_' + getTacheName(nodeCode);
            var commonNodeId = nodeId.replace(/^groupNode_[A-Z]+_/, 'groupNode_common_');
            if (statusMap.hasOwnProperty(streamNode.nodeStatus)) {
                updateZFlowNodeState(groupFlow, commonNodeId, statusMap[streamNode.nodeStatus]);
            }
        }
    }
    window.lastGroupQueryRet = groupQueryRet;
}

// 渲染集团编排流程列表
function renderGroupProcessList(groupQueryRet) {
    if (!groupQueryRet || !groupQueryRet.viewTacheProcessVoList) return;
    
    var tableBody = document.getElementById('groupProcessTbody');
    if (!tableBody) return;
    
    var processList = groupQueryRet.viewTacheProcessVoList;
    tableBody.innerHTML = '';
    
    for (var i = 0; i < processList.length; i++) {
        var item = processList[i];
        // 根据result字段判断是否为异常行
        var isError = item.result === '0';
        var tr = document.createElement('tr');
        
        if (isError) {
            tr.className = 'error-row';
        }
        
        tr.innerHTML = 
            '<td title="' + (item.linkName || '') + '">' + (item.linkName || '') + '</td>' +
            '<td title="' + (item.startTime || '') + '">' + (item.startTime || '') + '</td>' +
            '<td title="' + (item.endTime || '') + '">' + (item.endTime || '') + '</td>' +
            '<td title="' + (item.senderName || '') + '">' + (item.senderName || '') + '</td>' +
            '<td title="' + (item.receiverName || '') + '">' + (item.receiverName || '') + '</td>' +
            '<td title="' + (item.castTime || '') + '">' + (item.castTime || '') + '</td>' +
            '<td title="' + (item.tacheTypeName || '') + '">' + (item.tacheTypeName || '') + '</td>' +
            '<td title="' + (item.resultName || '') + '">' + (item.resultName || '') + '</td>' +
            '<td title="' + (item.linkUser || '') + '">' + (item.linkUser || '') + '</td>' +
            '<td title="' + (item.linkNumber || '') + '">' + (item.linkNumber || '') + '</td>' +
            '<td class="dispatch-msg" title="' + ((item.sendMsg || '').replace(/"/g, '&quot;')) + '">' + (item.sendMsg || '') + '</td>' +
            '<td class="response-msg" title="' + ((item.recvMsg || '').replace(/"/g, '&quot;')) + '">' + (item.recvMsg || '') + '</td>';
        
        tableBody.appendChild(tr);
    }
    
    // 数据渲染后更新表格宽度
    setTimeout(function() {
        var section = tableBody.closest('.table-section');
        if (section) {
            var headerTable = section.querySelector('.table-header');
            var bodyTable = section.querySelector('.table-body');
            var headerCols = headerTable.querySelectorAll('col');
            if (headerTable && bodyTable && headerCols.length > 0) {
                updateTableWidth(headerTable, bodyTable, headerCols);
            }
        }
    }, 100);
}

window.refreshOrderDetailData = function() {
    var detailStr = localStorage.getItem('orderDetailData');
    if (!detailStr) {
        console.error('没有找到工单数据，请重新加载页面');
        return;
    }
    var detail;
    try {
        detail = JSON.parse(detailStr);
    } catch (e) {
        console.error('解析工单数据失败:', e);
        return;
    }
    // 工单信息
    function renderDetail(data) {
        if (!data) return;
        
        // 处理新的API响应结构
        if (data.groupQueryRet && data.groupQueryRet.viewOrderInfo) {
            var viewOrderInfo = data.groupQueryRet.viewOrderInfo;
            // 更新工单信息字段
            var groupOrderNoEl = document.getElementById('groupOrderNo');
            var businessSceneEl = document.getElementById('businessScene');
            if (groupOrderNoEl) {
                var serialIdValue = viewOrderInfo.serialId || '';
                setTextContent(groupOrderNoEl, serialIdValue);
                if (serialIdValue) {
                    groupOrderNoEl.title = serialIdValue;
                }
            }
            if (businessSceneEl) {
                var businessSceneValue = viewOrderInfo.businessScenName || '';
                setTextContent(businessSceneEl, businessSceneValue);
                if (businessSceneValue) {
                    businessSceneEl.title = businessSceneValue;
                }
            }
        }
        
        // 原有的字段映射保持不变
        for (var key in data) {
            if (data.hasOwnProperty(key)) {
                var el = document.getElementById(key);
                if (el) {
                    var value = data[key] == null ? '' : String(data[key]);
                    setTextContent(el, value);
                    // 为字段值添加悬浮提示
                    if (value && value.length > 0) {
                        el.title = value;
                    }
                }
            }
        }
    }
    
    // 从localStorage中获取的参数
    var orderNo = detail.custOrderCode;
    var serialId = detail.serialId || '';
    var instanceNo = detail.instanceNo || '';
    var productCode = detail.productCode || '';
    var busiNo = detail.busiNo || '';
    var accessNo = detail.accessNo || '';
    var provinceId = detail.provinceId || '';
    var areaId = detail.areaId || '';
    var startTime = detail.startTime || '';
    var endTime = detail.endTime || '';

    var xhr = window.XMLHttpRequest ? new XMLHttpRequest() : new ActiveXObject('Microsoft.XMLHTTP');
    xhr.open('POST', '/pdos/queryCenterOrderDetails', true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    
    // 显示查询中悬浮框
    showLoadingOverlay();
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            // 隐藏查询中悬浮框
            hideLoadingOverlay();
            
            if (xhr.status === 200) {
                var data;
                try {
                    data = JSON.parse(xhr.responseText);
                } catch (e) { 
                    console.error('解析响应数据失败:', e);
                    return; 
                }
                // 校验resultCode
                if (!data || data.resultCode !== '0') {
                    if (data && data.resultMsg) {
                        alert(data.resultMsg);
                    } else {
                        alert('接口返回异常');
                    }
                    return;
                }
                renderDetail(data);
                // 第一步：渲染集团流程图和流程列表
                if (data.groupQueryRet) {
                    updateGroupFlowStatus(data.groupQueryRet);
                    renderGroupProcessList(data.groupQueryRet);
                }
                // 第二步：渲染流程表格和端到端流程图
                if (data.systemInfo && typeof data.systemInfo.length === 'number') {
                    for (var i = 0; i < data.systemInfo.length; i++) {
                        renderFlowTable(data.systemInfo[i].systemType, data.systemInfo[i].tacheGroup || []);
                    }
                    updateFlowNodeStatus(data.systemInfo);
                }
                // 第三步：根据端到端流程图环节加载客户规划流程
                if (data.systemInfo && typeof data.systemInfo.length === 'number') {
                    updateCustomerPlanStatus();
                }
                if (window.console && window.console.log) window.console.log('详情数据', data);
            } else {
                // 请求失败时也隐藏加载提示
                console.error('请求失败，状态码:', xhr.status);
                alert('请求失败，请稍后重试');
            }
        }
    };
    xhr.send(JSON.stringify({
        serialId: serialId || '',
        prodInstId: instanceNo || '',
        productCode: productCode || '',
        custOrderCode: orderNo || '',
        businessAccount: busiNo || '',
        accNum: accessNo || '',
        provinceId: provinceId || '',
        areaId: areaId || '',
        startTime: startTime || '',
        endTime: endTime || ''
    }));
};

// 更新表格总宽度
function updateTableWidth(headerTable, bodyTable, headerCols) {
    var totalWidth = 0;
    headerCols.forEach(function(col) {
        totalWidth += parseInt(col.style.width) || 120;
    });

    // 设置表格最小宽度
    headerTable.style.minWidth = totalWidth + 'px';
    bodyTable.style.minWidth = totalWidth + 'px';
}

// 绑定刷新按钮事件
addDomReadyListener(function() {
    var refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        refreshBtn.onclick = function() {
            if (typeof window.refreshOrderDetailData === 'function') {
                window.refreshOrderDetailData();
            } else {
                console.error('refreshOrderDetailData function not found');
            }
        };
    }
    
    // 初始化表格列宽调整功能
    function initTableResize() {
        var tableSections = document.querySelectorAll('.table-section');
        
        tableSections.forEach(function(section) {
            var headerTable = section.querySelector('.table-header');
            var bodyTable = section.querySelector('.table-body');
            var headerCols = headerTable.querySelectorAll('col');
            var bodyCols = bodyTable.querySelectorAll('col');
            var resizers = headerTable.querySelectorAll('.col-resize');
            var headerWrapper = section.querySelector('.table-header-wrapper');
            var bodyWrapper = section.querySelector('.table-body-wrapper');
            
            // 同步表头和表体的滚动
            if (headerWrapper && bodyWrapper) {
                headerWrapper.addEventListener('scroll', function() {
                    bodyWrapper.scrollLeft = headerWrapper.scrollLeft;
                });
                
                bodyWrapper.addEventListener('scroll', function() {
                    headerWrapper.scrollLeft = bodyWrapper.scrollLeft;
                });
            }
            
            resizers.forEach(function(resizer, idx) {
                resizer.addEventListener('mousedown', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    document.body.style.cursor = 'col-resize';
                    document.body.style.userSelect = 'none';
                    
                    var startX = e.clientX;
                    var startWidth = parseInt(headerCols[idx].style.width) || 120;
                    
                    function onMouseMove(e2) {
                        e2.preventDefault();
                        var deltaX = e2.clientX - startX;
                        var newWidth = startWidth + deltaX;
                        
                        // 限制最小和最大宽度
                        newWidth = Math.max(80, Math.min(400, newWidth));
                        
                        // 设置新的宽度
                        headerCols[idx].style.width = newWidth + 'px';
                        bodyCols[idx].style.width = newWidth + 'px';
                        
                        // 更新表格总宽度
                        updateTableWidth(headerTable, bodyTable, headerCols);
                    }
                    
                    function onMouseUp() {
                        document.body.style.cursor = '';
                        document.body.style.userSelect = '';
                        document.removeEventListener('mousemove', onMouseMove);
                        document.removeEventListener('mouseup', onMouseUp);
                    }
                    
                    document.addEventListener('mousemove', onMouseMove);
                    document.addEventListener('mouseup', onMouseUp);
                });
            });
            

            // 初始化表格宽度
            updateTableWidth(headerTable, bodyTable, headerCols);
        });
    }
    
    // 页面加载完成后初始化表格功能
    setTimeout(function() {
        initTableResize();
    }, 100);
});