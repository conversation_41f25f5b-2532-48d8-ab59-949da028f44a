// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock HTMLCanvasElement.getContext
HTMLCanvasElement.prototype.getContext = jest.fn();

// Mock echarts
jest.mock('echarts', () => ({
  init: jest.fn(() => ({
    setOption: jest.fn(),
    resize: jest.fn(),
    dispose: jest.fn(),
    on: jest.fn(),
    off: jest.fn(),
  })),
  dispose: jest.fn(),
  registerTheme: jest.fn(),
}));

// Mock echarts-for-react
jest.mock('echarts-for-react', () => {
  return {
    __esModule: true,
    default: ({ option, ...props }: any) => {
      return <div data-testid="echarts-mock" {...props} />;
    },
  };
});

// Global test utilities
global.testUtils = {
  // 创建模拟的API响应
  createMockApiResponse: (data: any, code = 200, message = '成功') => ({
    code,
    message,
    data,
    timestamp: new Date().toISOString(),
  }),

  // 创建模拟的分页数据
  createMockPageData: (items: any[], pageNum = 1, pageSize = 10) => ({
    data: items,
    pageNum,
    pageSize,
    total: items.length,
    totalPages: Math.ceil(items.length / pageSize),
    hasNext: pageNum * pageSize < items.length,
    hasPrevious: pageNum > 1,
    isFirst: pageNum === 1,
    isLast: pageNum * pageSize >= items.length,
  }),

  // 等待异步操作完成
  waitForAsync: () => new Promise(resolve => setTimeout(resolve, 0)),
};

// 声明全局类型
declare global {
  var testUtils: {
    createMockApiResponse: (data: any, code?: number, message?: string) => any;
    createMockPageData: (items: any[], pageNum?: number, pageSize?: number) => any;
    waitForAsync: () => Promise<void>;
  };
}
