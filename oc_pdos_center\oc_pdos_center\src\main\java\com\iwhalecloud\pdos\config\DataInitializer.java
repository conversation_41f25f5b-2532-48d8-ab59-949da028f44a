package com.iwhalecloud.pdos.config;

import com.iwhalecloud.pdos.entity.Order;
import com.iwhalecloud.pdos.entity.ProcessStep;
import com.iwhalecloud.pdos.entity.Product;
import com.iwhalecloud.pdos.repository.OrderRepository;
import com.iwhalecloud.pdos.repository.ProcessStepRepository;
import com.iwhalecloud.pdos.repository.ProductRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * 数据初始化器
 * 在应用启动时初始化测试数据
 */
@Component
@Slf4j
public class DataInitializer implements CommandLineRunner {
    
    @Autowired
    private ProductRepository productRepository;
    
    @Autowired
    private OrderRepository orderRepository;
    
    @Autowired
    private ProcessStepRepository processStepRepository;
    
    @Override
    public void run(String... args) throws Exception {
        try {
            // 检查是否已有数据，避免重复初始化
            if (productRepository.count() > 0) {
                log.info("数据已存在，跳过初始化");
                return;
            }
            
            log.info("开始初始化测试数据...");
            
            // 初始化产品数据
            initProducts();
            
            // 初始化订单数据
            initOrders();
            
            // 初始化流程步骤数据
            initProcessSteps();
            
            log.info("测试数据初始化完成");
        } catch (Exception e) {
            log.error("数据初始化失败", e);
        }
    }
    
    private void initProducts() {
        List<Product> products = Arrays.asList(
            createProduct("CLOUD_PC_PUBLIC", "云电脑公众版", "云计算", "面向个人用户的云桌面服务"),
            createProduct("CLOUD_EYE", "天翼云眼", "AI服务", "智能视频监控分析服务"),
            createProduct("SMART_HOME", "智慧家庭", "物联网", "家庭智能化解决方案"),
            createProduct("ENTERPRISE_CLOUD", "企业云", "云计算", "企业级云服务平台"),
            createProduct("5G_PACKAGE", "5G套餐", "通信服务", "5G网络通信套餐服务"),
            createProduct("IOT_PLATFORM", "物联网平台", "物联网", "物联网设备管理平台"),
            createProduct("AI_ASSISTANT", "AI助手", "AI服务", "智能语音助手服务"),
            createProduct("SECURITY_SERVICE", "安全服务", "网络安全", "企业网络安全防护服务")
        );
        
        productRepository.saveAll(products);
        log.info("初始化了 {} 个产品", products.size());
    }
    
    private Product createProduct(String code, String name, String type, String description) {
        Product product = new Product();
        product.setProductCode(code);
        product.setProductName(name);
        product.setProductType(type);
        product.setStatus(1);
        product.setDescription(description);
        product.setCreateTime(LocalDateTime.now());
        product.setUpdateTime(LocalDateTime.now());
        product.setCreateBy("system");
        product.setUpdateBy("system");
        return product;
    }
    
    private void initOrders() {
        List<Product> products = productRepository.findAll();
        Random random = new Random();
        
        String[] provinces = {"北京", "上海", "广东", "江苏", "浙江", "山东", "河南", "四川", "湖北", "湖南"};
        String[] provinceCodes = {"BJ", "SH", "GD", "JS", "ZJ", "SD", "HN", "SC", "HB", "HN2"};
        String[] businessTypes = {"新装", "续费", "升级", "退订", "变更"};
        String[] statuses = {"待处理", "处理中", "已完成", "已取消", "异常"};
        String[] currentSteps = {"订单审核", "资源分配", "服务开通", "测试验收", "交付完成"};
        
        for (int i = 1; i <= 50; i++) {
            Product product = products.get(random.nextInt(products.size()));
            int provinceIndex = random.nextInt(provinces.length);
            
            Order order = new Order();
            order.setOrderNo("ORD" + String.format("%08d", i));
            order.setCustOrderNo("CUST" + String.format("%08d", i));
            order.setProductId(product.getId());
            order.setProductName(product.getProductName());
            order.setCustomerId("CUST" + String.format("%06d", random.nextInt(1000) + 1));
            order.setCustomerName("客户" + (random.nextInt(1000) + 1));
            order.setOrderStatus(random.nextInt(5)); // 0-4的状态值
            order.setOrderAmount(new BigDecimal(random.nextInt(10000) + 100));
            order.setProvinceName(provinces[provinceIndex]);
            order.setProvinceCode(provinceCodes[provinceIndex]);
            // order.setCity(provinces[provinceIndex] + "市"); // Order实体中没有city字段
            order.setBusinessType(businessTypes[random.nextInt(businessTypes.length)]);
            order.setProcessInstanceId("PROC" + String.format("%08d", i));
            order.setCurrentStep(currentSteps[random.nextInt(currentSteps.length)]);
            order.setDescription("订单描述信息" + i);
            order.setCreateTime(LocalDateTime.now().minusDays(random.nextInt(30)));
            order.setUpdateTime(LocalDateTime.now().minusHours(random.nextInt(24)));
            order.setCreateBy("system");
            order.setUpdateBy("system");
            
            orderRepository.save(order);
        }
        
        log.info("初始化了 50 个订单");
    }
    
    private void initProcessSteps() {
        List<Order> orders = orderRepository.findAll();
        Random random = new Random();
        
        String[] stepCodes = {"ORDER_AUDIT", "RESOURCE_ALLOC", "SERVICE_OPEN", "TEST_ACCEPT", "DELIVERY"};
        String[] stepNames = {"订单审核", "资源分配", "服务开通", "测试验收", "交付完成"};
        String[] statuses = {"PENDING", "PROCESSING", "COMPLETED", "EXCEPTION"};
        String[] processors = {"张三", "李四", "王五", "赵六", "钱七"};
        String[] depts = {"订单中心", "资源中心", "开通中心", "测试中心", "交付中心"};
        
        for (Order order : orders) {
            int stepCount = random.nextInt(3) + 2; // 每个订单2-4个步骤
            
            for (int i = 0; i < stepCount; i++) {
                ProcessStep step = new ProcessStep();
                step.setOrderId(order.getId());
                step.setOrderNo(order.getOrderNo());
                step.setStepCode(stepCodes[i]);
                step.setStepName(stepNames[i]);
                step.setStepOrder(i + 1);
                
                // 前面的步骤设为已完成，最后一个设为处理中
                if (i < stepCount - 1) {
                    step.setStepStatus(2); // 已完成
                } else {
                    step.setStepStatus(random.nextBoolean() ? 1 : 2); // 处理中或已完成
                }
                
                step.setHandler(processors[random.nextInt(processors.length)]);
                step.setHandlerDept(depts[i]);
                step.setRemark("步骤" + (i + 1) + "处理说明");
                step.setCreateTime(order.getCreateTime().plusHours(i * 2 + random.nextInt(4)));
                
                processStepRepository.save(step);
            }
        }
        
        log.info("初始化了流程步骤数据");
    }
}