# PDOS战新可视化系统 - 自测指南

## 📋 测试概述

本文档提供了PDOS战新可视化系统的完整自测方案，包括后端单元测试、集成测试、前端组件测试和API接口测试。

## 🏗️ 测试架构

```
PDOS测试体系
├── 后端测试
│   ├── 单元测试 (Unit Tests)
│   │   ├── Controller层测试
│   │   ├── Service层测试
│   │   └── Repository层测试
│   └── 集成测试 (Integration Tests)
│       ├── API端到端测试
│       ├── 数据库集成测试
│       └── 业务流程测试
├── 前端测试
│   ├── 组件测试 (Component Tests)
│   ├── 服务测试 (Service Tests)
│   └── 页面集成测试
└── API接口测试
    ├── 功能测试
    ├── 性能测试
    └── 错误处理测试
```

## 🚀 快速开始

### 一键运行所有测试

```bash
# Windows环境
.\run-tests.bat

# 或者手动运行各项测试
```

### 环境要求

- **Java**: JDK 17+
- **Maven**: 3.6+
- **Node.js**: 16+
- **npm**: 8+

## 📊 测试详情

### 1. 后端测试

#### 1.1 单元测试

**位置**: `src/test/java/`

**运行命令**:
```bash
# 运行所有单元测试
mvn test

# 运行特定测试类
mvn test -Dtest=PdosCenterOrderControllerTest

# 运行特定测试方法
mvn test -Dtest=PdosCenterOrderControllerTest#testQueryCenterOrderList_Success
```

**测试覆盖**:
- ✅ `PdosCenterOrderControllerTest` - 控制器层测试
  - 订单列表查询功能
  - 订单详情查询功能
  - 参数校验测试
  - 异常处理测试
- ✅ `PdosCenterOrderServiceTest` - 服务层测试
  - 业务逻辑测试
  - 数据处理测试
  - 边界条件测试

#### 1.2 集成测试

**位置**: `src/test/java/com/iwhalecloud/pdos/integration/`

**运行命令**:
```bash
# 运行集成测试
mvn test -Dtest=*IntegrationTest
```

**测试覆盖**:
- ✅ `PdosIntegrationTest` - 完整API流程测试
  - 端到端业务流程
  - 数据库集成
  - 跨域配置测试
  - 性能测试

### 2. 前端测试

#### 2.1 组件测试

**位置**: `pdos-frontend/src/pages/__tests__/`

**运行命令**:
```bash
cd pdos-frontend

# 运行所有测试
npm test

# 运行测试并生成覆盖率报告
npm test -- --coverage

# 监听模式运行测试
npm test -- --watch
```

**测试覆盖**:
- ✅ `OrderList.test.tsx` - 订单列表页面测试
  - 组件渲染测试
  - 用户交互测试
  - 数据加载测试
  - 错误处理测试

#### 2.2 服务测试

**位置**: `pdos-frontend/src/services/__tests__/`

**测试覆盖**:
- ✅ `orderService.test.ts` - 订单服务测试
  - API调用测试
  - 参数转换测试
  - 错误处理测试

### 3. API接口测试

#### 3.1 自动化API测试

**位置**: `api-tests/`

**运行命令**:
```bash
cd api-tests

# 安装依赖
npm install

# 运行API测试
npm test

# 详细模式运行
npm run test:verbose
```

**测试覆盖**:
- ✅ 服务器连通性测试
- ✅ 产品列表API测试
- ✅ 订单查询API测试
- ✅ 订单详情API测试
- ✅ 分页功能测试
- ✅ 参数校验测试
- ✅ 错误处理测试
- ✅ 并发请求测试
- ✅ 响应时间测试

## 📈 测试报告

### 后端测试报告

测试完成后，报告生成在以下位置：
- **Surefire报告**: `target/surefire-reports/`
- **覆盖率报告**: `target/site/jacoco/` (需配置JaCoCo插件)

### 前端测试报告

测试完成后，报告生成在以下位置：
- **Jest报告**: 控制台输出
- **覆盖率报告**: `pdos-frontend/coverage/`

### API测试报告

API测试结果直接在控制台输出，包括：
- 测试用例执行状态
- 响应时间统计
- 错误详情
- 成功率统计

## 🔧 测试配置

### 后端测试配置

**测试配置文件**: `src/test/resources/application-test.yml`

主要配置：
- 使用H2内存数据库
- 启用SQL日志
- 随机端口避免冲突

**测试数据**: `src/test/resources/test-data.sql`

### 前端测试配置

**Jest配置**: `pdos-frontend/src/setupTests.ts`

主要配置：
- Mock浏览器API
- Mock第三方组件
- 全局测试工具

## 🐛 常见问题

### 1. 后端测试问题

**问题**: 测试时数据库连接失败
```
解决方案:
1. 检查H2数据库依赖是否正确
2. 确认test profile配置正确
3. 检查测试数据SQL语法
```

**问题**: Mock对象注入失败
```
解决方案:
1. 检查@MockBean注解使用
2. 确认Spring Boot Test配置
3. 验证依赖注入配置
```

### 2. 前端测试问题

**问题**: 组件渲染失败
```
解决方案:
1. 检查测试环境Mock配置
2. 确认组件依赖是否正确Mock
3. 检查React Testing Library版本兼容性
```

**问题**: 异步测试超时
```
解决方案:
1. 使用waitFor等待异步操作
2. 增加测试超时时间
3. 检查Promise处理逻辑
```

### 3. API测试问题

**问题**: 连接后端服务失败
```
解决方案:
1. 确认后端服务已启动
2. 检查端口配置(默认8888)
3. 验证网络连接
```

**问题**: 测试数据不匹配
```
解决方案:
1. 检查测试数据是否已初始化
2. 确认API返回格式
3. 更新测试期望值
```

## 📝 测试最佳实践

### 1. 编写测试的原则

- **单一职责**: 每个测试只验证一个功能点
- **独立性**: 测试之间不应相互依赖
- **可重复**: 测试结果应该是确定的
- **快速执行**: 单元测试应该快速完成
- **清晰命名**: 测试名称应该清楚表达测试意图

### 2. 测试数据管理

- 使用独立的测试数据库
- 每次测试前清理数据
- 使用工厂模式创建测试数据
- 避免硬编码测试数据

### 3. Mock使用指南

- 对外部依赖进行Mock
- 保持Mock的简单性
- 验证Mock的调用
- 避免过度Mock

## 🔄 持续集成

### CI/CD集成建议

```yaml
# GitHub Actions示例
name: PDOS Tests
on: [push, pull_request]
jobs:
  backend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up JDK 17
        uses: actions/setup-java@v2
        with:
          java-version: '17'
      - name: Run backend tests
        run: mvn test
        
  frontend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
      - name: Install dependencies
        run: cd pdos-frontend && npm install
      - name: Run frontend tests
        run: cd pdos-frontend && npm test -- --coverage
```

## 📞 支持

如果在测试过程中遇到问题，请：

1. 查看本文档的常见问题部分
2. 检查测试日志和错误信息
3. 联系开发团队获取支持

---

**最后更新**: 2024年1月
**维护团队**: PDOS开发团队