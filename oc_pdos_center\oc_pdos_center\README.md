# PDOS战新可视化系统

## 项目简介

PDOS（Product Delivery and Operation Support）战新可视化系统是一个用于订单管理和流程可视化的Web应用系统。系统采用前后端分离架构，提供订单查询、详情查看、流程跟踪等功能。

## 技术栈

### 后端
- **框架**: Spring Boot 2.x
- **数据库**: SQLite
- **ORM**: Spring Data JPA + Hibernate
- **构建工具**: Maven
- **Java版本**: JDK 8+

### 前端
- **框架**: React 18 + TypeScript
- **UI组件库**: Ant Design
- **构建工具**: Create React App
- **包管理器**: npm

## 项目结构

```
oc_pdos_center/
├── src/main/java/                 # 后端Java源码
│   └── com/example/pdos/
│       ├── controller/            # 控制器层
│       ├── service/               # 服务层
│       ├── repository/            # 数据访问层
│       └── entity/                # 实体类
├── src/main/resources/            # 后端资源文件
│   ├── application.yml            # 应用配置
│   ├── data.sql                   # 数据库初始化脚本
│   └── static/                    # 静态资源
├── pdos-frontend/                 # 前端项目
│   ├── src/
│   │   ├── pages/                 # 页面组件
│   │   ├── services/              # API服务
│   │   └── types/                 # TypeScript类型定义
│   └── package.json
├── start-all.bat                  # 一键启动所有服务
├── start-backend.bat              # 后端启动脚本
├── start-frontend.bat             # 前端启动脚本
├── quick-start.bat                # 快速启动菜单
├── check-status.bat               # 系统状态检查
├── pom.xml                        # Maven配置
└── pdos_center.db                 # SQLite数据库文件
```

## 快速开始

### 环境要求

- JDK 8 或更高版本
- Maven 3.6+
- Node.js 14+ 和 npm

### 启动方式

#### 方式一：快速启动（推荐）

1. **一键启动所有服务**
   ```bash
   # Windows - 启动后端和前端
   start-all.bat
   ```
   
2. **交互式启动菜单**
   ```bash
   # Windows - 提供启动选项菜单
   quick-start.bat
   ```
   
   菜单选项：
   - 启动所有服务（后端+前端）
   - 仅启动后端
   - 仅启动前端
   - 停止所有服务
   
3. **系统状态检查**
   ```bash
   # Windows - 检查环境和服务状态
   check-status.bat
   ```

#### 方式二：单独启动服务

1. **启动后端服务**
   ```bash
   # Windows
   start-backend.bat
   
   # 或手动执行
   mvn spring-boot:run
   ```
   
   后端服务将在 `http://localhost:8888` 启动

2. **启动前端服务**
   ```bash
   # Windows
   start-frontend.bat
   
   # 或手动执行
   cd pdos-frontend
   npm install  # 首次运行需要安装依赖
   npm start
   ```
   
   前端服务将在 `http://localhost:3002` 启动

#### 方式二：手动启动

1. **后端启动**
   ```bash
   # 编译项目
   mvn clean compile
   
   # 启动应用
   mvn spring-boot:run
   ```

2. **前端启动**
   ```bash
   cd pdos-frontend
   
   # 安装依赖（首次运行）
   npm install
   
   # 启动开发服务器
   npm start
   ```

### 访问应用

- **前端界面**: http://localhost:3002
- **后端API**: http://localhost:8888/api

## 启动脚本功能说明

### start-all.bat - 一键启动所有服务
- 自动检查系统环境（Java、Maven、Node.js、npm）
- 检查端口占用情况（8888、3002）
- 自动安装前端依赖（如果需要）
- 编译后端项目
- 按顺序启动后端和前端服务
- 提供健康检查和启动状态反馈

### quick-start.bat - 交互式启动菜单
- 提供友好的菜单界面
- 支持选择性启动服务
- 一键停止所有服务功能
- 自动进程管理

### check-status.bat - 系统状态检查
- 检查开发环境是否完整
- 显示端口占用状态
- 验证项目文件完整性
- 检查依赖安装状态
- 服务健康检查

### 启动脚本特性
- **UTF-8编码支持**: 正确显示中文字符
- **端口冲突检测**: 启动前检查端口是否被占用
- **环境验证**: 自动检查必需的开发工具
- **错误处理**: 详细的错误信息和解决建议
- **进度反馈**: 实时显示启动进度和状态
- **健康检查**: 验证服务是否正常启动

## 数据库配置

### SQLite数据库

系统使用SQLite作为数据库，配置简单，无需额外安装。

**配置文件**: `src/main/resources/application.yml`

```yaml
spring:
  datasource:
    url: **************************
    driver-class-name: org.sqlite.JDBC
  jpa:
    hibernate:
      ddl-auto: update  # 自动创建/更新表结构
    show-sql: true      # 显示SQL语句
  sql:
    init:
      mode: always      # 总是执行初始化脚本
```

### 数据库初始化

系统启动时会自动执行 `src/main/resources/data.sql` 脚本，初始化以下数据：

- **产品数据**: 天翼云眼、云电脑公众版、智慧家庭、企业云、5G套餐
- **订单数据**: 5个测试订单，包含不同状态和流程阶段
- **流程步骤数据**: 订单对应的流程步骤和状态

### 数据表结构

- `t_product`: 产品信息表
- `t_order`: 订单信息表
- `t_process_step`: 流程步骤表

## API接口

### 订单相关接口

- `POST /api/pdos/queryCenterOrderList` - 查询订单列表
- `POST /api/pdos/queryCenterOrderDetails` - 查询订单详情

### 请求示例

```javascript
// 查询订单列表
POST /api/pdos/queryCenterOrderList
{
  "pageNum": 1,
  "pageSize": 10,
  "orderNo": "ORD000000050",
  "customerName": "张三"
}

// 查询订单详情
POST /api/pdos/queryCenterOrderDetails
{
  "orderNo": "ORD000000050"
}
```

## 功能特性

### 🎯 核心功能

#### 订单管理
- **智能查询**: 支持订单号、客户名称、订单状态等多维度筛选
- **分页展示**: 高效的分页查询，支持大数据量订单展示
- **实时状态**: 订单状态实时更新，支持状态变更通知
- **批量操作**: 支持批量查询和状态更新操作
- **数据导出**: 支持订单数据导出为Excel格式

#### 流程可视化
- **端到端流程**: 完整展示从订单受理到完成的全流程
- **实时进度**: 动态显示当前流程节点和完成进度
- **状态追踪**: 详细记录每个流程节点的处理时间和处理人
- **异常监控**: 自动识别流程异常和超时节点
- **流程回溯**: 支持查看历史流程变更记录

#### 数据可视化
- **响应式设计**: 适配不同屏幕尺寸，支持移动端访问
- **图表展示**: 使用ECharts展示订单统计和趋势分析
- **状态标识**: 直观的颜色标识和图标显示不同状态
- **进度条**: 可视化流程完成进度
- **详情弹窗**: 模态框展示详细信息，避免页面跳转

### 🚀 技术特性

#### 性能优化
- **懒加载**: 组件和数据按需加载，提升页面响应速度
- **缓存机制**: 智能缓存常用数据，减少API调用
- **分页优化**: 后端分页查询，避免大数据量传输
- **SQL优化**: 使用索引和查询优化，提升数据库性能

#### 用户体验
- **加载状态**: 友好的加载动画和进度提示
- **错误处理**: 完善的错误提示和异常处理机制
- **操作反馈**: 及时的操作成功/失败反馈
- **快捷操作**: 支持键盘快捷键和批量操作

#### 系统集成
- **API标准化**: RESTful API设计，易于集成
- **数据同步**: 支持与外部系统的数据同步
- **权限控制**: 基于角色的访问控制（预留接口）
- **日志审计**: 完整的操作日志记录和审计功能

### 📊 业务特性

#### 战新业务支持
- **多产品类型**: 支持天翼云眼、云电脑、智慧家庭等战新产品
- **省市联动**: 支持省级和地市级订单管理
- **流程标准化**: 标准化的业务流程模板
- **KPI监控**: 关键业务指标监控和报表

#### 运营支持
- **实时监控**: 订单处理效率和异常监控
- **统计分析**: 多维度数据统计和趋势分析
- **报表生成**: 自动生成日报、周报、月报
- **预警机制**: 异常订单和超时流程预警

## 开发说明

### 前端开发

- 使用TypeScript进行类型安全开发
- 组件化设计，便于维护和扩展
- 使用Ant Design组件库，保证UI一致性

### 后端开发

- 遵循RESTful API设计规范
- 使用Spring Boot自动配置
- JPA实体映射，简化数据库操作

### 代码规范

- 前端使用ESLint和Prettier进行代码格式化
- 后端遵循Java编码规范
- 统一的错误处理和日志记录

## 故障排除

### 常见问题

1. **端口冲突**
   - 后端默认端口：8888
   - 前端默认端口：3002
   - 如有冲突，请修改配置文件中的端口设置

2. **数据库连接问题**
   - 确保SQLite驱动已正确加载
   - 检查数据库文件权限

3. **跨域问题**
   - 已在后端配置CORS，允许前端访问
   - 如有问题，检查application.yml中的跨域配置

### 日志查看

- 后端日志：控制台输出
- 前端日志：浏览器开发者工具Console

## 版本信息

- **当前版本**: 1.0.0
- **最后更新**: 2024年1月

## 联系方式

如有问题或建议，请联系开发团队。