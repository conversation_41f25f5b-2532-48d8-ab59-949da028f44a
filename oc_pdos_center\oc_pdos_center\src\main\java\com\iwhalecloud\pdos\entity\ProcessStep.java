package com.iwhalecloud.pdos.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 流程步骤实体类
 * 记录订单处理的各个环节
 */
@Entity
@Table(name = "t_process_step")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProcessStep {
    
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;
    
    /**
     * 订单ID
     */
    @Column(name = "order_id", nullable = false)
    private Long orderId;
    
    /**
     * 订单号
     */
    @Column(name = "order_no", nullable = false, length = 50)
    private String orderNo;
    
    /**
     * 步骤编码
     */
    @Column(name = "step_code", nullable = false, length = 50)
    private String stepCode;
    
    /**
     * 步骤名称
     */
    @Column(name = "step_name", nullable = false, length = 100)
    private String stepName;
    
    /**
     * 步骤序号
     */
    @Column(name = "step_order", nullable = false)
    private Integer stepOrder;
    
    /**
     * 步骤状态：0-未开始，1-进行中，2-已完成，3-已跳过，4-异常
     */
    @Column(name = "step_status", nullable = false)
    private Integer stepStatus = 0;
    
    /**
     * 步骤类型：PROVINCE_CRM-省CRM，PROVINCE_ORCHESTRATION-省编排，GROUP_ORCHESTRATION-集团编排，PROVINCE_DISPATCH-省调度
     */
    @Column(name = "step_type", length = 50)
    private String stepType;
    
    /**
     * 处理人
     */
    @Column(name = "handler", length = 50)
    private String handler;
    
    /**
     * 处理部门
     */
    @Column(name = "handler_dept", length = 100)
    private String handlerDept;
    
    /**
     * 开始时间
     */
    @Column(name = "start_time")
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @Column(name = "end_time")
    private LocalDateTime endTime;
    
    /**
     * 处理时长（分钟）
     */
    @Column(name = "duration_minutes")
    private Long durationMinutes;
    
    /**
     * 步骤结果
     */
    @Column(name = "step_result", length = 50)
    private String stepResult;
    
    /**
     * 处理备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
    
    /**
     * 异常信息
     */
    @Column(name = "error_message", length = 1000)
    private String errorMessage;
    
    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    @Column(name = "create_by", length = 50)
    private String createBy;
    
    /**
     * 更新人
     */
    @Column(name = "update_by", length = 50)
    private String updateBy;
}