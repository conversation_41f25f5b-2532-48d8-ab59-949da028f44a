import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Descriptions,
  Timeline,
  Button,
  Space,
  Tag,
  Spin,
  message,
  Row,
  Col,
  Divider,
  Steps,
  Table,
  Alert,
} from 'antd';
import { ArrowLeftOutlined, ReloadOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { orderApi } from '../services/api';
import { OrderDetail as OrderDetailType, CustomerPlanStep, EndToEndStep, ProcessListItem } from '../types';
import dayjs from 'dayjs';

const { Step } = Steps;

const OrderDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [orderDetail, setOrderDetail] = useState<OrderDetailType | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // 订单状态映射
  const getOrderStatusTag = (status: string | number) => {
    const statusMap: { [key: string]: { text: string; color: string } } = {
      '0': { text: '待处理', color: 'default' },
      '1': { text: '处理中', color: 'processing' },
      '2': { text: '已完成', color: 'success' },
      '3': { text: '已取消', color: 'error' },
      '4': { text: '异常', color: 'error' },
      '待处理': { text: '待处理', color: 'default' },
      '处理中': { text: '处理中', color: 'processing' },
      '已完成': { text: '已完成', color: 'success' },
      '已取消': { text: '已取消', color: 'error' },
      '异常': { text: '异常', color: 'error' },
    };
    const statusInfo = statusMap[String(status)] || { text: '未知', color: 'default' };
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
  };

  // 步骤状态映射
  const getStepStatusTag = (status: string) => {
    const statusMap: { [key: string]: { text: string; color: string } } = {
      'PENDING': { text: '未开始', color: 'default' },
      'PROCESSING': { text: '执行中', color: 'processing' },
      'COMPLETED': { text: '已完成', color: 'success' },
      'EXCEPTION': { text: '异常', color: 'error' },
    };
    const statusInfo = statusMap[status] || { text: '未知', color: 'default' };
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
  };

  // 获取步骤状态对应的颜色（用于流程图）
  const getStepStatusColor = (status: string) => {
    const colorMap: { [key: string]: string } = {
      'PENDING': '#d9d9d9', // 灰色
      'PROCESSING': '#52c41a', // 绿色
      'COMPLETED': '#1890ff', // 蓝色
      'EXCEPTION': '#ff4d4f', // 红色
    };
    return colorMap[status] || '#d9d9d9';
  };

  // 获取订单详情
  const fetchOrderDetail = async (showLoading = true) => {
    if (!id) {
      message.error('订单ID不能为空');
      console.error('OrderDetail: 订单ID为空');
      return;
    }

    console.log('OrderDetail: 开始获取订单详情, orderNo =', id);

    try {
      if (showLoading) {
        setLoading(true);
      } else {
        setRefreshing(true);
      }
      
      // 首先获取基本订单信息
      const response = await orderApi.getOrderDetail(id);
      console.log('OrderDetail: API响应:', response);
      
      if (response.code === 0) {
        console.log('OrderDetail: 获取订单详情成功:', response.data);
        const basicOrderDetail = response.data;
        
        // 判断是否为战新业务产品，如果是则调用新接口获取详细环节信息
        const isNewBusinessProduct = basicOrderDetail?.order?.productId && 
          ['PROD001', 'PROD002', 'PROD003', 'PROD004'].includes(String(basicOrderDetail.order.productId));
        
        if (isNewBusinessProduct) {
          // 调用新业务省内段环节信息查询接口（4.122）
          try {
            const detailResponse = await orderApi.getProvinceOrderDetails({
              productCode: String(basicOrderDetail.order.productId),
              businessNumber: basicOrderDetail.order.custOrderNo || '',
              orderNumber: basicOrderDetail.order.orderNo,
              accessNumber: '', // 从订单信息中提取或留空
              productInstanceId: '', // 从订单信息中提取或留空
              provinceCode: basicOrderDetail.order.provinceCode || '',
              cityCode: basicOrderDetail.order.cityCode || '',
              startTime: dayjs(basicOrderDetail.order.createTime).format('YYYY-MM-DD'),
              endTime: dayjs().format('YYYY-MM-DD'),
            });
            
            // 合并基本信息和详细环节信息
            const enhancedOrderDetail = {
              ...basicOrderDetail,
              provinceSteps: detailResponse.data.provinceSteps || [],
              groupVisualizationData: detailResponse.data.groupVisualizationData || {},
              endToEndSteps: detailResponse.data.groupVisualizationData?.endToEndProcess || basicOrderDetail.endToEndSteps,
              customerPlanSteps: detailResponse.data.groupVisualizationData?.customerPlanProcess || basicOrderDetail.customerPlanSteps,
              processListItems: detailResponse.data.groupVisualizationData?.processList || basicOrderDetail.processListItems,
              groupedProcessSteps: detailResponse.data.groupVisualizationData?.groupedProcessSteps || basicOrderDetail.groupedProcessSteps,
              statistics: detailResponse.data.groupVisualizationData?.statistics || {},
            };
            
            setOrderDetail(enhancedOrderDetail);
          } catch (detailError) {
            console.warn('获取详细环节信息失败，使用基本信息:', detailError);
            setOrderDetail(basicOrderDetail);
          }
        } else {
          setOrderDetail(basicOrderDetail);
        }
      } else {
        console.error('OrderDetail: API返回错误:', response);
        message.error(response.message || '获取订单详情失败');
      }
    } catch (error) {
      console.error('OrderDetail: 获取订单详情失败:', error);
      message.error('获取订单详情失败: ' + (error as Error).message);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // 刷新环节信息
  const refreshStepInfo = async () => {
    await fetchOrderDetail(false);
    message.success('环节信息已刷新');
  };

  useEffect(() => {
    fetchOrderDetail();
  }, [id]);

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!orderDetail) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <p>订单详情不存在</p>
        <Button onClick={() => navigate('/orders')}>返回订单列表</Button>
      </div>
    );
  }

  const { order, steps, product, customerPlanSteps, endToEndSteps, processListItems, groupedProcessSteps, completedSteps, totalSteps, progress } = orderDetail;

  // 客户规划流程步骤配置
  const defaultCustomerPlanSteps: CustomerPlanStep[] = [
    {
      stepName: '受理',
      stepStatus: 'COMPLETED',
      relatedEndToEndSteps: ['订单受理']
    },
    {
      stepName: '电信应急建采及云网配置',
      stepStatus: 'PROCESSING',
      relatedEndToEndSteps: ['省编排收单', '省编排拆单', '省派发集团', '集团编排开通', '省编排派发综调']
    },
    {
      stepName: '预约上门及外线安装',
      stepStatus: 'PENDING',
      relatedEndToEndSteps: ['省内装维']
    },
    {
      stepName: '报竣',
      stepStatus: 'PENDING',
      relatedEndToEndSteps: ['省内向CRM报竣', '全程报竣']
    }
  ];

  // 端到端流程步骤配置
  const defaultEndToEndSteps: EndToEndStep[] = [
    { stepCode: 'ORDER_ACCEPT', stepName: '订单受理', stepStatus: 'COMPLETED', stepType: 'PROVINCE_CRM' },
    { stepCode: 'PROVINCE_RECEIVE', stepName: '省编排收单', stepStatus: 'COMPLETED', stepType: 'PROVINCE_ORCHESTRATION' },
    { stepCode: 'PROVINCE_SPLIT', stepName: '省编排拆单', stepStatus: 'PROCESSING', stepType: 'PROVINCE_ORCHESTRATION' },
    { stepCode: 'SEND_TO_GROUP', stepName: '省派发集团', stepStatus: 'PENDING', stepType: 'PROVINCE_ORCHESTRATION' },
    { stepCode: 'GROUP_ORCHESTRATION', stepName: '集团编排开通', stepStatus: 'PENDING', stepType: 'GROUP_ORCHESTRATION' },
    { stepCode: 'SEND_TO_DISPATCH', stepName: '省编排派发综调', stepStatus: 'PENDING', stepType: 'PROVINCE_ORCHESTRATION' },
    { stepCode: 'PROVINCE_INSTALL', stepName: '省内装维', stepStatus: 'PENDING', stepType: 'PROVINCE_DISPATCH' },
    { stepCode: 'REPORT_TO_CRM', stepName: '省内向CRM报竣', stepStatus: 'PENDING', stepType: 'PROVINCE_CRM' },
    { stepCode: 'FINAL_REPORT', stepName: '全程报竣', stepStatus: 'PENDING', stepType: 'PROVINCE_CRM' }
  ];

  // 流程列表表格列定义
  const processListColumns: ColumnsType<any> = [
    {
      title: '环节名称',
      dataIndex: 'stepName',
      key: 'stepName',
      width: 150,
    },
    {
      title: '环节状态',
      dataIndex: 'stepStatus',
      key: 'stepStatus',
      width: 100,
      render: (status: string | number) => {
        // 处理数字状态码
        if (typeof status === 'number') {
          switch (status) {
            case 0: return <Tag color="default">等待处理</Tag>;
            case 1: return <Tag color="processing">处理中</Tag>;
            case 2: return <Tag color="success">已完成</Tag>;
            case 3: return <Tag color="warning">已暂停</Tag>;
            case 4: return <Tag color="error">异常</Tag>;
            default: return <Tag>{status}</Tag>;
          }
        }
        // 处理字符串状态
        return getStepStatusTag(status);
      },
    },
    {
      title: '处理人',
      dataIndex: 'processor',
      key: 'processor',
      width: 120,
      render: (text: string) => text || '-',
    },
    {
      title: '处理部门',
      dataIndex: 'processorDept',
      key: 'processorDept',
      width: 150,
      render: (text: string) => text || '-',
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 150,
      render: (time: string) => time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      key: 'endTime',
      width: 150,
      render: (time: string) => time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '处理时长(分钟)',
      dataIndex: 'durationMinutes',
      key: 'durationMinutes',
      width: 120,
      render: (duration: number) => duration || '-',
    },
    {
      title: '备注',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      render: (text: string) => text || '-',
    },
  ];

  // 使用后端返回的分组流程步骤数据，如果没有则使用空数组
  const groupedProcessList = {
    provinceCrm: groupedProcessSteps?.PROVINCE_CRM || [],
    provinceOrchestration: groupedProcessSteps?.PROVINCE_ORCHESTRATION || [],
    groupOrchestration: groupedProcessSteps?.GROUP_ORCHESTRATION || [],
    provinceDispatch: groupedProcessSteps?.PROVINCE_DISPATCH || [],
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/orders')}
          >
            返回列表
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={refreshStepInfo}
            loading={refreshing}
          >
            刷新
          </Button>
        </Space>
      </div>

      <Row gutter={[24, 24]}>
        {/* 工单信息 */}
        <Col span={24}>
          <Card title="工单信息" bordered={false}>
            <Descriptions column={3} bordered>
              <Descriptions.Item label="产品名称">{order.productName}</Descriptions.Item>
              <Descriptions.Item label="订单流水号">{order.orderNo}</Descriptions.Item>
              <Descriptions.Item label="客户订单号">{order.custOrderNo || '-'}</Descriptions.Item>
              <Descriptions.Item label="客户名称">{order.customerName || '-'}</Descriptions.Item>
              <Descriptions.Item label="客户ID">{order.customerId || '-'}</Descriptions.Item>
              <Descriptions.Item label="订单状态">{getOrderStatusTag(order.orderStatus)}</Descriptions.Item>
              <Descriptions.Item label="订单金额">{order.orderAmount ? `¥${order.orderAmount.toFixed(2)}` : '-'}</Descriptions.Item>
              <Descriptions.Item label="省份">{order.provinceName || '-'}</Descriptions.Item>
              <Descriptions.Item label="地市">{order.cityName || '-'}</Descriptions.Item>
              <Descriptions.Item label="业务类型">{order.businessType || '-'}</Descriptions.Item>
              <Descriptions.Item label="流程实例ID">{order.processInstanceId || '-'}</Descriptions.Item>
              <Descriptions.Item label="当前环节">{order.currentStep || '-'}</Descriptions.Item>
              <Descriptions.Item label="创建时间">{order.createTime ? dayjs(order.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</Descriptions.Item>
              <Descriptions.Item label="更新时间">{order.updateTime ? dayjs(order.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</Descriptions.Item>
              <Descriptions.Item label="完成时间">{order.completeTime ? dayjs(order.completeTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</Descriptions.Item>
              <Descriptions.Item label="订单描述" span={3}>{order.description || '-'}</Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {/* 客户规划流程 */}
        <Col span={24}>
          <Card title="客户规划流程" bordered={false}>
            <Steps current={1} status="process">
              {(customerPlanSteps || defaultCustomerPlanSteps).map((step, index) => (
                <Step
                  key={index}
                  title={step.stepName}
                  status={
                    step.stepStatus === 'COMPLETED' ? 'finish' :
                    step.stepStatus === 'PROCESSING' ? 'process' :
                    step.stepStatus === 'EXCEPTION' ? 'error' : 'wait'
                  }
                  description={getStepStatusTag(step.stepStatus)}
                />
              ))}
            </Steps>
          </Card>
        </Col>

        {/* 端到端流程图 */}
        <Col span={24}>
          <Card title="端到端流程图" bordered={false}>
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px', justifyContent: 'center' }}>
              {(endToEndSteps || defaultEndToEndSteps).map((step, index) => (
                <div key={step.stepCode} style={{ display: 'flex', alignItems: 'center' }}>
                  <div
                    style={{
                      padding: '12px 16px',
                      backgroundColor: getStepStatusColor(step.stepStatus),
                      color: step.stepStatus === 'PENDING' ? '#666' : '#fff',
                      borderRadius: '6px',
                      textAlign: 'center',
                      minWidth: '120px',
                      fontSize: '12px',
                      fontWeight: 'bold',
                    }}
                  >
                    {step.stepName}
                  </div>
                  {index < (endToEndSteps || defaultEndToEndSteps).length - 1 && (
                    <div style={{ margin: '0 8px', color: '#ccc' }}>→</div>
                  )}
                </div>
              ))}
            </div>
          </Card>
        </Col>

        {/* 流程列表 */}
        <Col span={24}>
          <Card 
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>流程列表</span>
                <Button 
                  icon={<ReloadOutlined />} 
                  size="small" 
                  onClick={refreshStepInfo}
                  loading={refreshing}
                >
                  刷新
                </Button>
              </div>
            } 
            bordered={false}
          >
            {/* 省CRM流程列表 */}
            <div style={{ marginBottom: '24px' }}>
              <h4>省CRM流程列表</h4>
              <Table
                columns={processListColumns}
                dataSource={groupedProcessList.provinceCrm}
                rowKey="id"
                pagination={false}
                size="small"
                locale={{ emptyText: '暂无省CRM流程数据' }}
              />
            </div>

            {/* 省编排流程列表 */}
            <div style={{ marginBottom: '24px' }}>
              <h4>省编排流程列表</h4>
              <Table
                columns={processListColumns}
                dataSource={groupedProcessList.provinceOrchestration}
                rowKey="id"
                pagination={false}
                size="small"
                locale={{ emptyText: '暂无省编排流程数据' }}
              />
            </div>

            {/* 集团编排流程列表 */}
            <div style={{ marginBottom: '24px' }}>
              <h4>集团编排流程列表</h4>
              <Table
                columns={processListColumns}
                dataSource={groupedProcessList.groupOrchestration}
                rowKey="id"
                pagination={false}
                size="small"
                locale={{ emptyText: '暂无集团编排流程数据' }}
              />
            </div>

            {/* 省调度流程列表 */}
            <div>
              <h4>省调度流程列表</h4>
              <Table
                columns={processListColumns}
                dataSource={groupedProcessList.provinceDispatch}
                rowKey="id"
                pagination={false}
                size="small"
                locale={{ emptyText: '暂无省调度流程数据' }}
              />
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default OrderDetail;