package com.iwhalecloud.pdos.service;

import com.iwhalecloud.pdos.dto.PageResult;
import com.iwhalecloud.pdos.entity.Order;
import org.springframework.data.domain.Page;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * PDOS中心订单服务接口
 */
public interface PdosCenterOrderService {
    
    /**
     * 查询订单列表（原有方法，保持兼容性）
     */
    PageResult<Order> queryCenterOrderList(
        String orderNo, String custOrderNo, String productName, String customerName,
        Integer orderStatus, String provinceCode, String businessType,
        LocalDateTime startTime, LocalDateTime endTime, int pageNum, int pageSize);
    
    /**
     * 查询订单详情（原有方法，保持兼容性）
     */
    Map<String, Object> queryCenterOrderDetails(String orderNo, Long orderId);
    
    /**
     * 4.121 新业务省内段订单列表信息查询接口
     * 按照接口规范实现省内段订单列表查询
     */
    Page<Order> queryProvinceOrderList(String productCode, String businessNumber, 
                                      String orderNumber, String accessNumber, 
                                      String productInstanceId, String provinceCode, 
                                      String cityCode, String startTime, String endTime, 
                                      int pageNum, int pageSize);
    
    /**
     * 4.122 新业务省内段环节信息查询接口
     * 按照接口规范实现省内段环节信息查询
     */
    Map<String, Object> queryProvinceOrderDetails(String productCode, String businessNumber, 
                                                 String orderNumber, String accessNumber, 
                                                 String productInstanceId, String provinceCode, 
                                                 String cityCode, String startTime, String endTime);
    
    /**
     * 查询集团战新可视化PG库数据接口
     * 通过入参查询集团编排系统可视化数据库匹配未输入的参数信息
     */
    Map<String, Object> queryGroupVisualizationData(String productCode, String businessNumber, 
                                                   String orderNumber, String accessNumber, 
                                                   String productInstanceId, String provinceCode, 
                                                   String cityCode, String startTime, String endTime);
    
    /**
     * 获取统计数据
     */
    Map<String, Object> getStatistics();
    
    /**
     * 创建订单
     */
    Order createOrder(Order order);
    
    /**
     * 更新订单状态
     */
    void updateOrderStatus(Long orderId, Integer status);
}