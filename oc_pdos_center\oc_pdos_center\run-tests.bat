@echo off
echo ========================================
echo PDOS系统自测脚本
echo ========================================
echo.

:: 设置颜色
color 0A

:: 检查Java环境
echo 🔍 检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java环境未安装或配置不正确
    pause
    exit /b 1
)
echo ✅ Java环境检查通过

:: 检查Node.js环境
echo 🔍 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js环境未安装或配置不正确
    pause
    exit /b 1
)
echo ✅ Node.js环境检查通过

:: 检查Maven环境
echo 🔍 检查Maven环境...
mvn --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Maven环境未安装或配置不正确
    pause
    exit /b 1
)
echo ✅ Maven环境检查通过

echo.
echo ========================================
echo 开始运行测试
echo ========================================
echo.

:: 1. 后端单元测试
echo 🧪 运行后端单元测试...
echo ----------------------------------------
mvn test -Dtest=*Test
if %errorlevel% neq 0 (
    echo ❌ 后端单元测试失败
    set TEST_FAILED=1
) else (
    echo ✅ 后端单元测试通过
)

echo.

:: 2. 后端集成测试
echo 🧪 运行后端集成测试...
echo ----------------------------------------
mvn test -Dtest=*IntegrationTest
if %errorlevel% neq 0 (
    echo ❌ 后端集成测试失败
    set TEST_FAILED=1
) else (
    echo ✅ 后端集成测试通过
)

echo.

:: 3. 前端测试
echo 🧪 运行前端测试...
echo ----------------------------------------
cd pdos-frontend
npm test -- --coverage --watchAll=false
if %errorlevel% neq 0 (
    echo ❌ 前端测试失败
    set TEST_FAILED=1
) else (
    echo ✅ 前端测试通过
)
cd ..

echo.

:: 4. API接口测试（需要先启动后端服务）
echo 🧪 准备API接口测试...
echo ----------------------------------------
echo 请确保后端服务已启动在端口8888
echo 按任意键继续API测试，或按Ctrl+C取消...
pause >nul

cd api-tests
echo 安装API测试依赖...
npm install
echo 运行API接口测试...
npm test
if %errorlevel% neq 0 (
    echo ❌ API接口测试失败
    set TEST_FAILED=1
) else (
    echo ✅ API接口测试通过
)
cd ..

echo.
echo ========================================
echo 测试结果汇总
echo ========================================

if defined TEST_FAILED (
    echo ❌ 部分测试失败，请检查上述输出
    echo 📋 测试报告位置：
    echo    - 后端测试报告: target/surefire-reports/
    echo    - 前端测试报告: pdos-frontend/coverage/
    echo    - API测试日志: 控制台输出
) else (
    echo ✅ 所有测试通过！
    echo 🎉 系统功能正常
)

echo.
echo 按任意键退出...
pause >nul